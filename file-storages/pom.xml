<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<groupId>io.tapdata</groupId>
	<modelVersion>4.0.0</modelVersion>
    <artifactId>file-storages</artifactId>
    <name>file-storages</name>
    <packaging>pom</packaging>
	<version>1.0-SNAPSHOT</version>
    <modules>
		<module>file-core</module>
		<module>local-file</module>
        <module>ftp-file</module>
        <module>smb-file</module>
		<module>sftp-file</module>
        <module>nfs-file</module>
        <module>s3fs-file</module>
		<module>oss-file</module>
	</modules>
	<properties>
		<connector.file.name>${project.artifactId}-v${project.version}</connector.file.name>
		<java.version>8</java.version>
		<tapdata.pdk.runner.verison>2.0-SNAPSHOT</tapdata.pdk.runner.verison>
		<tapdata.pdk.api.verison>2.0.0-SNAPSHOT</tapdata.pdk.api.verison>
		<tapdata.pdk.connector.core.version>1.0-SNAPSHOT</tapdata.pdk.connector.core.version>
		<junit.jupiter.version>5.8.1</junit.jupiter.version>
		<junit.platform.version>1.8.1</junit.platform.version>
		<commons.lang3.version>3.12.0</commons.lang3.version>
		<commons.collections4.version>4.4</commons.collections4.version>
		<log4j.version>2.17.1</log4j.version>
	</properties>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>tapdata-api</artifactId>
				<version>${tapdata.pdk.api.verison}</version>
			</dependency>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>tapdata-pdk-api</artifactId>
				<version>${tapdata.pdk.api.verison}</version>
			</dependency>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>tapdata-pdk-runner</artifactId>
				<version>${tapdata.pdk.runner.verison}</version>
			</dependency>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>connector-core</artifactId>
				<version>${tapdata.pdk.connector.core.version}</version>
			</dependency>
			<dependency>
				<groupId>org.junit.jupiter</groupId>
				<artifactId>junit-jupiter</artifactId>
				<version>${junit.jupiter.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.junit.jupiter</groupId>
				<artifactId>junit-jupiter-engine</artifactId>
				<version>${junit.jupiter.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.junit.jupiter</groupId>
				<artifactId>junit-jupiter-api</artifactId>
				<version>${junit.jupiter.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.junit.jupiter</groupId>
				<artifactId>junit-jupiter-params</artifactId>
				<version>${junit.jupiter.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.junit.platform</groupId>
				<artifactId>junit-platform-suite</artifactId>
				<version>${junit.platform.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.junit.platform</groupId>
				<artifactId>junit-platform-launcher</artifactId>
				<version>${junit.platform.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-lang3</artifactId>
				<version>${commons.lang3.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-collections4</artifactId>
				<version>${commons.collections4.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>4.2.1</version>
                <extensions>true</extensions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>

        </plugins>
    </build>
	<repositories>
		<repository>
			<id>tapdata-tapdata-maven</id>
			<name>maven</name>
			<url>https://tapdata-maven.pkg.coding.net/repository/tapdata/maven/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>
</project>

