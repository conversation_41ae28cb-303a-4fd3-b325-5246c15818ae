package com.tapdata.tm.inspect.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.extra.cglib.CglibUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.mongodb.client.result.UpdateResult;
import com.tapdata.tm.Settings.constant.CategoryEnum;
import com.tapdata.tm.Settings.constant.KeyEnum;
import com.tapdata.tm.Settings.entity.Settings;
import com.tapdata.tm.Settings.service.AlarmSettingService;
import com.tapdata.tm.Settings.service.SettingsService;
import com.tapdata.tm.alarm.constant.AlarmComponentEnum;
import com.tapdata.tm.alarm.constant.AlarmStatusEnum;
import com.tapdata.tm.alarm.constant.AlarmTypeEnum;
import com.tapdata.tm.alarm.entity.AlarmInfo;
import com.tapdata.tm.alarm.service.AlarmService;
import com.tapdata.tm.base.dto.Filter;
import com.tapdata.tm.base.dto.Page;
import com.tapdata.tm.base.dto.Where;
import com.tapdata.tm.base.exception.BizException;
import com.tapdata.tm.base.reporitory.BaseRepository;
import com.tapdata.tm.commons.base.dto.BaseDto;
import com.tapdata.tm.commons.dag.Node;
import com.tapdata.tm.commons.dag.nodes.DataParentNode;
import com.tapdata.tm.commons.dag.nodes.DatabaseNode;
import com.tapdata.tm.commons.dag.nodes.TableNode;
import com.tapdata.tm.commons.schema.DataSourceConnectionDto;
import com.tapdata.tm.commons.schema.Field;
import com.tapdata.tm.commons.schema.MetadataInstancesDto;
import com.tapdata.tm.commons.schema.bean.PlatformInfo;
import com.tapdata.tm.commons.task.constant.AlarmKeyEnum;
import com.tapdata.tm.commons.task.constant.AlarmSettingTypeEnum;
import com.tapdata.tm.commons.task.constant.NotifyEnum;
import com.tapdata.tm.commons.task.dto.ParentTaskDto;
import com.tapdata.tm.commons.task.dto.TaskDto;
import com.tapdata.tm.commons.task.dto.alarm.AlarmSettingDto;
import com.tapdata.tm.commons.task.dto.alarm.AlarmSettingVO;
import com.tapdata.tm.commons.util.JsonUtil;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.dataflow.dto.DataFlowDto;
import com.tapdata.tm.dataflow.service.DataFlowService;
import com.tapdata.tm.ds.service.impl.DataSourceService;
import com.tapdata.tm.inspect.bean.Limit;
import com.tapdata.tm.inspect.bean.Source;
import com.tapdata.tm.inspect.bean.Stats;
import com.tapdata.tm.inspect.bean.Task;
import com.tapdata.tm.inspect.bean.Timing;
import com.tapdata.tm.inspect.constant.InspectMethod;
import com.tapdata.tm.inspect.constant.InspectResultEnum;
import com.tapdata.tm.inspect.constant.InspectStatusEnum;
import com.tapdata.tm.inspect.constant.Mode;
import com.tapdata.tm.inspect.dto.InpsectTaskImportDto;
import com.tapdata.tm.inspect.dto.InspectDto;
import com.tapdata.tm.inspect.dto.InspectResultDto;
import com.tapdata.tm.inspect.entity.InspectEntity;
import com.tapdata.tm.inspect.entity.InspectResultEntity;
import com.tapdata.tm.inspect.recovery.AutoRecoveryUtils;
import com.tapdata.tm.inspect.repository.InspectRepository;
import com.tapdata.tm.inspect.vo.FailTableAndRowsVo;
import com.tapdata.tm.inspect.vo.InspectDetailVo;
import com.tapdata.tm.inspect.vo.InspectRecoveryStartVerifyVo;
import com.tapdata.tm.message.constant.Level;
import com.tapdata.tm.message.constant.MsgTypeEnum;
import com.tapdata.tm.message.service.MessageService;
import com.tapdata.tm.metadatainstance.service.MetadataInstancesService;
import com.tapdata.tm.permissions.DataPermissionHelper;
import com.tapdata.tm.task.constant.SyncType;
import com.tapdata.tm.task.service.TaskService;
import com.tapdata.tm.utils.*;
import com.tapdata.tm.worker.service.WorkerService;
import lombok.NonNull;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @Author:
 * @Date: 2021/09/14
 * @Description:
 */
@Service
@Slf4j
@Setter(onMethod_ = {@Autowired})
@Primary
public class InspectServiceImpl extends InspectService {
    private static final String USER_ID = "user_id";
    private MessageService messageService;
    private SettingsService settingsService;
    private InspectDetailsService inspectDetailsService;
    private InspectResultService inspectResultService;
    private WorkerService workerService;
    private AlarmSettingService alarmSettingService;
    private AlarmService alarmService;
    private MetadataInstancesService metadataInstancesService;
    private InspectTaskService inspectTaskService;
    private TaskService taskService;
    private DataSourceService dataSourceService;
    private static final String INSPECT_ID= "inspect_id";
    public static final String IS_DELETED = "is_deleted";

    public InspectServiceImpl(@NonNull InspectRepository repository) {
        super(repository);
    }

    @Override
    public Supplier<InspectDto> dataPermissionFindById(ObjectId inspectId, com.tapdata.tm.base.dto.Field fields) {
        return null == inspectId ? null : () -> {
            if (null != fields) {
                fields.put(USER_ID, true);
                fields.put(DataPermissionHelper.FIELD_NAME, true);
            }
            return findById(inspectId, fields);
        };
    }

    @Override
    public <T extends BaseDto> T convertToDto(InspectEntity entity, Class<T> dtoClass, String... ignoreProperties) {
        T dto = super.convertToDto(entity, dtoClass, ignoreProperties);
        if (dto instanceof InspectDto) {
            InspectDto inspectDto = (InspectDto) dto;
            inspectDto.setCanRecovery(AutoRecoveryUtils.checkCanRecovery(inspectDto).isEmpty());
        }
        return dto;
    }


    protected void beforeSave(InspectDto inspect, UserDetail user) {
        if (InspectStatusEnum.WAITING.getValue().equals(inspect.getStatus())) {
            inspect.setScheduleTimes(0);
        }

        List<String> tags = new ArrayList<>();
        if (inspect.getPlatformInfo() != null) {
            PlatformInfo platformInfo = inspect.getPlatformInfo();
            if (platformInfo.getRegion() != null) {
                tags.add(platformInfo.getRegion());
            }

            if (platformInfo.getZone() != null) {
                tags.add(platformInfo.getZone());
            }

            if (platformInfo.getAgentType() != null) {
                tags.add(platformInfo.getAgentType());
            }

            if (platformInfo.getIsThrough() != null && platformInfo.getIsThrough()) {
                tags.add("internet");
            }
            inspect.setAgentTags(tags);
        }


        if (StringUtils.isNotBlank(inspect.getFlowId())) {
            Criteria criteria = Criteria.where("id").is(MongoUtils.toObjectId(inspect.getFlowId()));
            Query query = new Query(criteria);
            query.fields().include("agentTags");
            DataFlowService dataFlowService = SpringContextHelper.getBean(DataFlowService.class);
            DataFlowDto dataFlowDto = dataFlowService.findOne(query, user);
            if (dataFlowDto != null) {
                if (CollectionUtils.isNotEmpty(dataFlowDto.getAgentTags())) {
                    inspect.setAgentTags(dataFlowDto.getAgentTags());
                }
            }
        }
    }


    /**
     * @param id
     * @param user
     * @return
     */
    @Transactional
    public Map<String, Long> delete(String id, UserDetail user) {
        deleteLogicsById(id);

        //移除定时器
        CronUtil.removeJob(id);
        long detailNum = 0;
        long resultNum = 0;
        Criteria criteria = Criteria.where(INSPECT_ID).is(id);
        detailNum = inspectDetailsService.deleteAll(new Query(criteria));
        resultNum = inspectResultService.deleteAll(new Query(criteria));
        Map<String, Long> result = new HashMap<>();
        result.put("detailNum", detailNum);
        result.put("resultNum", resultNum);

        //add message
        InspectEntity inspectEntity = repository.getMongoOperations().findById(MongoUtils.toObjectId(id), InspectEntity.class);
        if (null != inspectEntity) {
            messageService.addInspect(inspectEntity.getName(), id, MsgTypeEnum.DELETED, Level.INFO, user);
        }
        return result;
    }


    protected void joinResult(List<InspectDto> inspectDtos) {
        List<String> inspectIdList = inspectDtos.stream().map(InspectDto::getId).map(ObjectId::toString).collect(Collectors.toList());
        List<InspectResultDto> inspectResultDtoList = findLatestInspectResult(inspectIdList);

        Map<String, List<InspectResultDto>> inspectIdToInspectResult = inspectResultDtoList.stream().collect(Collectors.groupingBy(InspectResultDto::getInspect_id));

        for (InspectDto inspectDto : inspectDtos) {
            String inspectId = inspectDto.getId().toString();
            List<InspectResultDto> singleResultList = inspectIdToInspectResult.getOrDefault(inspectId, new ArrayList<InspectResultDto>());
            if (CollectionUtils.isNotEmpty(singleResultList)) {
                InspectResultDto latestResult = singleResultList.get(0);
                Pair<Integer, String> pair = ImmutablePair.of(0, InspectResultDto.RESULT_PASSED);
                pair = joinResult(inspectDto, latestResult);
                latestResult.setSourceTotal(latestResult.getFirstSourceTotal());
                inspectDto.setLastStartTime(inspectDto.getScheduleTime());
                inspectDto.setInspectResult(latestResult);
                inspectDto.setDifferenceNumber(pair.getKey());
                inspectDto.setResult(pair.getValue());
            } else {
                log.error("inspectDto  为：{}  还没有inspectResult", inspectDto);
            }
        }
    }

    protected List<InspectResultDto> findLatestInspectResult(List<String> inspectIdList){
        MongoTemplate mongoTemplate = repository.getMongoOperations();
        ProjectionOperation projection = Aggregation.project("_id","inspect_id","createTime");
        MatchOperation match = Aggregation.match(Criteria.where(INSPECT_ID).in(inspectIdList));
        GroupOperation groupOperation = Aggregation.group(INSPECT_ID).first("$$ROOT").as("latestData");
        SortOperation sort = Aggregation.sort(Sort.by("createTime").descending());
        Aggregation aggregation = Aggregation.newAggregation(projection,match,sort,groupOperation,Aggregation.replaceRoot().withValueOf("$latestData"));
        aggregation.withOptions(Aggregation.newAggregationOptions().allowDiskUse(true).build());
        AggregationResults<InspectResultEntity> results = mongoTemplate.aggregate(aggregation,"InspectResult", InspectResultEntity.class);
        List<ObjectId> ids = new ArrayList<>();
        results.getMappedResults().forEach(result -> {
            ids.add(result.getId());
        });
        List<InspectResultDto> inspectResultDtos = inspectResultService.findAll(new Query(Criteria.where("_id").in(ids)));
        return inspectResultDtos;
    }


    public static Pair<Integer, String> joinResult(InspectDto inspectDto, InspectResultDto inspectResultDto) {
        int differenceNumber = 0;
        String inspectResultResult = InspectResultDto.RESULT_PASSED;
        if (inspectDto == null) {
            return ImmutablePair.of(differenceNumber, inspectResultResult);
        }
        boolean add = StringUtils.isNotBlank(inspectDto.getInspectMethod()) && !"row_count".equals(inspectDto.getInspectMethod());

        if (CollectionUtils.isNotEmpty(inspectResultDto.getStats())) {
            for (Stats stat : inspectResultDto.getStats()) {
                if (!InspectResultDto.RESULT_PASSED.equals(stat.getResult())) {
                    inspectResultResult = InspectResultDto.RESULT_FAILED;
                }

                if (stat.getSourceOnly() == null) {
                    stat.setSourceOnly(0L);
                }

                if (stat.getTargetOnly() == null) {
                    stat.setTargetOnly(0L);
                }

                if (stat.getRowFailed() == null) {
                    stat.setRowFailed(0L);
                }

                if (add) {
                    Long sourceOnly = stat.getSourceOnly() == null ? (0L) : stat.getSourceOnly();
                    Long targetOnly = stat.getTargetOnly() == null ? (0L) : stat.getTargetOnly();
                    Long rowFailed = stat.getRowFailed() == null ? (0L) : stat.getRowFailed();
                    differenceNumber += (sourceOnly + targetOnly + rowFailed);
                }

            }
        }
        return ImmutablePair.of(differenceNumber, inspectResultResult);
    }


    public Page<InspectDto> list(Filter filter, UserDetail userDetail) {
        Where where = filter.getWhere();
        Map<String, Object> notDeleteMap = new HashMap<>();
        notDeleteMap.put("$ne", true);
        where.put("is_deleted", notDeleteMap);

        Criteria criteria = QueryUtil.parseWhereToCriteria(where);
        Query query = Query.query(criteria);
        long total = count(query);
        List<Sort> sorts = QueryUtil.parseOrder(filter);
        for (Sort sort : sorts) {
            query.with(sort);
        }
        QueryUtil.parsePageParam(filter, query);
        if (!userDetail.isRoot() && !userDetail.isFreeAuth() && !DataPermissionHelper.setFilterConditions(true, query, userDetail)) {
            criteria.and(USER_ID).is(userDetail.getUserId());
        } else {
            BaseRepository.removeFilter(USER_ID, query);
        }

        List<InspectEntity> inspectEntities = findAllEntity(query);
        List<InspectDto> inspectDtoList = Optional.ofNullable(inspectEntities).map(entities -> {
            List<InspectDto> dtoList = new ArrayList<>();
            for (InspectEntity inspectEntity : inspectEntities) {
                InspectDto inspectDto = convertToDto(inspectEntity, InspectDto.class);
                dtoList.add(inspectDto);
            }
            return dtoList;
        }).orElse(null);

        if (CollectionUtils.isNotEmpty(inspectDtoList)) {
            joinResult(inspectDtoList);
        }

        Page<InspectDto> result = new Page<>();
        result.setItems(inspectDtoList);
        result.setTotal(total);
        return result;
    }

    public InspectDto findById(Filter filter, UserDetail userDetail) {
        InspectDto inspectDto = super.findOne(filter, userDetail);
        Criteria criteria = Criteria.where(INSPECT_ID).is(inspectDto.getId().toHexString());
        Query query = new Query(criteria);
        query.with(Sort.by(Sort.Direction.DESC, "_id"));
        query.fields().exclude("inspect.timing");
        List<InspectResultDto> resultDtos = inspectResultService.findAllDto(query, userDetail);
        if (CollectionUtils.isNotEmpty(resultDtos)) {
            InspectResultDto inspectResultDto = resultDtos.get(0);
            inspectResultDto.setSourceTotal(inspectResultDto.getFirstSourceTotal());
            Pair<Integer, String> pair = InspectServiceImpl.joinResult(inspectDto, inspectResultDto);
            inspectDto.setInspectResult(resultDtos.get(0));
            inspectDto.setDifferenceNumber(pair.getKey());
        }
        BeanUtil.copyProperties(inspectDto, InspectDetailVo.class);
        return inspectDto;
    }


    @Override
    public InspectDto save(InspectDto inspectDto, UserDetail user) {
        inspectDto.setUserId(user.getUserId());
        List<Task> taskList = inspectDto.getTasks();
        if (CollectionUtils.isNotEmpty(taskList)) {
            taskList.forEach(task -> {
                task.setTaskId(UUIDUtil.getUUID());
            });
        }

        PlatformInfo platformInfo = inspectDto.getPlatformInfo();
        List agentTags = new ArrayList();
        if (StringUtils.isNotBlank(platformInfo.getAgentType())) {
            agentTags.add(platformInfo.getAgentType());
        }
        inspectDto.setAgentTags(agentTags);

      /*  if (Mode.MANUAL.getValue().equals(inspectDto.getMode())){
            inspectDto.setStatus(InspectStatusEnum.SCHEDULING.getValue());
        }
        else {
            inspectDto.setStatus(InspectStatusEnum.WAITING.getValue());
        }*/
        Date now = new Date();
        //保存的时候，直接把ping_time 保存为当前时间
        inspectDto.setPing_time(now.getTime());
        inspectDto.setLastStartTime(0L);

				supplementAlarm(inspectDto, user);

        super.save(inspectDto, user);

				if (InspectStatusEnum.SCHEDULING.getValue().equalsIgnoreCase(inspectDto.getStatus())) {
					Where where = new Where();
					where.put("id", inspectDto.getId().toString());
					inspectTaskService.executeInspect(where, inspectDto, user);
				}
        return inspectDto;
    }

    /**
     * 新建数据复制任务的时候，需要新建数据校验，就调用这个方法
     * 数据开发，目前不支持数据校验
     *
     * @param taskDto
     * @param userDetail
     * @return
     */
    public void saveInspect(TaskDto taskDto, UserDetail userDetail) {
        try {
            String synType = taskDto.getSyncType();
            if (SyncType.MIGRATE.getValue().equals(synType)) {
                InspectDto inspectDto = new InspectDto();
                inspectDto.setName(taskDto.getName());
                inspectDto.setTaskId(taskDto.getId().toString());
                inspectDto.setPing_time(new Date().getTime());
                inspectDto.setUserId(userDetail.getUserId());
                inspectDto.setMode("manual");
                inspectDto.setInspectMethod("cdcField");
                workerService.scheduleTaskToEngine(inspectDto, userDetail);
                super.save(inspectDto, userDetail);
            }
        } catch (Exception e) {
            log.error("新建任务同时，新建数据校验失败", e);
        }
    }

    public InspectDto createCheckByTask(TaskDto taskDto, UserDetail userDetail) {
        InspectDto inspectDto = new InspectDto();
        inspectDto.setFlowId(taskDto.getId().toHexString());
        inspectDto.setName(taskDto.getName() + "_inspect_" + DateUtil.formatTime(DateUtil.date()));
        inspectDto.setMode("manual");
        inspectDto.setInspectMethod("row_count");
        inspectDto.setInspectDifferenceMode("All");
        Limit limit = new Limit();
        limit.setKeep(100);
        inspectDto.setLimit(limit);
        inspectDto.setEnabled(true);
        inspectDto.setStatus("waiting");
        inspectDto.setPing_time(System.currentTimeMillis());

        inspectDto.setScheduleTimes(0);

        List<AlarmSettingVO> alarmSettings = new ArrayList<>();
        alarmSettings.add(AlarmSettingVO.builder()
                .type(AlarmSettingTypeEnum.INSPECT)
                .open(true)
                .key(AlarmKeyEnum.INSPECT_TASK_ERROR)
                .notify(Lists.newArrayList(NotifyEnum.SYSTEM, NotifyEnum.EMAIL)).build());
        Map<String, Object> map = Maps.newHashMap();
        map.put("maxDifferentialRows", 0);
        alarmSettings.add(AlarmSettingVO.builder()
                .type(AlarmSettingTypeEnum.INSPECT)
                .open(true)
                .key(AlarmKeyEnum.INSPECT_COUNT_ERROR)
                .notify(Lists.newArrayList(NotifyEnum.SYSTEM, NotifyEnum.EMAIL))
                .params(map).build());
        inspectDto.setAlarmSettings(alarmSettings);

        List<Task> taskList = new ArrayList<>();
        List<MetadataInstancesDto> instancesDtos = metadataInstancesService.findByTaskId(taskDto.getId().toHexString(), userDetail);
        Assert.notEmpty(instancesDtos);
        Map<String, List<MetadataInstancesDto>> metaMap = instancesDtos.stream().collect(Collectors.groupingBy(MetadataInstancesDto::getNodeId));

        taskDto.getDag().getSources().forEach(node -> {
            List<MetadataInstancesDto> metadataInstancesDtos = metaMap.get(node.getId());
            if (CollectionUtils.isNotEmpty(metadataInstancesDtos)) {
                metadataInstancesDtos.forEach(meta -> {
                    Task task = new Task();
                    task.setTaskId(UUIDUtil.getUUID());
                    task.setFullMatch(true);
                    task.setShowAdvancedVerification(false);

                    Source source = new Source();
                    source.setNodeId(node.getId());
                    source.setConnectionName(node.getName());
                    source.setNodeName(node.getName());

                    AtomicReference<String> sortColumn = new AtomicReference<>("");
                    meta.getFields().stream().filter(field -> field.getPrimaryKeyPosition() == 1).findFirst().ifPresent(key -> {
                        sortColumn.set(key.getFieldName());
                    });
                    if (StringUtils.isEmpty(sortColumn.get())) {
                        meta.getFields().stream().filter(field -> field.getColumnPosition() == 1).findFirst().ifPresent(key -> {
                            sortColumn.set(key.getFieldName());
                        });
                    }
                    source.setSortColumn(sortColumn.get());
                    source.setTable(meta.getOriginalName());
                    if (TaskDto.SYNC_TYPE_MIGRATE.equals(taskDto.getSyncType())) {
                        DatabaseNode databaseNode = (DatabaseNode) node;
                        source.setConnectionId(databaseNode.getConnectionId());
                        source.setDatabaseType(databaseNode.getDatabaseType());
                    } else {
                        TableNode tableNode = (TableNode) node;
                        source.setConnectionId(tableNode.getConnectionId());
                        source.setDatabaseType(tableNode.getDatabaseType());
                    }
                    if (Lists.newArrayList("Clickhouse", "Kafka").contains(source.getDatabaseType())) {
                        throw new BizException("Kafka or Clickhouse not suppport row count inspect!");
                    }

                    task.setSource(source);
                    taskList.add(task);
                });
            }
        });

        taskDto.getDag().getTargets().forEach(node -> {
            List<MetadataInstancesDto> metadataInstancesDtos = metaMap.get(node.getId());
            if (CollectionUtils.isNotEmpty(metadataInstancesDtos)) {
                metadataInstancesDtos.forEach(meta -> {

                    Task task = taskList.stream().filter(t -> meta.getAncestorsName().equals(t.getSource().getTable()))
                            .findFirst().orElseThrow(() -> new BizException("not found task by AncestorsName:" + meta.getAncestorsName()));

                    Source target = new Source();
                    target.setNodeId(node.getId());
                    target.setConnectionName(node.getName());
                    target.setNodeName(node.getName());

                    AtomicReference<String> sortColumn = new AtomicReference<>("");
                    meta.getFields().stream().filter(field -> field.getPrimaryKeyPosition() == 1).findFirst().ifPresent(key -> {
                        sortColumn.set(key.getFieldName());
                    });
                    if (StringUtils.isEmpty(sortColumn.get())) {
                        meta.getFields().stream().filter(field -> field.getColumnPosition() == 1).findFirst().ifPresent(key -> {
                            sortColumn.set(key.getFieldName());
                        });
                    }

                    target.setSortColumn(sortColumn.get());
                    target.setTable(meta.getOriginalName());
                    if (TaskDto.SYNC_TYPE_MIGRATE.equals(taskDto.getSyncType())) {
                        DatabaseNode databaseNode = (DatabaseNode) node;
                        target.setConnectionId(databaseNode.getConnectionId());
                        target.setDatabaseType(databaseNode.getDatabaseType());
                    } else {
                        TableNode tableNode = (TableNode) node;
                        target.setConnectionId(tableNode.getConnectionId());
                        target.setDatabaseType(tableNode.getDatabaseType());
                    }
                    if (Lists.newArrayList("Clickhouse", "Kafka").contains(target.getDatabaseType())) {
                        throw new BizException("Kafka or Clickhouse not suppport row count inspect!");
                    }

                    task.setTarget(target);
                });
            }
        });

        inspectDto.setTasks(taskList);

        save(inspectDto, userDetail);

        return inspectDto;
    }

    @Override
    public List<InspectDto> findByTaskIdList(List<String> taskIdList) {
        Query query = Query.query(Criteria.where("flowId").in(taskIdList).and("is_deleted").ne(true));
        return findAll(query);
    }

    @Override
    public UpdateResult deleteByTaskId(String taskId) {
        UpdateResult updateResult = null;
        try {
            Query query = Query.query(Criteria.where("taskId").is(taskId));
            Update update = new Update().set("is_deleted", true);
            updateResult = update(query, update);
        } catch (Exception e) {
            log.error("删除校验任务异常", e);
        }
        return updateResult;
    }

    @Override
    public InspectDto doExecuteInspect(Where where, InspectDto updateDto, UserDetail user) {
        String status = updateDto.getStatus();
        if (InspectStatusEnum.SCHEDULING.getValue().equals(status)) {
            return inspectTaskService.inspectTaskRun(where, updateDto, user);
        }
        update(where, updateDto, user);
        String id = where.getOrDefault("id", "").toString();
        if (InspectStatusEnum.STOPPING.getValue().equals(status)) {
            return inspectTaskService.inspectTaskStop(id, updateDto, user);
        }
        if (InspectStatusEnum.ERROR.getValue().equals(status)) {
            return inspectTaskService.inspectTaskError(id, updateDto, user);
        }
        if (InspectStatusEnum.DONE.getValue().equals(status)) {
            return inspectTaskService.inspectTaskDone(id, updateDto, user);

        }
        return updateDto;
    }

    protected void update(Where where, InspectDto updateDto, UserDetail user) {
        super.updateByWhere(where, updateDto, user);
    }

    /**
     * 编辑的时候，如果新增了校验条件，就要新生成一个taskId ,如果原有taskId ,就不用
     *
     * @param objectId
     * @param inspectDto
     * @param userDetail
     * @return
     */
    public InspectDto updateById(ObjectId objectId, InspectDto inspectDto, UserDetail userDetail) {
        Where where = new Where();
        where.put("id", objectId);
        supplementAlarm(inspectDto, userDetail);

        List<Task> newTaskList = inspectDto.getTasks();
        if (CollectionUtils.isNotEmpty(newTaskList)) {
            newTaskList.stream()
                .filter(t -> Objects.nonNull(t) && Objects.isNull(t.getTaskId()))
                .forEach(task -> task.setTaskId(UUIDUtil.getUUID()));
        }

        workerService.scheduleTaskToEngine(inspectDto, userDetail);

        String agentId = inspectDto.getAgentId();
        updateByWhere(where, inspectDto, userDetail);

        //编辑的时候，都先把就的定时任务删掉
        CronUtil.removeJob(inspectDto.getId().toString());

        if (InspectStatusEnum.SCHEDULING.getValue().equalsIgnoreCase(inspectDto.getStatus())) {
            inspectTaskService.startInspectTask(inspectDto, agentId);
        }
        return inspectDto;
    }


    public List<InspectDto> findByName(String name) {
        Query query = Query.query(Criteria.where("name").is(name).and("is_deleted").ne(true));
        List<InspectDto> inspectDtoList = findAll(query);

        return inspectDtoList;
    }

    /**
     * * upsert  0跳过已有数据  1 覆盖已有数据
     *
     * @param json
     * @param upsert
     */
    public void importData(String json, String upsert, UserDetail userDetail) {
        Map map = JsonUtil.parseJson(json, Map.class);
        List<Map> data = (List) map.get("data");

        if (CollectionUtils.isNotEmpty(data)) {
            for (Map singleDataMap : data) {
                Query query = new Query();
                String id = singleDataMap.get("id").toString();
                singleDataMap.remove("id");
                if (StringUtils.isNotBlank(id)) {
                    InspectDto existedDto = findById(MongoUtils.toObjectId(id), userDetail);
                    Map existedPoperty = BeanUtil.beanToMap(existedDto);
                    if ("0".equals(upsert)) {
//                        跳过已有数据,
                        singleDataMap.forEach((key, value) ->
                        {
                            if (existedPoperty.containsKey(key)) {
                                singleDataMap.put(key, existedPoperty.get(key));
                            }
                        });

                    }
                    query.addCriteria(Criteria.where("id").is(id));
                }
                InspectDto newDto = BeanUtil.mapToBean(singleDataMap, InspectDto.class, false, CopyOptions.create());
                newDto.setIs_deleted(false);
//                newDto.setResult("");
                newDto.setPing_time(new Date().getTime());
//                newDto.setInspectResult(null);
                newDto.setLastStartTime(null);
/*                newDto.setErrorMsg("");
                newDto.setStatus("");*/
//                newDto.setDifferenceNumber(0);
                super.upsert(query, newDto, userDetail);
            }
        }
    }

    public void setRepeatInspectTask() {
        Query query = Query.query(Criteria.where("mode").is(Mode.CRON.getValue()).and("enabled").is(true).and("is_deleted").ne(true));
        List<InspectDto> inspectDtoList = findAll(query);

        inspectDtoList.forEach(inspectDto -> {
            Timing timing = inspectDto.getTiming();
            if (null != timing) {
                Date startDate = new Date(timing.getStart());
                Date endDate = new Date(timing.getEnd());
                Long intervals = inspectDto.getTiming().getIntervals();
                String intervalUnit = inspectDto.getTiming().getIntervalsUnit();
                String id = inspectDto.getId().toString();
                CronUtil.addJob(startDate, endDate, intervals, intervalUnit, id);
            } else {
                log.error("数据有误，定时校验任务timing为空 inspectDto：{}", JsonUtil.toJson(inspectDto));
            }
        });
    }


    public UpdateResult updateStatusById(String id, InspectStatusEnum inspectStatusEnum) {
        Update update = new Update();
        update.set("status", inspectStatusEnum.getValue());
        Query updateQuery = new Query(Criteria.where("id").is(id));
        return repository.getMongoOperations().updateFirst(updateQuery, update, InspectEntity.class);
    }

    public UpdateResult updateStatusByIds(List<ObjectId> idList, InspectStatusEnum status) {
        Update update = new Update();
        update.set("status", status.getValue());
        Query updateQuery = new Query(Criteria.where("id").in(idList));
        return repository.getMongoOperations().updateMulti(updateQuery, update, InspectEntity.class);
    }

    public Map<String, Integer> inspectPreview(UserDetail user) {
        Query query = Query.query(Criteria.where("is_deleted").ne(true).and("user_id").is(user.getUserId()));
        List<InspectDto> inspectDtos = findAll(query);
        if (CollectionUtils.isNotEmpty(inspectDtos)) {
            joinResult(inspectDtos);
        }

        int error = inspectDtos.stream().filter(item->InspectStatusEnum.ERROR.getValue().equals(item.getStatus())).collect(Collectors.toList()).size();
        int passed =  inspectDtos.stream().filter(item->InspectResultEnum.PASSED.getValue().equals(item.getResult())
                                                &&(!InspectStatusEnum.ERROR.getValue().equals(item.getStatus()) )).collect(Collectors.toList()).size();

        int countDiff = inspectDtos.stream().filter(item->InspectResultEnum.FAILED.getValue().equals(item.getResult())
                                                        &&InspectMethod.ROW_COUNT.getValue().equals(item.getInspectMethod())
                                                        &&!InspectStatusEnum.ERROR.getValue().equals(item.getStatus()) ).collect(Collectors.toList()).size();

        int valueDiff = inspectDtos.stream().filter(item->InspectResultEnum.FAILED.getValue().equals(item.getResult())
                                                        &&!InspectMethod.ROW_COUNT.getValue().equals(item.getInspectMethod())
                                                        &&!InspectStatusEnum.ERROR.getValue().equals(item.getStatus()) ).collect(Collectors.toList()).size();;


        /*for (InspectDto inspectDto : inspectDtos) {
            if ("error".equals(inspectDto.getStatus())) {
                error++;
            } else if ("passed".equals(inspectDto.getResult())) {
                passed++;
            } else if ("row_count".equals(inspectDto.getInspectMethod())) {
                countDiff++;
            } else {
                valueDiff++;
            }
        }*/
        Map<String, Integer> chart5 = new HashMap<>();
        chart5.put("total", inspectDtos.size());
        chart5.put("error", error);
        chart5.put("passed", passed);
        chart5.put("countDiff", countDiff);
        chart5.put("valueDiff", valueDiff);
        return chart5;
    }

    public List<InspectDto> findByStatus(InspectStatusEnum inspectStatusEnum) {
        Query query = Query.query(Criteria.where("status").is(inspectStatusEnum.getValue()));
        List<InspectDto> inspectDtoList = findAllNotDeleted(query);
        return inspectDtoList;
    }

    public List<InspectDto> findByResult(boolean passed) {
        Query query;
        if (passed) {
            query = Query.query(Criteria.where("result").is(InspectStatusEnum.PASSED.getValue()));
        } else {
            query = Query.query(Criteria.where("result").ne(InspectStatusEnum.PASSED.getValue()));
        }
        return findAllNotDeleted(query);
    }

    public void cleanDeadInspect() {
        Settings settings = settingsService.getByCategoryAndKey(CategoryEnum.JOB, KeyEnum.JOB_HEART_TIMEOUT);
        Long timeOutMillSeconds = Long.valueOf(settings.getValue().toString());

        Date now = new Date();
        //超时时长 为 5分钟
        Long timeOutDate = now.getTime() - timeOutMillSeconds;
        Query query = Query.query(Criteria.where("status").is(InspectStatusEnum.SCHEDULING.getValue()).and("ping_time").lt(timeOutDate));
        Update update = new Update().set("status", InspectStatusEnum.ERROR.getValue());

        List list = findAll(query);
        log.info(JsonUtil.toJson(list));

        UpdateResult updateResult = update(query, update);
    }

		public void supplementAlarm(InspectDto inspectDto, UserDetail userDetail) {
        try {
            List<AlarmSettingDto> settingDtos = alarmSettingService.findAllAlarmSetting(userDetail);
            Map<AlarmKeyEnum, AlarmSettingDto> settingDtoMap = settingDtos.stream().collect(Collectors.toMap(AlarmSettingDto::getKey, Function.identity(), (e1, e2) -> e1));
            if (CollectionUtils.isEmpty(inspectDto.getAlarmSettings())) {
                List<AlarmSettingDto> alarmSettingDtos = Lists.newArrayList();
                alarmSettingDtos.add(settingDtoMap.get(AlarmKeyEnum.INSPECT_TASK_ERROR));
                alarmSettingDtos.add(settingDtoMap.get(AlarmKeyEnum.INSPECT_COUNT_ERROR));
                alarmSettingDtos.add(settingDtoMap.get(AlarmKeyEnum.INSPECT_VALUE_ERROR));
                inspectDto.setAlarmSettings(CglibUtil.copyList(alarmSettingDtos, AlarmSettingVO::new));
            }
        } catch (Exception e) {
            log.warn("supplement alarm error", e);
        }
    }

	@Override
	public long updateByWhere(Where where, InspectDto dto, UserDetail userDetail) {
			//更新状态
		String status = dto.getStatus();
		switch (Objects.requireNonNull(InspectStatusEnum.of(status))) {
			case ERROR:
			case FAILED:
				List<InspectDto> inspectDtos = this.findAll(new Filter(where));
				if (CollectionUtils.isNotEmpty(inspectDtos)) {
					for (InspectDto inspectDto : inspectDtos) {
						boolean checkOpen = alarmService.checkOpen(inspectDto.getAlarmSettings(), AlarmKeyEnum.INSPECT_TASK_ERROR, null, userDetail);
						if (checkOpen) {
							Map<String, Object> param = Maps.newHashMap();
							param.put("inspectName", inspectDto.getName());
							param.put("alarmDate", DateUtil.now());
							AlarmInfo errorInfo = AlarmInfo.builder().status(AlarmStatusEnum.ING).level(Level.CRITICAL).component(AlarmComponentEnum.FE)
											.type(AlarmTypeEnum.INSPECT_ALARM).agentId(inspectDto.getAgentId()).inspectId(inspectDto.getId().toHexString())
											.name(inspectDto.getName()).summary("INSPECT_TASK_ERROR").metric(AlarmKeyEnum.INSPECT_TASK_ERROR)
											.param(param)
											.lastNotifyTime(DateUtil.date())
											.build();
							errorInfo.setUserId(inspectDto.getUserId());
                            log.info("inspect service errorInfo:{}", JsonUtil.toJson(errorInfo));
							alarmService.save(errorInfo);
						}
					}
				}
				break;
			case SCHEDULING:
				Object id = where.get("id");
				if (id instanceof ObjectId) {
					id = ((ObjectId) id).toHexString();
				} else {
					id = id.toString();
				}
				alarmService.closeWhenInspectTaskRunning((String) id);
				break;

		}
		return super.updateByWhere(where, dto, userDetail);
	}

	public List<InspectDto> findAllByIds(List<String> inspectIds) {
		List<ObjectId> ids = inspectIds.stream().map(ObjectId::new).collect(Collectors.toList());

		Query query = new Query(Criteria.where("_id").in(ids));
		List<InspectEntity> entityList = findAllEntity(query);
		return CglibUtil.copyList(entityList, InspectDto::new);
//        return findAll(query);
	}



    @Override
    public InspectRecoveryStartVerifyVo recoveryStartVerity(InspectDto inspectDto) {
        InspectRecoveryStartVerifyVo vo = new InspectRecoveryStartVerifyVo();
        vo.setInspectId(Optional.ofNullable(inspectDto.getId()).map(ObjectId::toHexString).orElse(null));
        vo.setDiffLimit(Optional.ofNullable(inspectDto.getLimit()).map(Limit::getKeep).orElse(null));

        // 判断并设置是否可修复标记
        vo.setErrorCodes(AutoRecoveryUtils.checkCanRecovery(inspectDto));
        if (!vo.getErrorCodes().isEmpty()) {
            return vo;
        }
        vo.setCanRecovery(true);
        vo.setFlowId(inspectDto.getFlowId());

        // 设置二次校验标记
        InspectResultDto inspectResultDto = inspectResultService.getLatestInspectResult(inspectDto.getId());
        if (null == inspectResultDto) {
            vo.getErrorCodes().add("Inspect.Recovery.ResultNotFound");
        } else {
            vo.setFirstCheckId(inspectResultDto.getFirstCheckId());
            Optional.ofNullable(inspectResultDto.getStats()).ifPresent(statsList -> {
                long failRowTotals = 0;
                long sourceOnly = 0;
                long targetOnly = 0;
                for (Stats stats : statsList) {
                    if ("done".equals(stats.getStatus()) && "failed".equals(stats.getResult())) {
                        failRowTotals += stats.getRowFailed();
                        sourceOnly += Optional.ofNullable(stats.getSourceOnly()).orElse(0L);
                        targetOnly += Optional.ofNullable(stats.getTargetOnly()).orElse(0L);
                    }
                }
                vo.setDiffTotals(failRowTotals);
                vo.setSourceOnly(sourceOnly);
                vo.setTargetOnly(targetOnly);
            });
            FailTableAndRowsVo failTableAndRowsVo = inspectDetailsService.totalFailTableAndRows(inspectResultDto.getId().toString());
            if (null != failTableAndRowsVo) {
                vo.setRecoveryDataTotals(failTableAndRowsVo.getRowsTotals());
                vo.setRecoveryTableTotals(failTableAndRowsVo.getTableTotals());
            }
        }

        // 根据任务状态判断是否可执行修复
        ObjectId taskId = new ObjectId(vo.getFlowId());
        com.tapdata.tm.base.dto.Field includeField = new com.tapdata.tm.base.dto.Field();
        includeField.put("type", 1);
        includeField.put("status", 1);
        includeField.put("syncStatus", 1);
        includeField.put("agentId", 1);
        includeField.put("name", 1);
        includeField.put("delayTime", 1);
        TaskDto taskDto = taskService.findById(taskId, includeField);
        if (null == taskDto) {
            vo.getErrorCodes().add("Inspect.Recovery.TaskNotFound");
            return vo;
        }

        if (null == taskDto.getType() || !taskDto.getType().contains(ParentTaskDto.TYPE_CDC)) {
            vo.getErrorCodes().add("Inspect.Recovery.TaskTypeIsNotCDC");
        }
        if (!TaskDto.STATUS_RUNNING.equals(taskDto.getStatus())) {
            vo.getErrorCodes().add("Inspect.Recovery.TaskIsNotRunning");
        }
        if (!"CDC".equals(taskDto.getSyncStatus())) {
            vo.getErrorCodes().add("Inspect.Recovery.TaskSyncStatusIsNotCDC");
        }

        vo.setAgentId(taskDto.getAgentId());
        vo.setFlowName(taskDto.getName());
        vo.setFlowType(taskDto.getType());
        vo.setFlowStatus(taskDto.getStatus());
        vo.setFlowMilestoneStage(taskDto.getSyncStatus());
        vo.setFlowDelay(taskDto.getDelayTime());
        return vo;
    }

    @Override
    public InspectRecoveryStartVerifyVo recoveryStart(InspectDto inspectDto, UserDetail userDetail) {
        InspectRecoveryStartVerifyVo startVerifyVo = recoveryStartVerity(inspectDto);
        if (!startVerifyVo.getErrorCodes().isEmpty()) {
            throw new BizException(startVerifyVo.getErrorCodes().get(0));
        }

        Where where = new Where();
        where.put("id", inspectDto.getId());

        inspectDto.setByFirstCheckId(startVerifyVo.getFirstCheckId());
        inspectDto.setStatus(InspectStatusEnum.SCHEDULING.getValue());
        inspectDto.setAgentId(startVerifyVo.getAgentId());
        inspectDto.setEnableRecovery(true);
        workerService.scheduleTaskToEngine(inspectDto, userDetail);

        String agentId = inspectDto.getAgentId();
        updateByWhere(where, inspectDto, userDetail);
        inspectTaskService.startInspectTask(inspectDto, agentId);
        return startVerifyVo;
    }

    @Override
    public void importTask(MultipartFile file, String taskId, UserDetail userDetail) {
        try {
            byte[] bytes = GZIPUtil.unGzip(file.getBytes());
            String json = new String(bytes, StandardCharsets.UTF_8);
            ObjectMapper objectMapper = new ObjectMapper();
            InpsectTaskImportDto inpsectTaskImportDto = objectMapper.readValue(json, InpsectTaskImportDto.class);
            if (CollectionUtils.isEmpty(inpsectTaskImportDto.getData())) {
                return;
            }
            for (InspectDto originInspect : inpsectTaskImportDto.getData()) {
                InspectDto newInspect = new InspectDto();
                if (StringUtils.isNotBlank(taskId)) {
                    TaskDto taskDto = taskService.findById(new ObjectId(taskId));
                    if (CollectionUtils.isNotEmpty(originInspect.getTasks())) {
                        List<Task> copiedTasks = originInspect.getTasks().stream().map(task -> copyAndAdjustTask(task, taskDto))
                                .collect(Collectors.toList());
                        newInspect.setTasks(copiedTasks);
                    }
                    newInspect.setFlowId(taskId);
                } else {
                    if (CollectionUtils.isNotEmpty(originInspect.getTasks())) {
                        List<Task> copiedTasks = originInspect.getTasks().stream().map(task -> {
                            Task copiedTask = new Task();
                            BeanUtils.copyProperties(task, copiedTask);
                            nullifyConnectionInfo(copiedTask.getSource());
                            nullifyConnectionInfo(copiedTask.getTarget());
                            copiedTask.setTaskId(null);
                            return copiedTask;
                        }).collect(Collectors.toList());
                        newInspect.setTasks(copiedTasks);
                    }
                }
                String newName = originInspect.getName();
                while (checkTaskNameNotError(newName, userDetail, null)) {
                    newName += "_import";
                }
                newInspect.setName(newName);
                newInspect.setMode(originInspect.getMode());
                newInspect.setInspectMethod(originInspect.getInspectMethod());
                newInspect.setInspectDifferenceMode(originInspect.getInspectDifferenceMode());
                newInspect.setPlatformInfo(originInspect.getPlatformInfo());
                newInspect.setTiming(originInspect.getTiming());
                newInspect.setLimit(originInspect.getLimit());
                newInspect.setEnabled(originInspect.getEnabled());
                newInspect.setStatus("waiting");
                newInspect.setPing_time(new Date().getTime());
                newInspect.setBrowserTimezoneOffset(originInspect.getBrowserTimezoneOffset());
                newInspect.setAlarmSettings(originInspect.getAlarmSettings());
                newInspect.setEnableRecovery(originInspect.getEnableRecovery());
                save(newInspect, userDetail);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    protected Task copyAndAdjustTask(Task originTask, TaskDto taskDto) {
        Task copiedTask = new Task();
        BeanUtils.copyProperties(originTask, copiedTask);

        if (TaskDto.SYNC_TYPE_MIGRATE.equals(taskDto.getSyncType())) {
            DataParentNode sourceNode = (DataParentNode) taskService.getSourceNode(taskDto);
            DataParentNode targetNode = (DataParentNode) taskService.getTargetNode(taskDto);

            copiedTask.setSource(copyAndSetAttributes(originTask.getSource(), sourceNode));
            copiedTask.setTarget(copyAndSetAttributes(originTask.getTarget(), targetNode));

        } else {
            nullifyConnectionInfo(copiedTask.getSource());
            nullifyConnectionInfo(copiedTask.getTarget());
        }

        copiedTask.setTaskId(null);
        return copiedTask;
    }

    protected void nullifyConnectionInfo(Source source) {
        source.setConnectionId(null);
        source.setNodeId(null);
        source.setNodeName(null);
        source.setConnectionName(null);
    }
    public boolean checkTaskNameNotError(String newName, UserDetail user, ObjectId id) {

        Criteria criteria = Criteria.where("name").is(newName).and(IS_DELETED).ne(true);
        if (id != null) {
            criteria.and("_id").ne(id);
        }
        Query query = new Query(criteria);
        long count = count(query, user);
        return count > 0;
    }

    protected Source copyAndSetAttributes(Source source, DataParentNode sourceNode) {
        Source copySource = new Source();
        BeanUtils.copyProperties(source, copySource);
        copySource.setConnectionId(sourceNode.getConnectionId());
        copySource.setNodeId(sourceNode.getId());
        copySource.setNodeName(sourceNode.getName());
        DataSourceConnectionDto dataSourceConnectionDto = dataSourceService.findById(new ObjectId(sourceNode.getConnectionId()));
        copySource.setConnectionName(dataSourceConnectionDto.getCollection_name());
        copySource.setDatabaseType(dataSourceConnectionDto.getDatabase_type());
        return copySource;
    }
}
