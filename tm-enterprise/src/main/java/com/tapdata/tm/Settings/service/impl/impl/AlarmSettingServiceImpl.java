package com.tapdata.tm.Settings.service.impl.impl;

import cn.hutool.extra.cglib.CglibUtil;
import com.tapdata.tm.Settings.entity.AlarmSetting;
import com.tapdata.tm.Settings.repository.AlarmSettingsRepository;
import com.tapdata.tm.Settings.service.AlarmSettingService;
import com.tapdata.tm.Settings.service.SettingsService;
import com.tapdata.tm.alarmrule.dto.UpdateRuleDto;
import com.tapdata.tm.commons.task.constant.AlarmKeyEnum;
import com.tapdata.tm.commons.task.dto.alarm.AlarmSettingDto;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.user.service.UserService;
import com.tapdata.tm.utils.OEMReplaceUtil;
import lombok.NonNull;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.*;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * <AUTHOR>
 * @date 2022/9/5
 */
@Service
@Setter(onMethod_ = {@Autowired})
public class AlarmSettingServiceImpl extends AlarmSettingService{
    private MongoTemplate mongoTemplate;

    private UserService  userService;
    @Autowired
    SettingsService settingsService;

    @Autowired
    @Qualifier("caffeineCache")
    private CaffeineCacheManager alarmSettingsCache;
    public static final String ALARM_SETTING_CACHE = "alarmSettingCache";

    public AlarmSettingServiceImpl(@NonNull AlarmSettingsRepository repository) {
        super(repository);
    }

    @Override
    public void saveAlarmSetting(List<AlarmSettingDto> alarms, UserDetail userDetail) {
        if (settingsService.isCloud()) {
           Objects.requireNonNull(alarmSettingsCache.getCache(ALARM_SETTING_CACHE)).evictIfPresent(userDetail.getUserId());
        }
        List<AlarmSetting> data = CglibUtil.copyList(alarms, AlarmSetting::new);
        if (CollectionUtils.isNotEmpty(data)) {
            data.forEach(info -> repository.save(info,userDetail));
        }
    }
    @Override
    public List<AlarmSettingDto> findAllAlarmSetting(UserDetail userDetail) {
        if (settingsService.isCloud()) {
            return Optional.ofNullable(alarmSettingsCache.getCache(ALARM_SETTING_CACHE))
                    .map(cache -> cache.get(userDetail.getUserId(), () -> getAllAlarmSetting(userDetail)))
                    .orElseGet(() -> getAllAlarmSetting(userDetail));
        } else {
            return getAllAlarmSetting(userDetail);
        }
    }

    protected List<AlarmSettingDto> getAllAlarmSetting(UserDetail userDetail){
        List<AlarmSetting> alarmSettings = repository.findAll(userDetail);
        if (CollectionUtils.isEmpty(alarmSettings) || alarmSettings.stream().noneMatch(t -> AlarmKeyEnum.INSPECT_TASK_ERROR.equals(t.getKey()))) {
            Query  query = Query.query(Criteria.where("userId").exists(false));
            alarmSettings = mongoTemplate.find(query, AlarmSetting.class);
            if (CollectionUtils.isNotEmpty(alarmSettings)) {
                alarmSettings.forEach(sett -> {
                    sett.setId(null);
                });
            }
        }
        List<AlarmSetting> list = alarmSettings.stream().collect(
                collectingAndThen(
                        toCollection(() -> new TreeSet<>(Comparator.comparing(AlarmSetting::getKey))), ArrayList::new));
        for (int i = 0; i < list.size(); i++) {
            AlarmSetting alarmSetting = list.get(i);
            Map<String, Object> oemConfig = OEMReplaceUtil.getOEMConfigMap("email/replace.json");
            alarmSetting.setEmailAlarmTitle(OEMReplaceUtil.replace(alarmSetting.getEmailAlarmTitle(), oemConfig));
            alarmSetting.setEmailAlarmContent(OEMReplaceUtil.replace(alarmSetting.getEmailAlarmContent(), oemConfig));
        }
        return CglibUtil.copyList(list, AlarmSettingDto::new);
    }

    @Override
    protected void beforeSave(AlarmSettingDto dto, UserDetail userDetail) {

    }
    @Override
    public void updateSystemNotify(UpdateRuleDto ruleDto, UserDetail userDetail) {
        if (settingsService.isCloud()) {
            Objects.requireNonNull(alarmSettingsCache.getCache(ALARM_SETTING_CACHE)).evictIfPresent(userDetail.getUserId());
        }
        Query query = new Query(Criteria.where("key").is(ruleDto.getKey()));
        Update update = new Update().set("systemNotify", ruleDto.isNotify());
        repository.updateFirst(query, update, userDetail);
    }

    @Override
    public AlarmSettingDto findByKey(AlarmKeyEnum keyEnum, UserDetail userDetail) {
        Query query = Query.query(Criteria.where("key").is(keyEnum.name()));
        AlarmSettingDto alarmSetting = findOne(query, userDetail);
        if (Objects.isNull(alarmSetting)) {
            query = Query.query(Criteria.where("userId").exists(false));
            alarmSetting = mongoTemplate.findOne(query, AlarmSettingDto.class);
        }

        if (Objects.isNull(alarmSetting)) {
            return null;
        }
        return alarmSetting;
    }
}
