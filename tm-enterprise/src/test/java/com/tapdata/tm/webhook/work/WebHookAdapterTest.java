package com.tapdata.tm.webhook.work;

import com.tapdata.tm.Unit4Util;
import com.tapdata.tm.alarm.constant.AlarmTypeEnum;
import com.tapdata.tm.commons.task.constant.AlarmKeyEnum;
import com.tapdata.tm.webhook.dto.WebHookInfoDto;
import com.tapdata.tm.webhook.entity.HookOneHistory;
import com.tapdata.tm.webhook.entity.WebHookEvent;
import com.tapdata.tm.webhook.enums.HookType;
import com.tapdata.tm.webhook.enums.PingResult;
import com.tapdata.tm.webhook.impl.WebHookHistoryServiceImpl;
import com.tapdata.tm.webhook.impl.WebHookServiceImpl;
import com.tapdata.tm.webhook.impl.convert.Converter;
import com.tapdata.tm.webhook.util.WebHookHttpUtil;
import com.tapdata.tm.webhook.vo.WebHookInfoVo;
import io.tapdata.entity.simplify.TapSimplify;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.internal.verification.Times;
import org.slf4j.Logger;
import org.springframework.context.ApplicationContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doCallRealMethod;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class WebHookAdapterTest {
    WebHookAdapter adapter;
    WebHookHistoryServiceImpl webHookHistoryService;
    WebHookServiceImpl webHookService;
    ApplicationContext applicationContext;
    WebHookHttpUtil webHookHttpUtil;
    WebHookEvent event;
    HookOneHistory send;
    Logger log;

    @BeforeEach
    void init() {
        log = mock(Logger.class);
        send = new HookOneHistory();
        event = WebHookEvent.of().withType("t").withMetric("m");
        adapter = mock(WebHookAdapter.class);
        webHookHistoryService = mock(WebHookHistoryServiceImpl.class);
        webHookService = mock(WebHookServiceImpl.class);
        applicationContext = mock(ApplicationContext.class);
        webHookHttpUtil = mock(WebHookHttpUtil.class);
        doCallRealMethod().when(adapter).setWebHookHistoryService(webHookHistoryService);
        doCallRealMethod().when(adapter).setWebHookService(webHookService);
        doCallRealMethod().when(adapter).setApplicationContext(applicationContext);
        doCallRealMethod().when(adapter).setWebHookHttpUtil(webHookHttpUtil);
        adapter.setWebHookHistoryService(webHookHistoryService);
        adapter.setWebHookService(webHookService);
        adapter.setApplicationContext(applicationContext);
        adapter.setWebHookHttpUtil(webHookHttpUtil);
        Unit4Util.mockSlf4jLog(adapter, log);
    }

    @Nested
    class SendTest {
        List<String> userId;

        @BeforeEach
        void init() {
            userId = new ArrayList<>();
            event.withUserId(userId);
            doNothing().when(adapter).sendAsync(any(WebHookEvent.class), anyList());
            doCallRealMethod().when(adapter).send(event);
        }

        @Test
        void testNormal() {
            event.withType("t")
                    .withMetric("m");
            when(webHookService.findMyOpenHookInfoList("t", "m", userId)).thenReturn(mock(List.class));
            adapter.send(event);
            verify(webHookService, times(1)).findMyOpenHookInfoList("t", "m", userId);
            verify(adapter, times(1)).sendAsync(any(WebHookEvent.class), anyList());
        }

        @Test
        void testTypeIsBlank() {
            event.withType(null)
                    .withMetric("m");
            when(webHookService.findMyOpenHookInfoList(null, "m", userId)).thenReturn(mock(List.class));
            adapter.send(event);
            when(webHookService.findMyOpenHookInfoList(any(), anyString(), anyList())).thenReturn(mock(List.class));
            verify(webHookService, times(1)).findMyOpenHookInfoList(null, "m", userId);
            verify(adapter, times(1)).sendAsync(any(WebHookEvent.class), anyList());
        }

        @Test
        void testMetricIsBlank() {
            event.withType("t")
                    .withMetric(null);
            when(webHookService.findMyOpenHookInfoList("t", null, userId)).thenReturn(mock(List.class));
            adapter.send(event);
            when(webHookService.findMyOpenHookInfoList(anyString(), any(), anyList())).thenReturn(mock(List.class));
            verify(webHookService, times(1)).findMyOpenHookInfoList("t", null, userId);
            verify(adapter, times(1)).sendAsync(any(WebHookEvent.class), anyList());
        }

        @Test
        void testMetricAndTypeIsBlank() {
            event.withType(null)
                    .withMetric(null);
            when(webHookService.findMyOpenHookInfoList(null, null, userId)).thenReturn(mock(List.class));
            adapter.send(event);
            verify(webHookService, times(0)).findMyOpenHookInfoList(null, null, userId);
            verify(adapter, times(0)).sendAsync(any(WebHookEvent.class), anyList());
        }
    }

    @Nested
    class SendAsyncTest {
        List<WebHookInfoDto> myOpenHookInfoList;
        CompletableFuture<Void> supplyAsync;

        @BeforeEach
        void init() {
            supplyAsync = mock(CompletableFuture.class);
            myOpenHookInfoList = new ArrayList<>();
            myOpenHookInfoList.add(null);
            myOpenHookInfoList.add(new WebHookInfoDto());
            doAnswer(a -> {
                Runnable argument = a.getArgument(0, Runnable.class);
                argument.run();
                return null;
            }).when(supplyAsync).thenRunAsync(any(Runnable.class));
            when(adapter.sendAndSave(any(), any())).thenReturn(send);
            doCallRealMethod().when(adapter).sendAsync(event, myOpenHookInfoList);
        }

        @Test
        void testNormal() {
            try (MockedStatic<CompletableFuture> cf = mockStatic(CompletableFuture.class)) {
                cf.when(() -> CompletableFuture.runAsync(any(Runnable.class))).thenAnswer(a -> {
                    Runnable argument = a.getArgument(0, Runnable.class);
                    argument.run();
                    return supplyAsync;
                });
                adapter.sendAsync(event, myOpenHookInfoList);
                cf.verify(() -> CompletableFuture.runAsync(any(Runnable.class)), times(1));
                verify(adapter, times(1)).sendAndSave(any(), any());
                verify(supplyAsync, times(1)).thenRunAsync(any(Runnable.class));
            }
        }

        @Test
        void testMyOpenHookInfoListIsEmpty() {
            myOpenHookInfoList.clear();
            try (MockedStatic<CompletableFuture> cf = mockStatic(CompletableFuture.class)) {
                cf.when(() -> CompletableFuture.runAsync(any(Runnable.class))).thenAnswer(a -> {
                    Runnable argument = a.getArgument(0, Runnable.class);
                    argument.run();
                    return supplyAsync;
                });
                adapter.sendAsync(event, myOpenHookInfoList);
                cf.verify(() -> CompletableFuture.runAsync(any(Runnable.class)), times(0));
                verify(adapter, times(0)).sendAndSave(any(), any());
                verify(supplyAsync, times(0)).thenRunAsync(any(Runnable.class));
            }
        }
    }

    @Nested
    class SendAndSaveTest {
        WebHookInfoDto myOpenHookInfo;

        @BeforeEach
        void init() {
            myOpenHookInfo = new WebHookInfoDto();
            myOpenHookInfo.setId(new ObjectId());
            myOpenHookInfo.setUrl("url");
            when(adapter.send(event, myOpenHookInfo)).thenReturn(send);
            when(webHookHistoryService.pushHistory(anyString(), anyList())).thenReturn(1L);
            send.setStatus(PingResult.SUCCEED.name());
            doNothing().when(log).error(
                    anyString(),
                    anyString(),
                    anyString(),
                    anyString(),
                    any(Exception.class));
            when(adapter.sendAndSave(event, myOpenHookInfo)).thenCallRealMethod();
        }

        @Test
        void testNormal() {
            when(webHookService.updatePingResult(any(WebHookInfoDto.class))).thenReturn(mock(WebHookInfoVo.class));
            HookOneHistory history = new HookOneHistory();
            history.setHookId("6826ee2b872e0755b7b4f83e");
            history.setStatus(PingResult.SUCCEED.name());
            when(adapter.send(event, myOpenHookInfo)).thenReturn(history);
            Assertions.assertDoesNotThrow(() -> adapter.sendAndSave(event, myOpenHookInfo));
            verify(adapter).send(event, myOpenHookInfo);
            verify(webHookHistoryService).pushHistory(anyString(), anyList());
            verify(webHookService).updatePingResult(any(WebHookInfoDto.class));
            verify(log, times(0)).error(
                    anyString(),
                    anyString(),
                    anyString(),
                    anyString(),
                    any(Exception.class));
        }

        @Test
        void testException() {
            when(webHookService.updatePingResult(any(WebHookInfoDto.class))).thenAnswer(a -> {
                throw new Exception("Update failed, connection timeout");
            });
            HookOneHistory history = new HookOneHistory();
            history.setHookId("6826ee2b872e0755b7b4f83e");
            history.setStatus(PingResult.SUCCEED.name());
            when(adapter.send(event, myOpenHookInfo)).thenReturn(history);
            Assertions.assertDoesNotThrow(() -> adapter.sendAndSave(event, myOpenHookInfo));
            verify(adapter).send(event, myOpenHookInfo);
            verify(webHookHistoryService).pushHistory(anyString(), anyList());
            verify(webHookService).updatePingResult(any(WebHookInfoDto.class));
            verify(log, times(1)).error(
                    anyString(),
                    anyString(),
                    anyString(),
                    anyString(),
                    any(Exception.class));
        }

        @Test
        void testWhenSendHookIdIsNull() {
            when(webHookService.updatePingResult(any(WebHookInfoDto.class))).thenReturn(mock(WebHookInfoVo.class));
            Assertions.assertDoesNotThrow(() -> adapter.sendAndSave(event, myOpenHookInfo));
            verify(adapter).send(event, myOpenHookInfo);
            verify(webHookHistoryService, new Times(0)).pushHistory(anyString(), anyList());
            verify(webHookService, new Times(0)).updatePingResult(any(WebHookInfoDto.class));
        }
    }

    @Nested
    class Send2Test {
        String beanName;
        WebHookInfoDto myOpenHookInfo;
        Converter<Object> converter;
        Object eventData;
        HookOneHistory history;

        @BeforeEach
        void init() {
            eventData = new HashMap<>();
            history = new HookOneHistory();
            myOpenHookInfo = new WebHookInfoDto();
            myOpenHookInfo.setUrl("http//:666.666.666.666:666");
            myOpenHookInfo.setId(new ObjectId());

            event.withEvent(eventData).withType("type").withMetric("metric");
            converter = mock(Converter.class);
            beanName = HookType.PING.getHookBeanName();

            when(applicationContext.getBean(anyString(), any(Class.class))).thenReturn(converter);
            when(converter.convert(eventData, myOpenHookInfo, event)).thenReturn(new Object());
            when(converter.analyseHead(myOpenHookInfo)).thenReturn(new HashMap<>());
            when(webHookHttpUtil.getHookOneHistoryByParams(anyString(), anyMap(), anyMap(), any(Object.class))).thenReturn(history);
            when(webHookHttpUtil.post(history)).thenReturn(history);
            when(adapter.send(event, myOpenHookInfo)).thenCallRealMethod();
        }

        @Test
        void testNormal() {
            event.withEvent(eventData).withType(HookType.PING.getHookBeanName()).withMetric("metric");
            HookOneHistory send = adapter.send(event, myOpenHookInfo);
            Assertions.assertNotNull(send);
            verify(applicationContext).getBean(anyString(), any(Class.class));
            verify(converter).convert(eventData, myOpenHookInfo, event);
            verify(converter).analyseHead(myOpenHookInfo);
            verify(webHookHttpUtil).getHookOneHistoryByParams(anyString(), anyMap(), anyMap(), any(Object.class));
            verify(webHookHttpUtil).post(history);
        }

        @Test
        void testNoNeedToSend() {
            event.withEvent(eventData).withType(HookType.PING.getHookBeanName()).withMetric("metric");
            HookOneHistory latestHookEvent = new HookOneHistory();
            latestHookEvent.setRequestBody(TapSimplify.toJson(eventData));
            latestHookEvent.setRequestAt(System.currentTimeMillis());
            when(webHookHistoryService.latestHookEvent(myOpenHookInfo.getId())).thenReturn(latestHookEvent);
            HookOneHistory send = adapter.send(event, myOpenHookInfo);
            Assertions.assertNotNull(send);
            verify(applicationContext).getBean(anyString(), any(Class.class));
            verify(webHookHttpUtil, new Times(0)).getHookOneHistoryByParams(anyString(), anyMap(), anyMap(), any(Object.class));
            verify(webHookHttpUtil, new Times(0)).post(history);
        }
    }

    @Nested
    class GetHookBeanNameByType {
        @Test
        void testTypeIsEmpty() {
            Assertions.assertEquals(HookType.ALTER, WebHookAdapter.getHookBeanNameByType("", ""));
        }

        @Test
        void testAlarmTypeEnum() {
            Assertions.assertEquals(HookType.ALTER, WebHookAdapter.getHookBeanNameByType(AlarmTypeEnum.INSPECT_ALARM.name(), ""));
        }

        @Test
        void testAlarmKeyEnum() {
            Assertions.assertEquals(HookType.ALTER, WebHookAdapter.getHookBeanNameByType("xxx", AlarmKeyEnum.DATANODE_AVERAGE_HANDLE_CONSUME.name()));
        }

        @Test
        void testPing() {
            Assertions.assertEquals(HookType.PING, WebHookAdapter.getHookBeanNameByType(HookType.PING.getHookName(), AlarmKeyEnum.DATANODE_AVERAGE_HANDLE_CONSUME.name()));
        }

        @Test
        void testOther() {
            Assertions.assertEquals(HookType.ALTER, WebHookAdapter.getHookBeanNameByType("xxx", "xxx"));
        }
    }
}