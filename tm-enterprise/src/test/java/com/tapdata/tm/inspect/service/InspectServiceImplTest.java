package com.tapdata.tm.inspect.service;

import cn.hutool.extra.cglib.CglibUtil;
import com.mongodb.client.result.UpdateResult;
import com.tapdata.tm.Settings.service.AlarmSettingService;
import com.tapdata.tm.Settings.service.SettingsService;
import com.tapdata.tm.alarm.service.AlarmService;
import com.tapdata.tm.base.dto.Filter;
import com.tapdata.tm.base.dto.Page;
import com.tapdata.tm.base.dto.Where;
import com.tapdata.tm.commons.dag.nodes.DataParentNode;
import com.tapdata.tm.commons.schema.DataSourceConnectionDto;
import com.tapdata.tm.commons.schema.Field;
import com.tapdata.tm.commons.schema.MetadataInstancesDto;
import com.tapdata.tm.commons.task.dto.TaskDto;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.ds.service.impl.DataSourceService;
import com.tapdata.tm.inspect.bean.Source;
import com.tapdata.tm.inspect.bean.Task;
import com.tapdata.tm.inspect.constant.InspectStatusEnum;
import com.tapdata.tm.inspect.dto.InspectDto;
import com.tapdata.tm.inspect.dto.InspectResultDto;
import com.tapdata.tm.inspect.entity.InspectEntity;
import com.tapdata.tm.inspect.entity.InspectResultEntity;
import com.tapdata.tm.inspect.repository.InspectRepository;
import com.tapdata.tm.message.service.MessageService;
import com.tapdata.tm.messagequeue.dto.MessageQueueDto;
import com.tapdata.tm.messagequeue.service.MessageQueueService;
import com.tapdata.tm.metadatainstance.service.MetadataInstancesService;
import com.tapdata.tm.permissions.DataPermissionHelper;
import com.tapdata.tm.task.service.TaskService;
import com.tapdata.tm.util.BeanUtil;
import com.tapdata.tm.utils.CronUtil;
import com.tapdata.tm.utils.Lists;
import com.tapdata.tm.worker.service.WorkerService;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.*;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URL;
import java.util.*;
import java.util.function.BiConsumer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

public class InspectServiceImplTest {
    InspectServiceImpl impl;
    UserDetail userDetail;

    MessageService messageService;
    SettingsService settingsService;
    InspectDetailsService inspectDetailsService;
    InspectResultService inspectResultService;
    WorkerService workerService;
    AlarmSettingService alarmSettingService;
    AlarmService alarmService;
    MetadataInstancesService metadataInstancesService;
    InspectTaskService inspectTaskService;

    @BeforeEach
    void init() {
        userDetail = mock(UserDetail.class);
        impl = mock(InspectServiceImpl.class);
        messageService = mock(MessageService.class);
        settingsService = mock(SettingsService.class);
        inspectDetailsService = mock(InspectDetailsService.class);
        inspectResultService = mock(InspectResultService.class);
        workerService = mock(WorkerService.class);
        alarmSettingService = mock(AlarmSettingService.class);
        metadataInstancesService = mock(MetadataInstancesService.class);
        inspectTaskService = mock(InspectTaskService.class);

        doCallRealMethod().when(impl).setMessageService(messageService);
        doCallRealMethod().when(impl).setSettingsService(settingsService);
        doCallRealMethod().when(impl).setInspectDetailsService(inspectDetailsService);
        doCallRealMethod().when(impl).setInspectResultService(inspectResultService);
        doCallRealMethod().when(impl).setWorkerService(workerService);
        doCallRealMethod().when(impl).setAlarmSettingService(alarmSettingService);
        doCallRealMethod().when(impl).setAlarmService(alarmService);
        doCallRealMethod().when(impl).setMetadataInstancesService(metadataInstancesService);
        doCallRealMethod().when(impl).setInspectTaskService(inspectTaskService);

        impl.setMessageService(messageService);
        impl.setSettingsService(settingsService);
        impl.setInspectDetailsService(inspectDetailsService);
        impl.setInspectResultService(inspectResultService);
        impl.setWorkerService(workerService);
        impl.setAlarmSettingService(alarmSettingService);
        impl.setAlarmService(alarmService);
        impl.setMetadataInstancesService(metadataInstancesService);
        impl.setInspectTaskService(inspectTaskService);
    }

    @Nested
    class DataPermissionFindByIdTest {
        com.tapdata.tm.base.dto.Field field;
        ObjectId id;

        @BeforeEach
        void init() {
            id = mock(ObjectId.class);
            field = mock(com.tapdata.tm.base.dto.Field.class);

            when(impl.dataPermissionFindById(id, field)).thenCallRealMethod();
            when(impl.dataPermissionFindById(null, field)).thenCallRealMethod();
            when(impl.dataPermissionFindById(id, null)).thenCallRealMethod();
            when(impl.findById(id, field)).thenReturn(mock(InspectDto.class));
            when(impl.findById(any(ObjectId.class), any(com.tapdata.tm.base.dto.Field.class))).thenReturn(mock(InspectDto.class));
        }

        @Test
        void testNormal() {
            Assertions.assertNotNull(impl.dataPermissionFindById(id, field));
        }

        @Test
        void testIdIsNull() {
            Assertions.assertNull(impl.dataPermissionFindById(null, field));
        }

        @Test
        void testFieldIsNull() {
            Assertions.assertNotNull(impl.dataPermissionFindById(id, null));
        }
    }

    @Nested
    class list {
        Filter filter;
        List<InspectEntity> inspectEntities;
        List<InspectDto> inspectDtoList;

        @BeforeEach
        void init() {
            filter = new Filter();
            inspectEntities = new ArrayList<>();
            inspectDtoList = new ArrayList<>();
            inspectDtoList.add(new InspectDto());

            when(userDetail.isRoot()).thenReturn(true);
            when(userDetail.isFreeAuth()).thenReturn(true);
            when(impl.count(any(Query.class))).thenReturn(1L);
            when(userDetail.getUserId()).thenReturn("id");
            when(impl.findAllEntity(any(Query.class))).thenReturn(inspectEntities);
            doNothing().when(impl).joinResult(anyList());

            when(impl.list(filter, userDetail)).thenCallRealMethod();
        }

        @Test
        void testNormal() {
            try (MockedStatic<DataPermissionHelper> dph = mockStatic(DataPermissionHelper.class);
                 MockedStatic<com.tapdata.tm.util.BeanUtil> bu = mockStatic(com.tapdata.tm.util.BeanUtil.class)) {
                bu.when(() -> com.tapdata.tm.util.BeanUtil.deepCloneList(anyList(), any(Class.class), any(BiConsumer.class))).thenReturn(inspectDtoList);
                dph.when(() -> DataPermissionHelper.setFilterConditions(anyBoolean(), any(Query.class), any(UserDetail.class))).thenReturn(true);
                Page<InspectDto> list = impl.list(filter, userDetail);
                Assertions.assertNotNull(list);
                verify(userDetail).isRoot();
                verify(userDetail, times(0)).isFreeAuth();
                verify(impl).count(any(Query.class));
                verify(userDetail, times(0)).getUserId();
                verify(impl, times(0)).joinResult(anyList());
            }
        }

        @Test
        void testNotRoot() {
            when(userDetail.isRoot()).thenReturn(false);
            try (MockedStatic<DataPermissionHelper> dph = mockStatic(DataPermissionHelper.class);
                 MockedStatic<com.tapdata.tm.util.BeanUtil> bu = mockStatic(com.tapdata.tm.util.BeanUtil.class)) {
                bu.when(() -> com.tapdata.tm.util.BeanUtil.deepCloneList(anyList(), any(Class.class), any(BiConsumer.class))).thenReturn(inspectDtoList);
                dph.when(() -> DataPermissionHelper.setFilterConditions(anyBoolean(), any(Query.class), any(UserDetail.class))).thenReturn(true);
                Page<InspectDto> list = impl.list(filter, userDetail);
                Assertions.assertNotNull(list);
                verify(userDetail).isRoot();
                verify(userDetail).isFreeAuth();
                verify(impl).count(any(Query.class));
                verify(userDetail, times(0)).getUserId();
                verify(impl, times(0)).joinResult(anyList());
            }
        }

        @Test
        void testNotFreeAuth() {
            when(userDetail.isRoot()).thenReturn(false);
            when(userDetail.isFreeAuth()).thenReturn(false);
            try (MockedStatic<DataPermissionHelper> dph = mockStatic(DataPermissionHelper.class);
                 MockedStatic<com.tapdata.tm.util.BeanUtil> bu = mockStatic(com.tapdata.tm.util.BeanUtil.class)) {
                bu.when(() -> com.tapdata.tm.util.BeanUtil.deepCloneList(anyList(), any(Class.class), any(BiConsumer.class))).thenReturn(inspectDtoList);
                dph.when(() -> DataPermissionHelper.setFilterConditions(anyBoolean(), any(Query.class), any(UserDetail.class))).thenReturn(true);
                Page<InspectDto> list = impl.list(filter, userDetail);
                Assertions.assertNotNull(list);
                verify(userDetail).isRoot();
                verify(userDetail).isFreeAuth();
                verify(impl).count(any(Query.class));
                verify(userDetail, times(0)).getUserId();
                verify(impl, times(0)).joinResult(anyList());
            }
        }

        @Test
        void testNotDataPermissionHelper() {
            when(userDetail.isRoot()).thenReturn(false);
            when(userDetail.isFreeAuth()).thenReturn(false);
            try (MockedStatic<DataPermissionHelper> dph = mockStatic(DataPermissionHelper.class);
                 MockedStatic<com.tapdata.tm.util.BeanUtil> bu = mockStatic(com.tapdata.tm.util.BeanUtil.class)) {
                bu.when(() -> com.tapdata.tm.util.BeanUtil.deepCloneList(anyList(), any(Class.class), any(BiConsumer.class))).thenReturn(inspectDtoList);
                dph.when(() -> DataPermissionHelper.setFilterConditions(anyBoolean(), any(Query.class), any(UserDetail.class))).thenReturn(false);
                Page<InspectDto> list = impl.list(filter, userDetail);
                Assertions.assertNotNull(list);
                verify(userDetail).isRoot();
                verify(userDetail).isFreeAuth();
                verify(impl).count(any(Query.class));
                verify(userDetail).getUserId();
                verify(impl, times(0)).joinResult(anyList());
            }
        }

        @Test
        void testResultIsEmpty() {
            filter.setSort(Lists.newArrayList("add DESC"));
            inspectDtoList.clear();
            try (MockedStatic<DataPermissionHelper> dph = mockStatic(DataPermissionHelper.class);
                 MockedStatic<com.tapdata.tm.util.BeanUtil> bu = mockStatic(com.tapdata.tm.util.BeanUtil.class)) {
                bu.when(() -> com.tapdata.tm.util.BeanUtil.deepCloneList(anyList(), any(Class.class), any(BiConsumer.class))).thenReturn(inspectDtoList);
                dph.when(() -> DataPermissionHelper.setFilterConditions(anyBoolean(), any(Query.class), any(UserDetail.class))).thenReturn(true);
                Page<InspectDto> list = impl.list(filter, userDetail);
                Assertions.assertNotNull(list);
                verify(userDetail).isRoot();
                verify(userDetail, times(0)).isFreeAuth();
                verify(impl).count(any(Query.class));
                verify(userDetail, times(0)).getUserId();
                verify(impl, times(0)).joinResult(anyList());
            }
        }

        @Test
        void testSort() {
            inspectDtoList.clear();
            try (MockedStatic<DataPermissionHelper> dph = mockStatic(DataPermissionHelper.class);
                 MockedStatic<com.tapdata.tm.util.BeanUtil> bu = mockStatic(com.tapdata.tm.util.BeanUtil.class)) {
                bu.when(() -> com.tapdata.tm.util.BeanUtil.deepCloneList(anyList(), any(Class.class), any(BiConsumer.class))).thenReturn(inspectDtoList);
                dph.when(() -> DataPermissionHelper.setFilterConditions(anyBoolean(), any(Query.class), any(UserDetail.class))).thenReturn(true);
                Page<InspectDto> list = impl.list(filter, userDetail);
                Assertions.assertNotNull(list);
                verify(userDetail).isRoot();
                verify(userDetail, times(0)).isFreeAuth();
                verify(impl).count(any(Query.class));
                verify(userDetail, times(0)).getUserId();
                verify(impl, times(0)).joinResult(anyList());
            }
        }
    }

    @Nested
    class findByTaskIdList {
        List<String> taskIdList;

        @BeforeEach
        void init() {
            taskIdList = new ArrayList<>();
            when(impl.findAll(any(Query.class))).thenReturn(new ArrayList<>());
            when(impl.findByTaskIdList(taskIdList)).thenCallRealMethod();
        }

        @Test
        void testNormal() {
            Assertions.assertDoesNotThrow(() -> impl.findByTaskIdList(taskIdList));
        }
    }

    @Nested
    class deleteByTaskId {
        @BeforeEach
        void init() {
            when(impl.update(any(Query.class), any(Update.class))).thenReturn(mock(UpdateResult.class));
            when(impl.deleteByTaskId("id")).thenCallRealMethod();
        }

        @BeforeEach
        void testNormal() {
            Assertions.assertDoesNotThrow(() -> impl.deleteByTaskId("id"));
        }
    }

    @Nested
    class doExecuteInspect {
        Where where;
        InspectDto updateDto;
        InspectDto r;
        String id;

        @BeforeEach
        void init() {
            r = new InspectDto();
            where = new Where();
            id = where.getOrDefault("id", "").toString();
            updateDto = new InspectDto();
            doNothing().when(impl).update(where, updateDto, userDetail);
            doNothing().when(impl).supplementAlarm(any(InspectDto.class), any(UserDetail.class));
            when(inspectTaskService.inspectTaskRun(where, updateDto, userDetail)).thenReturn(r);
            when(inspectTaskService.inspectTaskStop(id, updateDto, userDetail)).thenReturn(r);
            when(inspectTaskService.inspectTaskError(id, updateDto, userDetail)).thenReturn(r);
            when(inspectTaskService.inspectTaskDone(id, updateDto, userDetail)).thenReturn(r);
            when(impl.doExecuteInspect(where, updateDto, userDetail)).thenCallRealMethod();
        }

        @Test
        void testRun() {
            updateDto.setStatus(InspectStatusEnum.SCHEDULING.getValue());
            Assertions.assertDoesNotThrow(() -> impl.doExecuteInspect(where, updateDto, userDetail));
            verify(inspectTaskService, times(1)).inspectTaskRun(where, updateDto, userDetail);
            verify(inspectTaskService, times(0)).inspectTaskStop(id, updateDto, userDetail);
            verify(inspectTaskService, times(0)).inspectTaskError(id, updateDto, userDetail);
            verify(inspectTaskService, times(0)).inspectTaskDone(id, updateDto, userDetail);
        }

        @Test
        void testStop() {
            updateDto.setStatus(InspectStatusEnum.STOPPING.getValue());
            Assertions.assertDoesNotThrow(() -> impl.doExecuteInspect(where, updateDto, userDetail));
            verify(inspectTaskService, times(0)).inspectTaskRun(where, updateDto, userDetail);
            verify(inspectTaskService, times(1)).inspectTaskStop(id, updateDto, userDetail);
            verify(inspectTaskService, times(0)).inspectTaskError(id, updateDto, userDetail);
            verify(inspectTaskService, times(0)).inspectTaskDone(id, updateDto, userDetail);
        }

        @Test
        void testError() {
            updateDto.setStatus(InspectStatusEnum.ERROR.getValue());
            Assertions.assertDoesNotThrow(() -> impl.doExecuteInspect(where, updateDto, userDetail));
            verify(inspectTaskService, times(0)).inspectTaskRun(where, updateDto, userDetail);
            verify(inspectTaskService, times(0)).inspectTaskStop(id, updateDto, userDetail);
            verify(inspectTaskService, times(1)).inspectTaskError(id, updateDto, userDetail);
            verify(inspectTaskService, times(0)).inspectTaskDone(id, updateDto, userDetail);

        }

        @Test
        void testDone() {
            updateDto.setStatus(InspectStatusEnum.DONE.getValue());
            Assertions.assertDoesNotThrow(() -> impl.doExecuteInspect(where, updateDto, userDetail));
            verify(inspectTaskService, times(0)).inspectTaskRun(where, updateDto, userDetail);
            verify(inspectTaskService, times(0)).inspectTaskStop(id, updateDto, userDetail);
            verify(inspectTaskService, times(0)).inspectTaskError(id, updateDto, userDetail);
            verify(inspectTaskService, times(1)).inspectTaskDone(id, updateDto, userDetail);
        }

        @Test
        void testOther() {
            updateDto.setStatus(InspectStatusEnum.PASSED.getValue());
            Assertions.assertDoesNotThrow(() -> impl.doExecuteInspect(where, updateDto, userDetail));
            verify(inspectTaskService, times(0)).inspectTaskRun(where, updateDto, userDetail);
            verify(inspectTaskService, times(0)).inspectTaskStop(id, updateDto, userDetail);
            verify(inspectTaskService, times(0)).inspectTaskError(id, updateDto, userDetail);
            verify(inspectTaskService, times(0)).inspectTaskDone(id, updateDto, userDetail);
        }
    }

    @Nested
    class findAllByIds {
    }

    @Nested
    class TestImportTask {
        private InspectServiceImpl inspectService;
        private TaskService taskService;
        private DataSourceService dataSourceService;

        @BeforeEach
        void init() {
            inspectService = mock(InspectServiceImpl.class);
            taskService = mock(TaskService.class);
            dataSourceService=mock(DataSourceService.class);
            doCallRealMethod().when(inspectService).importTask(any(), any(), any());
            when(inspectService.copyAndAdjustTask(any(),any())).thenCallRealMethod();
            when(inspectService.copyAndSetAttributes(any(),any())).thenCallRealMethod();
            ReflectionTestUtils.setField(inspectService,"dataSourceService",dataSourceService);
        }
        @DisplayName("test import Migrate inspect task")
        @Test
        void test1() throws IOException {
            ReflectionTestUtils.setField(inspectService,"taskService",taskService);
            TaskDto taskDto = mock(TaskDto.class);
            when(taskDto.getSyncType()).thenReturn(TaskDto.SYNC_TYPE_MIGRATE);
            when(taskService.findById(any(ObjectId.class))).thenReturn(taskDto);

            String sourceConnectionId = "6833dea3c7568322c2077505";
            ObjectId sourceObjectId = new ObjectId(sourceConnectionId);
            DataSourceConnectionDto sourceConnectionDto=new DataSourceConnectionDto();
            sourceConnectionDto.setId(sourceObjectId);
            sourceConnectionDto.setDatabase_type("SYBASE");
            sourceConnectionDto.setCollection_name("sourceSybase");
            when(dataSourceService.findById(sourceObjectId)).thenReturn(sourceConnectionDto);

            DataParentNode source = mock(DataParentNode.class);
            when(source.getConnectionId()).thenReturn(sourceConnectionId);
            when(source.getName()).thenReturn("sourceNodeName");

            String targetConnectionId = "68355709ab9bb35e3414a515";
            DataParentNode target = mock(DataParentNode.class);
            when(target.getConnectionId()).thenReturn(targetConnectionId);
            when(target.getName()).thenReturn("targetNodeName");
            when(taskService.getSourceNode(taskDto)).thenReturn(source);
            when(taskService.getTargetNode(taskDto)).thenReturn(target);

            ObjectId targetObjectId = new ObjectId(targetConnectionId);
            DataSourceConnectionDto targetDatasource=new DataSourceConnectionDto();
            targetDatasource.setId(targetObjectId);
            targetDatasource.setDatabase_type("PostgreSQL");
            targetDatasource.setCollection_name("PG");
            when(dataSourceService.findById(targetObjectId)).thenReturn(targetDatasource);



            URL resource = this.getClass().getClassLoader().getResource("testInpsectMigrate.gz");
            FileInputStream fileInputStream = new FileInputStream(resource.getFile());
            MockMultipartFile mockMultipartFile = new MockMultipartFile("testInpsectMigrate.gz", fileInputStream);
            UserDetail userDetail = mock(UserDetail.class);
            doAnswer(invocation -> {
                InspectDto inspectDto = invocation.getArgument(0);
                Task task = inspectDto.getTasks().get(0);
                Source taskSource = task.getSource();
                Source taskTarget = task.getTarget();
                assertEquals("sourceNodeName",taskSource.getNodeName());
                assertEquals("targetNodeName",taskTarget.getNodeName());
                assertEquals("6833dea3c7568322c2077505",taskSource.getConnectionId());
                assertEquals("68355709ab9bb35e3414a515",taskTarget.getConnectionId());
                System.out.println(inspectDto);
                return null;
            }).when(inspectService).save(any(InspectDto.class), any(UserDetail.class));
            inspectService.importTask(mockMultipartFile,"6837d06ef51bb93030a1abfd",userDetail);

        }
    }


    @Nested
    class TestUpdateById {
        InspectRepository inspectRepository = mock(InspectRepository.class);

        @Test
        void test() {
            doNothing().when(impl).supplementAlarm(any(InspectDto.class), any(UserDetail.class));
            doNothing().when(workerService).scheduleTaskToEngine(any(InspectDto.class), any());
            when(impl.updateByWhere(any(Where.class), any(InspectDto.class), any(UserDetail.class))).thenReturn(0L);
            InspectDto except = new InspectDto();
            except.setStatus("scheduling");
            except.setId(new ObjectId(new Date()));
            except.setInspectMethod("jointField");
            when(inspectTaskService.startInspectTask(except, except.getAgentId())).thenReturn("id");

            when(impl.updateById(any(ObjectId.class), any(InspectDto.class), any(UserDetail.class))).thenCallRealMethod();
            try (MockedStatic<BeanUtils> beanUtilsMockedStatic = Mockito.mockStatic(BeanUtils.class);
                 MockedStatic<CronUtil> cronUtilMockedStatic = Mockito.mockStatic(CronUtil.class);
                 MockedStatic<CglibUtil> cglibUtilMockedStatic = Mockito.mockStatic(CglibUtil.class)) {
                cglibUtilMockedStatic.when(() -> CglibUtil.copy(any(), any())).thenReturn(null);
                beanUtilsMockedStatic.when(() -> BeanUtils.copyProperties(any(), any(), anyString())).thenAnswer(invocationOnMock -> null);
                cronUtilMockedStatic.when(() -> CronUtil.removeJob(any())).thenAnswer(invocationOnMock -> null);
                when(inspectRepository.updateByWhere(any(), any(), any())).thenReturn(mock(UpdateResult.class));
                InspectDto result = impl.updateById(mock(ObjectId.class), except, mock(UserDetail.class));
                assertEquals(except, result);
            }
        }
        @Test
        void testNotSchdule() {
            doNothing().when(impl).supplementAlarm(any(InspectDto.class), any(UserDetail.class));
            doNothing().when(workerService).scheduleTaskToEngine(any(InspectDto.class), any());
            when(impl.updateByWhere(any(Where.class), any(InspectDto.class), any(UserDetail.class))).thenReturn(0L);
            InspectDto except = new InspectDto();
            except.setStatus("stopping");
            except.setId(new ObjectId(new Date()));
            List<Task> list = new ArrayList<>();
            Task task = new Task();
            task.setSource(new Source());
            task.setTarget(new Source());
            list.add(task);
            except.setTasks(list);
            except.setInspectMethod("hash");
            when(inspectTaskService.startInspectTask(except, except.getAgentId())).thenReturn("id");

            when(impl.updateById(any(ObjectId.class), any(InspectDto.class), any(UserDetail.class))).thenCallRealMethod();
            try (MockedStatic<BeanUtils> beanUtilsMockedStatic = Mockito.mockStatic(BeanUtils.class);
                 MockedStatic<CronUtil> cronUtilMockedStatic = Mockito.mockStatic(CronUtil.class);
                 MockedStatic<CglibUtil> cglibUtilMockedStatic = Mockito.mockStatic(CglibUtil.class)) {
                cglibUtilMockedStatic.when(() -> CglibUtil.copy(any(), any())).thenReturn(null);
                beanUtilsMockedStatic.when(() -> BeanUtils.copyProperties(any(), any(), anyString())).thenAnswer(invocationOnMock -> null);
                cronUtilMockedStatic.when(() -> CronUtil.removeJob(any())).thenAnswer(invocationOnMock -> null);
                when(inspectRepository.updateByWhere(any(), any(), any())).thenReturn(mock(UpdateResult.class));
                InspectDto result = impl.updateById(mock(ObjectId.class), except, mock(UserDetail.class));
                assertEquals(except, result);
            }
        }

        @Test
        void testInspectMethodIsNull() {
            doNothing().when(impl).supplementAlarm(any(InspectDto.class), any(UserDetail.class));
            doNothing().when(workerService).scheduleTaskToEngine(any(InspectDto.class), any());
            when(impl.updateByWhere(any(Where.class), any(InspectDto.class), any(UserDetail.class))).thenReturn(0L);
            InspectDto except = new InspectDto();
            except.setStatus("stopping");
            except.setId(new ObjectId(new Date()));
            List<Task> list = new ArrayList<>();
            Task task = new Task();
            task.setSource(new Source());
            task.setTarget(new Source());
            list.add(task);
            except.setTasks(list);
            except.setInspectMethod(null);
            when(inspectTaskService.startInspectTask(except, except.getAgentId())).thenReturn("id");

            when(impl.updateById(any(ObjectId.class), any(InspectDto.class), any(UserDetail.class))).thenCallRealMethod();
            try (MockedStatic<BeanUtils> beanUtilsMockedStatic = Mockito.mockStatic(BeanUtils.class);
                 MockedStatic<CronUtil> cronUtilMockedStatic = Mockito.mockStatic(CronUtil.class);
                 MockedStatic<CglibUtil> cglibUtilMockedStatic = Mockito.mockStatic(CglibUtil.class)) {
                cglibUtilMockedStatic.when(() -> CglibUtil.copy(any(), any())).thenReturn(null);
                beanUtilsMockedStatic.when(() -> BeanUtils.copyProperties(any(), any(), anyString())).thenAnswer(invocationOnMock -> null);
                cronUtilMockedStatic.when(() -> CronUtil.removeJob(any())).thenAnswer(invocationOnMock -> null);
                when(inspectRepository.updateByWhere(any(), any(), any())).thenReturn(mock(UpdateResult.class));
                InspectDto result = impl.updateById(mock(ObjectId.class), except, mock(UserDetail.class));
                assertEquals(except, result);
            }
        }
        @Test
        void testNewTaskListNotEmpty() {
            doNothing().when(impl).supplementAlarm(any(InspectDto.class), any(UserDetail.class));
            doNothing().when(workerService).scheduleTaskToEngine(any(InspectDto.class), any());
            when(impl.updateByWhere(any(Where.class), any(InspectDto.class), any(UserDetail.class))).thenReturn(0L);
            InspectDto except = new InspectDto();
            except.setStatus("stopping");
            except.setInspectMethod("field");
            except.setId(new ObjectId(new Date()));
            List<Task> newTaskList = new ArrayList<>();
            newTaskList.add(null);
            newTaskList.add(new Task());
            Task task = new Task();
            task.setTaskId("id");
            newTaskList.add(task);

            when(inspectTaskService.startInspectTask(except, except.getAgentId())).thenReturn("id");

            when(impl.updateById(any(ObjectId.class), any(InspectDto.class), any(UserDetail.class))).thenCallRealMethod();
            try (MockedStatic<BeanUtils> beanUtilsMockedStatic = Mockito.mockStatic(BeanUtils.class);
                 MockedStatic<CronUtil> cronUtilMockedStatic = Mockito.mockStatic(CronUtil.class);
                 MockedStatic<CglibUtil> cglibUtilMockedStatic = Mockito.mockStatic(CglibUtil.class)) {
                cglibUtilMockedStatic.when(() -> CglibUtil.copy(any(), any())).thenReturn(null);
                beanUtilsMockedStatic.when(() -> BeanUtils.copyProperties(any(), any(), anyString())).thenAnswer(invocationOnMock -> null);
                cronUtilMockedStatic.when(() -> CronUtil.removeJob(any())).thenAnswer(invocationOnMock -> null);
                when(inspectRepository.updateByWhere(any(), any(), any())).thenReturn(mock(UpdateResult.class));
                InspectDto result = impl.updateById(mock(ObjectId.class), except, mock(UserDetail.class));
                assertEquals(except, result);
            }
        }
    }
    @Nested
    class FindLatestInspectResultTest{
        InspectServiceImpl injectServiceImpl;
        InspectRepository inspectRepository;
        InspectResultService inspectResultService;
        @BeforeEach
        void setUp(){
            inspectRepository = mock(InspectRepository.class);
            injectServiceImpl = new InspectServiceImpl(inspectRepository);
            inspectResultService = mock(InspectResultService.class);
            ReflectionTestUtils.setField(injectServiceImpl, "inspectResultService", inspectResultService);
        }
        @Test
        void test_main(){
            MongoTemplate mongoTemplate = mock(MongoTemplate.class);
            when(inspectRepository.getMongoOperations()).thenReturn(mongoTemplate);
            List<String> inspectIdList = new ArrayList<>();
            inspectIdList.add("test1");
            List<InspectResultEntity> list = new ArrayList<>();
            InspectResultEntity inspectResultEntity = new InspectResultEntity();
            inspectResultEntity.setInspect_id("test1");
            inspectResultEntity.setCreateAt(new Date());
            inspectResultEntity.setStatus("done");
            list.add(inspectResultEntity);
            AggregationResults<InspectResultEntity> results = new AggregationResults<>(list,new Document());
            when(mongoTemplate.aggregate(any(Aggregation.class),anyString(), eq(InspectResultEntity.class))).thenReturn(results);
            List<InspectResultDto> inspectResultDtos = BeanUtil.deepCloneList(results.getMappedResults(), InspectResultDto.class);
            when(inspectResultService.findAll(any(Query.class))).thenReturn(inspectResultDtos);
            List<InspectResultDto> result =injectServiceImpl.findLatestInspectResult(inspectIdList);
            assertEquals(list.size(),result.size());
            assertEquals(list.get(0).getInspect_id(),result.get(0).getInspect_id());
            assertEquals(list.get(0).getCreateAt(),result.get(0).getCreateAt());
            assertEquals(list.get(0).getStatus(),result.get(0).getStatus());

        }
    }
    @Nested
    class DeleteTest{
        InspectRepository inspectRepository;
        @BeforeEach
        void setup(){
            inspectRepository = mock(InspectRepository.class);
            ReflectionTestUtils.setField(impl,"repository",inspectRepository);
        }
        @Test
        void test_main(){
            doCallRealMethod().when(impl).delete(anyString(),any());
            when(inspectDetailsService.deleteAll(any())).thenReturn(1L);
            when(inspectResultService.deleteAll(any())).thenReturn(1L);
            MongoTemplate mongoTemplate = mock(MongoTemplate.class);
            when(inspectRepository.getMongoOperations()).thenReturn(mongoTemplate);
            when(mongoTemplate.findById(any(),any())).thenReturn(null);
            Map<String, Long> result =  impl.delete(new ObjectId().toHexString(),mock(UserDetail.class));
            assertEquals(1,result.get("detailNum"));
            assertEquals(1,result.get("resultNum"));
        }

    }

    @Nested
    class startInspectTaskTest {
        @Test
        void testStartInspectTaskWithEx() {
            inspectTaskService = mock(InspectTaskServiceImpl.class);
            MessageQueueService messageQueueService = mock(MessageQueueService.class);
            ReflectionTestUtils.setField(inspectTaskService, "messageQueueService", messageQueueService);
            InspectDto inspectDto = new InspectDto();
            String processId = "111";
            doThrow(RuntimeException.class).when(messageQueueService).sendMessage(any(MessageQueueDto.class));
            doCallRealMethod().when(inspectTaskService).startInspectTask(inspectDto, processId);
            inspectTaskService.startInspectTask(inspectDto, processId);
            assertEquals(InspectStatusEnum.ERROR.getValue(), inspectDto.getStatus());
            assertNotNull(inspectDto.getErrorMsg());
        }
    }
}
