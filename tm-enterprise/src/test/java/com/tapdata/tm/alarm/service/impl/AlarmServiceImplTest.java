package com.tapdata.tm.alarm.service.impl;

import com.tapdata.tm.Settings.dto.MailAccountDto;
import com.tapdata.tm.Settings.service.AlarmSettingService;
import com.tapdata.tm.Settings.service.SettingsService;
import com.tapdata.tm.alarm.constant.AlarmConstant;
import com.tapdata.tm.alarm.constant.AlarmMailTemplate;
import com.tapdata.tm.alarm.dto.AlarmListInfoVo;
import com.tapdata.tm.alarm.dto.AlarmMessageDto;
import com.tapdata.tm.alarm.dto.AlarmTemplateData;
import com.tapdata.tm.alarm.entity.AlarmInfo;
import com.tapdata.tm.alarm.service.AlarmService;
import com.tapdata.tm.base.dto.Page;
import com.tapdata.tm.base.exception.BizException;
import com.tapdata.tm.commons.dag.DAG;
import com.tapdata.tm.commons.task.constant.AlarmKeyEnum;
import com.tapdata.tm.commons.task.constant.NotifyEnum;
import com.tapdata.tm.commons.task.dto.TaskDto;
import com.tapdata.tm.commons.task.dto.alarm.AlarmRuleVO;
import com.tapdata.tm.commons.task.dto.alarm.AlarmSettingDto;
import com.tapdata.tm.commons.task.dto.alarm.AlarmSettingVO;
import com.tapdata.tm.commons.task.dto.alarm.AlarmVO;
import com.tapdata.tm.config.security.SimpleGrantedAuthority;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.events.service.EventsService;
import com.tapdata.tm.inspect.service.InspectService;
import com.tapdata.tm.message.constant.MsgTypeEnum;
import com.tapdata.tm.message.dto.MessageDto;
import com.tapdata.tm.message.service.MessageService;
import com.tapdata.tm.mp.service.MpService;
import com.tapdata.tm.sms.SmsService;
import com.tapdata.tm.task.service.TaskService;
import com.tapdata.tm.user.service.UserService;
import com.tapdata.tm.utils.MailUtils;
import com.tapdata.tm.utils.MessageUtil;
import com.tapdata.tm.utils.MongoUtils;
import com.tapdata.tm.utils.OEMReplaceUtil;
import com.tapdata.tm.webhook.impl.convert.TaskAlterWebHookSender;
import io.tapdata.pdk.core.utils.CommonUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.internal.verification.Times;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.test.util.ReflectionTestUtils;

import static com.tapdata.tm.commons.task.constant.AlarmKeyEnum.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@ExtendWith(MockitoExtension.class)
class AlarmServiceImplTest {

    @Mock
    private MongoTemplate mockMongoTemplate;
    @Mock
    private TaskService mockTaskService;
    @Mock
    private InspectService mockInspectService;
    @Mock
    private AlarmSettingService mockAlarmSettingService;
    @Mock
    private MessageService mockMessageService;
    @Mock
    private SettingsService mockSettingsService;
    @Mock
    private UserService mockUserService;
    @Mock
    private SmsService mockSmsService;
    @Mock
    private MpService mockMpService;
    @Mock
    private MailUtils mockMailUtils;
    @Mock
    private EventsService mockEventsService;

    private AlarmServiceImpl alarmServiceImplUnderTest;

    private Method sendMail;
    @Mock
    TaskAlterWebHookSender taskAlterWebHookSender;


    @BeforeEach
    void setUp() throws NoSuchMethodException {
        alarmServiceImplUnderTest = new AlarmServiceImpl();
        alarmServiceImplUnderTest.setMongoTemplate(mockMongoTemplate);
        alarmServiceImplUnderTest.setTaskService(mockTaskService);
        alarmServiceImplUnderTest.setInspectService(mockInspectService);
        alarmServiceImplUnderTest.setAlarmSettingService(mockAlarmSettingService);
        alarmServiceImplUnderTest.setMessageService(mockMessageService);
        alarmServiceImplUnderTest.setSettingsService(mockSettingsService);
        alarmServiceImplUnderTest.setUserService(mockUserService);
        alarmServiceImplUnderTest.setSmsService(mockSmsService);
        alarmServiceImplUnderTest.setMpService(mockMpService);
        alarmServiceImplUnderTest.setMailUtils(mockMailUtils);
        alarmServiceImplUnderTest.setEventsService(mockEventsService);
        alarmServiceImplUnderTest.setEnableSms(false);
        alarmServiceImplUnderTest.setTaskAlterWebHookSender(taskAlterWebHookSender);
        // 获取类的Class对象
        Class<?> myClass = AlarmServiceImpl.class;
        sendMail = myClass.getDeclaredMethod("sendMail", AlarmInfo.class, AlarmMessageDto.class, UserDetail.class,MessageDto.class, String.class);
        sendMail.setAccessible(true);
    }
    @Test
    void testSendMail() throws InvocationTargetException, IllegalAccessException {
         UserDetail userDetail = new UserDetail("userId", "customerId", "username", "password", "customerType",
                "accessCode", false, false, false, false, Arrays.asList(new SimpleGrantedAuthority("role")));
        userDetail.setEmail("<EMAIL>");
        AlarmMessageDto.builder().agentId("agentId").taskId("taskId").name("name").emailOpen(true);
        MessageDto messageDto = new MessageDto();
        messageDto.setMsg("connected");
        messageDto.setMessageMetadata("{\"name\":\"name\",\"id\":\"id\"}");
        AlarmInfo alarmInfo = new AlarmInfo();
        AlarmSettingDto alarmSettingDto = new AlarmSettingDto();
        alarmSettingDto.setKey(AlarmKeyEnum.SYSTEM_FLOW_EGINGE_UP);
        alarmSettingDto.setOpen(true);
        alarmSettingDto.setNotify(Arrays.asList(NotifyEnum.EMAIL));
        List<AlarmSettingDto> all =  Arrays.asList(alarmSettingDto);
        when(mockSettingsService.isCloud()).thenReturn(true);
        when(mockAlarmSettingService.findAllAlarmSetting(any(UserDetail.class))).thenReturn(all);
        when(mockSettingsService.getMailAccount(null)).thenReturn(mock(MailAccountDto.class));
        sendMail.invoke(alarmServiceImplUnderTest,alarmInfo,AlarmMessageDto.builder().agentId("agentId").taskId("taskId").name("name").emailOpen(true).build(),userDetail,messageDto,"messageId");
        verify(mockMessageService,times(1)).update(Query.query(Criteria.where("_id").is(MongoUtils.toObjectId("messageId"))), Update.update("isSend",true));
    }

    @Test
    void testSendMail_sendingEmailLimit() throws InvocationTargetException, IllegalAccessException {
        UserDetail userDetail = new UserDetail("userId", "customerId", "username", "password", "customerType",
                "accessCode", false, false, false, false, Arrays.asList(new SimpleGrantedAuthority("role")));
        userDetail.setEmail("<EMAIL>");
        AlarmMessageDto.builder().agentId("agentId").taskId("taskId").name("name").emailOpen(true);
        MessageDto messageDto = new MessageDto();
        messageDto.setMsg("connected");
        messageDto.setMessageMetadata("{\"name\":\"name\",\"id\":\"id\"}");
        AlarmInfo alarmInfo = new AlarmInfo();
        AlarmSettingDto alarmSettingDto = new AlarmSettingDto();
        alarmSettingDto.setKey(AlarmKeyEnum.SYSTEM_FLOW_EGINGE_UP);
        alarmSettingDto.setOpen(true);
        alarmSettingDto.setNotify(Arrays.asList(NotifyEnum.EMAIL));
        List<AlarmSettingDto> all =  Arrays.asList(alarmSettingDto);
        when(mockSettingsService.isCloud()).thenReturn(true);
        when(mockAlarmSettingService.findAllAlarmSetting(any(UserDetail.class))).thenReturn(all);
        when(mockMessageService.checkMessageLimit(userDetail)).thenReturn(11L);
        sendMail.invoke(alarmServiceImplUnderTest,alarmInfo,AlarmMessageDto.builder().agentId("agentId").taskId("taskId").name("name").emailOpen(true).build(),userDetail,messageDto,"messageId");
        verify(mockMessageService,times(0)).update(Query.query(Criteria.where("_id").is(null)), Update.update("isSend",true));
    }

    @Test
    void testSendMail_sendingEmailBoundary() throws InvocationTargetException, IllegalAccessException {
        UserDetail userDetail = new UserDetail("userId", "customerId", "username", "password", "customerType",
                "accessCode", false, false, false, false, Arrays.asList(new SimpleGrantedAuthority("role")));
        userDetail.setEmail("<EMAIL>");
        AlarmMessageDto.builder().agentId("agentId").taskId("taskId").name("name").emailOpen(true);
        MessageDto messageDto = new MessageDto();
        messageDto.setMsg("connected");
        messageDto.setMessageMetadata("{\"name\":\"name\",\"id\":\"id\"}");
        AlarmInfo alarmInfo = new AlarmInfo();
        AlarmSettingDto alarmSettingDto = new AlarmSettingDto();
        alarmSettingDto.setKey(AlarmKeyEnum.SYSTEM_FLOW_EGINGE_UP);
        alarmSettingDto.setOpen(true);
        alarmSettingDto.setNotify(Arrays.asList(NotifyEnum.EMAIL));
        List<AlarmSettingDto> all =  Arrays.asList(alarmSettingDto);

        when(mockSettingsService.isCloud()).thenReturn(true);
        when(mockAlarmSettingService.findAllAlarmSetting(any(UserDetail.class))).thenReturn(all);
        when(mockMessageService.checkMessageLimit(userDetail)).thenReturn(Long.valueOf(CommonUtils.getPropertyInt("cloud_mail_limit",10)));
        sendMail.invoke(alarmServiceImplUnderTest,alarmInfo,AlarmMessageDto.builder().agentId("agentId").taskId("taskId").name("name").emailOpen(true).build(),userDetail,messageDto,"messageId");
        verify(mockMessageService,times(0)).update(Query.query(Criteria.where("_id").is(null)), Update.update("isSend",true));
    }
    @Nested
    class CheckOpenTest{
        @Test
        @DisplayName("check open for task stop test")
        void test1(){
            TaskDto taskDto = new TaskDto();
            List<AlarmSettingVO> alarmSettings = new ArrayList<>();
            alarmSettings.add(mock(AlarmSettingVO.class));
            taskDto.setAlarmSettings(alarmSettings);
            taskDto.setDag(mock(DAG.class));
            String nodeId = "111";
            AlarmKeyEnum key = TASK_STATUS_STOP;
            NotifyEnum type = null;
            UserDetail userDetail = mock(UserDetail.class);
            List<AlarmSettingDto> settingDtos = new ArrayList<>();
            AlarmSettingDto alarmSettingDto = mock(AlarmSettingDto.class);
            when(alarmSettingDto.getKey()).thenReturn(key);
            when(alarmSettingDto.isOpen()).thenReturn(true);
            settingDtos.add(alarmSettingDto);
            when(mockAlarmSettingService.findAllAlarmSetting(userDetail)).thenReturn(settingDtos);
            boolean actual = alarmServiceImplUnderTest.checkOpen(taskDto, nodeId, key, type, userDetail);
            assertEquals(true,actual);
        }
        @Test
        @DisplayName("check open for task error test")
        void test2(){
            TaskDto taskDto = new TaskDto();
            List<AlarmSettingVO> alarmSettings = new ArrayList<>();
            alarmSettings.add(mock(AlarmSettingVO.class));
            taskDto.setAlarmSettings(alarmSettings);
            taskDto.setDag(mock(DAG.class));
            String nodeId = "111";
            AlarmKeyEnum key = TASK_STATUS_ERROR;
            NotifyEnum type = null;
            UserDetail userDetail = mock(UserDetail.class);
            boolean actual = alarmServiceImplUnderTest.checkOpen(taskDto, nodeId, key, type, userDetail);
            assertEquals(false,actual);
        }
    }
    @Nested
    class GetTaskTitleAndContentTest{
        private AlarmInfo info;
        private SimpleDateFormat dateFormat;
        private Date date;
        private String exceptedTitle;
        private String exceptedContent;
        private String exceptedSmsEvent;
        private Map<String, String> actual;
        @BeforeEach
        void buildInfo(){
            info = new AlarmInfo();
            info.setName("test task");
            date = new Date();
            info.setLastOccurrenceTime(date);
            dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        }
        @AfterEach
        void buildAssertion(){
            assertEquals(exceptedTitle,actual.get("title"));
            assertEquals(exceptedContent,actual.get("content"));
            assertEquals(exceptedSmsEvent,actual.get("smsEvent"));
        }
        @Test
        @DisplayName("get task title and content for task stop")
        void test1(){
            info.setMetric(TASK_STATUS_STOP);
            actual = alarmServiceImplUnderTest.getTaskTitleAndContent(info);
            exceptedTitle = MessageFormat.format(AlarmMailTemplate.TASK_STATUS_STOP_MANUAL_TITLE, "test task");
            exceptedContent = MessageFormat.format(AlarmMailTemplate.TASK_STATUS_STOP_MANUAL, "test task", dateFormat.format(date));
            exceptedSmsEvent = "任务停止";
        }
        @Test
        @DisplayName("get task title and content for task error")
        void test2(){
            info.setMetric(TASK_STATUS_ERROR);
            actual = alarmServiceImplUnderTest.getTaskTitleAndContent(info);
            exceptedTitle = MessageFormat.format(AlarmMailTemplate.TASK_STATUS_STOP_ERROR_TITLE, "test task");
            exceptedContent = MessageFormat.format(AlarmMailTemplate.TASK_STATUS_STOP_ERROR, "test task", dateFormat.format(date));
            exceptedSmsEvent = "任务错误";
        }
        @Test
        @DisplayName("get task title and content for task full complete")
        void test3(){
            info.setMetric(TASK_FULL_COMPLETE);
            Map<String,Object> map = new HashMap<>();
            map.put("snapDoneDate","test");
            info.setParam(map);
            actual = alarmServiceImplUnderTest.getTaskTitleAndContent(info);
            exceptedTitle = MessageFormat.format(AlarmMailTemplate.TASK_FULL_COMPLETE_TITLE, "test task");
            exceptedContent = MessageFormat.format(AlarmMailTemplate.TASK_FULL_COMPLETE, "test task", "test");
            exceptedSmsEvent = "全量结束";
        }
        @Test
        @DisplayName("get task title and content for task full complete")
        void test4(){
            info.setMetric(TASK_INCREMENT_START);
            Map<String,Object> map = new HashMap<>();
            map.put("cdcTime","test");
            info.setParam(map);
            actual = alarmServiceImplUnderTest.getTaskTitleAndContent(info);
            exceptedTitle = MessageFormat.format(AlarmMailTemplate.TASK_INCREMENT_START_TITLE, "test task");
            exceptedContent = MessageFormat.format(AlarmMailTemplate.TASK_INCREMENT_START, "test task", "test");
            exceptedSmsEvent = "增量开始";
        }
        @Test
        @DisplayName("get task title and content for task increment delay")
        void test5(){
            info.setMetric(TASK_INCREMENT_DELAY);
            Map<String,Object> map = new HashMap<>();
            map.put("currentValue","test");
            info.setParam(map);
            actual = alarmServiceImplUnderTest.getTaskTitleAndContent(info);
            exceptedTitle = MessageFormat.format(AlarmMailTemplate.TASK_INCREMENT_DELAY_START_TITLE, "test task");
            exceptedContent = MessageFormat.format(AlarmMailTemplate.TASK_INCREMENT_DELAY_START, "test task", "test");
            exceptedSmsEvent = "增量延迟";
        }
        @Test
        @DisplayName("get task title and content for data node average handle consume")
        void test6(){
            info.setMetric(DATANODE_AVERAGE_HANDLE_CONSUME);
            Map<String,Object> map = new HashMap<>();
            map.put("currentValue","test");
            info.setParam(map);
            actual = alarmServiceImplUnderTest.getTaskTitleAndContent(info);
            exceptedTitle = MessageFormat.format(AlarmMailTemplate.AVERAGE_HANDLE_CONSUME_TITLE, "test task");
            exceptedContent = MessageFormat.format(AlarmMailTemplate.AVERAGE_HANDLE_CONSUME, "test task",null, "test",null,dateFormat.format(date));
            exceptedSmsEvent = "当前任务运行超过阈值";
        }
        @Test
        @DisplayName("get task title and content for process node average handle consume")
        void test7(){
            info.setMetric(PROCESSNODE_AVERAGE_HANDLE_CONSUME);
            Map<String,Object> map = new HashMap<>();
            map.put("currentValue","test");
            info.setParam(map);
            actual = alarmServiceImplUnderTest.getTaskTitleAndContent(info);
            exceptedTitle = MessageFormat.format(AlarmMailTemplate.AVERAGE_HANDLE_CONSUME_TITLE, "test task");
            exceptedContent = MessageFormat.format(AlarmMailTemplate.AVERAGE_HANDLE_CONSUME, "test task",null,null,"test",dateFormat.format(date));
            exceptedSmsEvent = "当前任务运行超过阈值";
        }
        @Test
        @DisplayName("get task title and content for inspect task error")
        void test8(){
            info.setMetric(INSPECT_TASK_ERROR);
            Map<String,Object> map = new HashMap<>();
            map.put("inspectName","test");
            info.setParam(map);
            actual = alarmServiceImplUnderTest.getTaskTitleAndContent(info);
            exceptedTitle = MessageFormat.format(AlarmMailTemplate.INSPECT_TASK_ERROR_TITLE, "test");
            exceptedContent = MessageFormat.format(AlarmMailTemplate.INSPECT_TASK_ERROR_CONTENT, "test",null);
            exceptedSmsEvent = "校验任务异常";
        }
        @Test
        @DisplayName("get task title and content for inspect count error")
        void test9(){
            info.setMetric(INSPECT_COUNT_ERROR);
            Map<String,Object> map = new HashMap<>();
            map.put("inspectName","test");
            info.setParam(map);
            actual = alarmServiceImplUnderTest.getTaskTitleAndContent(info);
            exceptedTitle = MessageFormat.format(AlarmMailTemplate.INSPECT_COUNT_ERROR_TITLE, "test");
            exceptedContent = MessageFormat.format(AlarmMailTemplate.INSPECT_COUNT_ERROR_CONTENT, "test",null);
            exceptedSmsEvent = "快速count校验不一致告警";
        }
        @Test
        @DisplayName("get task title and content for inspect value error for join")
        void test10(){
            info.setMetric(INSPECT_VALUE_ERROR);
            info.setSummary("INSPECT_VALUE_JOIN_ERROR");
            Map<String,Object> map = new HashMap<>();
            map.put("inspectName","test");
            info.setParam(map);
            actual = alarmServiceImplUnderTest.getTaskTitleAndContent(info);
            exceptedTitle = MessageFormat.format(AlarmMailTemplate.INSPECT_VALUE_ERROR_JOIN_TITLE, "test");
            exceptedContent = MessageFormat.format(AlarmMailTemplate.INSPECT_VALUE_ERROR_JOIN_CONTENT, "test",null);
            exceptedSmsEvent = "关联字段值校验结果不一致告警";
        }
        @Test
        @DisplayName("get task title and content for inspect value error for all")
        void test11(){
            info.setMetric(INSPECT_VALUE_ERROR);
            Map<String,Object> map = new HashMap<>();
            map.put("inspectName","test");
            info.setParam(map);
            actual = alarmServiceImplUnderTest.getTaskTitleAndContent(info);
            exceptedTitle = MessageFormat.format(AlarmMailTemplate.INSPECT_VALUE_ERROR_ALL_TITLE, "test");
            exceptedContent = MessageFormat.format(AlarmMailTemplate.INSPECT_VALUE_ERROR_ALL_CONTENT, "test",null);
            exceptedSmsEvent = "表全字段值校验结果不一致告警";
        }
        @Test
        @DisplayName("get task title and content for default")
        void test12(){
            info.setMetric(SYSTEM_FLOW_EGINGE_UP);
            Map<String,Object> map = new HashMap<>();
            map.put("inspectName","test");
            info.setParam(map);
            actual = alarmServiceImplUnderTest.getTaskTitleAndContent(info);
            exceptedTitle = "test task发生异常";
            exceptedContent = null;
            exceptedSmsEvent = "异常";
        }
    }
    @Nested
    class UpdateTaskAlarmTest{

        @Test
        void testTaskIdIsNull(){
            AlarmVO alarmVO = new AlarmVO();
            alarmVO.setTaskId(null);
            alarmServiceImplUnderTest.updateTaskAlarm(alarmVO);
            verify(mockTaskService,times(0)).update(any(Query.class),any(Update.class));
        }

        @Test
        void testAlarmSettingsIsNull(){
            AlarmVO alarmVO = new AlarmVO();
            alarmVO.setTaskId("test");
            alarmVO.setAlarmSettings(new ArrayList<>());
            alarmServiceImplUnderTest.updateTaskAlarm(alarmVO);
            verify(mockTaskService,times(0)).update(any(Query.class),any(Update.class));
        }

        @Test
        void testAlarmRulesIsNull(){
            AlarmVO alarmVO = new AlarmVO();
            alarmVO.setTaskId("test");
            AlarmSettingVO alarmSettingVO = new AlarmSettingVO();
            List<AlarmSettingVO> list = new ArrayList<>();
            list.add(alarmSettingVO);
            alarmVO.setAlarmSettings(list);
            alarmVO.setAlarmRules(null);
            alarmServiceImplUnderTest.updateTaskAlarm(alarmVO);
            verify(mockTaskService,times(0)).update(any(Query.class),any(Update.class));
        }

        @Test
        void testTaskAlarm(){
            AlarmVO alarmVO = new AlarmVO();
            alarmVO.setTaskId(new ObjectId().toHexString());
            AlarmSettingVO alarmSettingVO = new AlarmSettingVO();
            List<AlarmSettingVO> alarmSettingVOListst = new ArrayList<>();
            alarmSettingVOListst.add(alarmSettingVO);
            AlarmRuleVO alarmRuleVO = new AlarmRuleVO();
            List<AlarmRuleVO> alarmRuleVOS = new ArrayList<>();
            alarmRuleVOS.add(alarmRuleVO);
            List<String> emailReceivers = new ArrayList<>();
            emailReceivers.add("<EMAIL>");
            alarmVO.setAlarmSettings(alarmSettingVOListst);
            alarmVO.setAlarmRules(alarmRuleVOS);
            alarmVO.setEmailReceivers(emailReceivers);
            alarmServiceImplUnderTest.updateTaskAlarm(alarmVO);
            verify(mockTaskService,times(1)).update(any(Query.class),any(Update.class));
        }

        @Test
        void testTaskNodeAlarm(){
            AlarmVO alarmVO = new AlarmVO();
            alarmVO.setNodeId("tst");
            alarmVO.setTaskId(new ObjectId().toHexString());
            AlarmSettingVO alarmSettingVO = new AlarmSettingVO();
            List<AlarmSettingVO> alarmSettingVOListst = new ArrayList<>();
            alarmSettingVOListst.add(alarmSettingVO);
            AlarmRuleVO alarmRuleVO = new AlarmRuleVO();
            List<AlarmRuleVO> alarmRuleVOS = new ArrayList<>();
            alarmRuleVOS.add(alarmRuleVO);
            List<String> emailReceivers = new ArrayList<>();
            emailReceivers.add("<EMAIL>");
            alarmVO.setEmailReceivers(emailReceivers);
            alarmVO.setAlarmSettings(alarmSettingVOListst);
            alarmVO.setAlarmRules(alarmRuleVOS);
            alarmVO.setEmailReceivers(emailReceivers);
            alarmServiceImplUnderTest.updateTaskAlarm(alarmVO);
            verify(mockTaskService,times(1)).update(any(Query.class),any(Update.class));
        }


    }

    @Nested
    class SaveTest{
        AlarmServiceImpl alarmService;
        TaskAlterWebHookSender sender;
        AlarmInfo info;
        @BeforeEach
        void init() {
            alarmService = mock(AlarmServiceImpl.class);
            info = mock(AlarmInfo.class);
            sender = mock(TaskAlterWebHookSender.class);
            doCallRealMethod().when(alarmService).setTaskAlterWebHookSender(sender);
            alarmService.setTaskAlterWebHookSender(sender);
            doNothing().when(sender).send(info);
            doNothing().when(alarmService).saveInfo(info);
            doCallRealMethod().when(alarmService).save(info);
        }
        @Test
        void testNormal() {
            Assertions.assertDoesNotThrow(() -> alarmService.save(info));
            verify(alarmService).saveInfo(info);
            verify(sender).send(info);
        }
    }
    @Nested
    class sendMailTest {
        private AlarmInfo info;
        private AlarmMessageDto alarmMessageDto;
        private UserDetail userDetail;
        private MessageDto messageDto;
        private String messageId;

        @BeforeEach
        void setUp() {
            info = new AlarmInfo();
            info.setTaskId("67ab160e294cdc56b29d6cc8");
            alarmMessageDto = AlarmMessageDto.builder().build();
            userDetail = mock(UserDetail.class);
            messageDto = new MessageDto();
            messageId = "testMessageId";
            alarmMessageDto.setEmailOpen(true);
        }

        @Test
        void testSendMail_WithMessageDto() {
            MailAccountDto mailAccount = new MailAccountDto("127.0.0.1", 467, "<EMAIL>", "<EMAIL>", "password", Arrays.asList("<EMAIL>"), "SSL", "", 0);

            try (MockedStatic<MailUtils> mb = Mockito
                    .mockStatic(MailUtils.class)) {
                mb.when(()->MailUtils.sendHtmlEmail(any(), eq(mailAccount.getReceivers()), anyString(), anyString())).thenReturn(null);
                messageDto.setUserId("111");
                messageDto.setMsg(MsgTypeEnum.CONNECTED.getValue());
                when(mockSettingsService.getMailAccount(anyString())).thenReturn(mailAccount);
                when(mockSettingsService.isCloud()).thenReturn(false);
                TaskDto taskDto = new TaskDto();
                taskDto.setEmailReceivers(Arrays.asList("<EMAIL>"));
                when(mockTaskService.findByTaskId(any(ObjectId.class), eq("emailReceivers"))).thenReturn(taskDto);
                List<AlarmSettingDto> alarmSettingDtoList = new ArrayList<>();
                AlarmSettingDto alarmSettingDto = new AlarmSettingDto();
                alarmSettingDto.setKey(SYSTEM_FLOW_EGINGE_UP);
                alarmSettingDto.setOpen(true);
                alarmSettingDto.setNotify(Arrays.asList(NotifyEnum.EMAIL));
                alarmSettingDtoList.add(alarmSettingDto);
                when(mockAlarmSettingService.findAllAlarmSetting(any(UserDetail.class))).thenReturn(alarmSettingDtoList);
                boolean result = alarmServiceImplUnderTest.sendMail(info, alarmMessageDto, userDetail, messageDto, messageId);
                mb.verify(() -> MailUtils.sendHtmlEmail(any(), eq(mailAccount.getReceivers()), anyString(), anyString()),new Times(1));
            }
        }
        @Test
        void testSendMail_WithoutEmailReceivers() {
            MailAccountDto mailAccount = new MailAccountDto("127.0.0.1", 467, "<EMAIL>", "<EMAIL>", "password", Arrays.asList("<EMAIL>"), "SSL", "", 0);

            try (MockedStatic<MailUtils> mb = Mockito
                    .mockStatic(MailUtils.class)) {
                mb.when(()->MailUtils.sendHtmlEmail(any(), eq(mailAccount.getReceivers()), anyString(), anyString())).thenReturn(null);
                messageDto.setUserId("111");
                messageDto.setMsg(MsgTypeEnum.CONNECTED.getValue());
                when(mockSettingsService.getMailAccount(anyString())).thenReturn(mailAccount);
                when(mockSettingsService.isCloud()).thenReturn(false);
                TaskDto taskDto = new TaskDto();
                when(mockTaskService.findByTaskId(any(ObjectId.class), eq("emailReceivers"))).thenReturn(taskDto);
                List<AlarmSettingDto> alarmSettingDtoList = new ArrayList<>();
                AlarmSettingDto alarmSettingDto = new AlarmSettingDto();
                alarmSettingDto.setKey(SYSTEM_FLOW_EGINGE_UP);
                alarmSettingDto.setOpen(true);
                alarmSettingDto.setNotify(Arrays.asList(NotifyEnum.EMAIL));
                alarmSettingDtoList.add(alarmSettingDto);
                when(mockAlarmSettingService.findAllAlarmSetting(any(UserDetail.class))).thenReturn(alarmSettingDtoList);
                boolean result = alarmServiceImplUnderTest.sendMail(info, alarmMessageDto, userDetail, messageDto, messageId);
                mb.verify(() -> MailUtils.sendHtmlEmail(any(), eq(mailAccount.getReceivers()), anyString(), anyString()),new Times(1));
            }
        }
        @Test
        void testSendMail_WithTaskDtoIsNull() {
            MailAccountDto mailAccount = new MailAccountDto("127.0.0.1", 467, "<EMAIL>", "<EMAIL>", "password", Arrays.asList("<EMAIL>"), "SSL", "", 0);

            try (MockedStatic<MailUtils> mb = Mockito
                    .mockStatic(MailUtils.class)) {
                mb.when(()->MailUtils.sendHtmlEmail(any(), eq(mailAccount.getReceivers()), anyString(), anyString())).thenReturn(null);
                messageDto.setUserId("111");
                messageDto.setMsg(MsgTypeEnum.CONNECTED.getValue());
                when(mockSettingsService.getMailAccount(anyString())).thenReturn(mailAccount);
                when(mockSettingsService.isCloud()).thenReturn(false);
                when(mockTaskService.findByTaskId(any(ObjectId.class), eq("emailReceivers"))).thenReturn(null);
                List<AlarmSettingDto> alarmSettingDtoList = new ArrayList<>();
                AlarmSettingDto alarmSettingDto = new AlarmSettingDto();
                alarmSettingDto.setKey(SYSTEM_FLOW_EGINGE_UP);
                alarmSettingDto.setOpen(true);
                alarmSettingDto.setNotify(Arrays.asList(NotifyEnum.EMAIL));
                alarmSettingDtoList.add(alarmSettingDto);
                when(mockAlarmSettingService.findAllAlarmSetting(any(UserDetail.class))).thenReturn(alarmSettingDtoList);
                boolean result = alarmServiceImplUnderTest.sendMail(info, alarmMessageDto, userDetail, messageDto, messageId);
                mb.verify(() -> MailUtils.sendHtmlEmail(any(), eq(mailAccount.getReceivers()), anyString(), anyString()),new Times(1));
            }
        }
    }
    @Test
    void testList(){
        try (MockedStatic<AlarmListInfoVo> mb = Mockito
                .mockStatic(AlarmListInfoVo.class)) {
            mb.when(AlarmListInfoVo::builder).thenReturn(mock(AlarmListInfoVo.AlarmListInfoVoBuilder.class));
            when(mockMongoTemplate.count(any(Query.class), any(Class.class))).thenReturn(1L);
            List<AlarmInfo> alarmInfos = new ArrayList<>();
            alarmInfos.add(new AlarmInfo());
            when(mockMongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(alarmInfos);
            alarmServiceImplUnderTest.list(null, null, null, null, 1, 1, null, null);
            mb.verify(AlarmListInfoVo::builder,new Times(0));
        }
    }
    @Nested
    class TestRenderTemplate{
        private AlarmServiceImpl alarmService;
        @BeforeEach
        void beforeEach(){
            alarmService = mock(AlarmServiceImpl.class);
            when(alarmService.renderTemplate(anyString(), anyMap())).thenCallRealMethod();
        }
        @DisplayName("Test template replacement with {xxx} parameter")
        @Test
        void testRenderTemplate(){
            Map<String, Object> params = new HashMap<>();
            params.put("taskName", "task1");
            String template = "The Task Name is {taskName}";
            String renderTemplate = alarmService.renderTemplate(template, params);
            assertEquals("The Task Name is task1",renderTemplate);
        }
        @DisplayName("There is no {xxxx} template replacement in the test ")
        @Test
        void testRenderTemplate2(){
            Map<String, Object> params = new HashMap<>();
            params.put("taskName", "task1");
            String template = "The Task Name is taskName";
            String renderTemplate = alarmService.renderTemplate(template, params);
            assertEquals("The Task Name is taskName",renderTemplate);
        }
        @DisplayName("The test does not provide template arguments")
        @Test
        void testRenderTemplate3(){
            Map<String, Object> params = new HashMap<>();
            String template = "The Task Name is taskName";
            String renderTemplate = alarmService.renderTemplate(template, params);
            assertEquals("The Task Name is taskName",renderTemplate);
        }
        @DisplayName("The test params value is null,no replace")
        @Test
        void testRenderTemplate4(){
            Map<String, Object> params = new HashMap<>();
            params.put("taskName",null);
            String template = "The Task Name is {taskName}";
            String renderTemplate = alarmService.renderTemplate(template, params);
            assertEquals("The Task Name is {taskName}",renderTemplate);
        }
    }
    @Nested
    class findAllParamsTest{
        AlarmServiceImpl alarmService;

        @BeforeEach
        void init() {
            alarmService = mock(AlarmServiceImpl.class);
            doCallRealMethod().when(alarmService).findAllParams(any(), any());
        }

        @Test
        @DisplayName("TASK_STATUS_ERROR: should set ERROR_TIME and ERROR_LOG")
        void testFindAllParams_TASK_STATUS_ERROR() {
            AlarmInfo info = createAlarmInfo(AlarmKeyEnum.TASK_STATUS_ERROR);
            info.getParam().put(AlarmConstant.ERROR_EVENT, "Exception details");
            AlarmTemplateData templateData = new AlarmTemplateData();

            alarmService.findAllParams(info, templateData);

            assertEquals("Exception details", info.getParam().get(AlarmConstant.ERROR_LOG));
            assertNotNull(info.getParam().get(AlarmConstant.ERROR_TIME));
            assertEquals("Test Task", info.getParam().get(AlarmConstant.TASK_NAME));
        }

        @Test
        @DisplayName("TASK_FULL_COMPLETE: should set COMPLETION_TIME")
        void testFindAllParams_TASK_FULL_COMPLETE() {
            AlarmInfo info = createAlarmInfo(AlarmKeyEnum.TASK_FULL_COMPLETE);
            info.getParam().put(AlarmConstant.SNAP_DONE_DATE, "2024-05-30 12:00");
            AlarmTemplateData templateData = new AlarmTemplateData();

            alarmService.findAllParams(info, templateData);

            assertEquals("2024-05-30 12:00", info.getParam().get(AlarmConstant.COMPLETION_TIME));
        }

        @Test
        @DisplayName("TASK_INCREMENT_DELAY: should set DELAY_TIME")
        void testFindAllParams_TASK_INCREMENT_DELAY() {
            AlarmInfo info = createAlarmInfo(AlarmKeyEnum.TASK_INCREMENT_DELAY);
            info.getParam().put(AlarmConstant.CURRENT_VALUE, "5 minutes");
            AlarmTemplateData templateData = new AlarmTemplateData();

            alarmService.findAllParams(info, templateData);

            assertEquals("5 minutes", info.getParam().get(AlarmConstant.DELAY_TIME));
        }

        @Test
        @DisplayName("TASK_STATUS_STOP: should set STOP_TIME")
        void testFindAllParams_TASK_STATUS_STOP() {
            AlarmInfo info = createAlarmInfo(AlarmKeyEnum.TASK_STATUS_STOP);
            AlarmTemplateData templateData = new AlarmTemplateData();

            alarmService.findAllParams(info, templateData);

            assertNotNull(info.getParam().get(AlarmConstant.STOP_TIME));
        }

        @Test
        @DisplayName("DATANODE_AVERAGE_HANDLE_CONSUME: should set NODE_NAME, OCCURRED_TIME, and COST_TIME")
        void testFindAllParams_DATANODE_AVERAGE_HANDLE_CONSUME() {
            AlarmInfo info = createAlarmInfo(AlarmKeyEnum.DATANODE_AVERAGE_HANDLE_CONSUME);
            info.setNode("NodeA");
            info.getParam().put(AlarmConstant.CURRENT_VALUE, "123ms");
            AlarmTemplateData templateData = new AlarmTemplateData();

            alarmService.findAllParams(info, templateData);

            assertEquals("NodeA", info.getParam().get(AlarmConstant.NODE_NAME));
            assertEquals("123ms", info.getParam().get(AlarmConstant.COST_TIME));
            assertNotNull(info.getParam().get(AlarmConstant.OCCURRED_TIME));
        }

        @Test
        @DisplayName("INSPECT_TASK_ERROR: should set TASK_NAME and ERROR_TIME")
        void testFindAllParams_INSPECT_TASK_ERROR() {
            AlarmInfo info = createAlarmInfo(AlarmKeyEnum.INSPECT_TASK_ERROR);
            info.getParam().put(AlarmConstant.INSPECT_NAME, "Inspection Task A");
            info.getParam().put(AlarmConstant.ALARM_DATE, "2024-05-30 11:11");
            AlarmTemplateData templateData = new AlarmTemplateData();

            alarmService.findAllParams(info, templateData);

            assertEquals("Inspection Task A", info.getParam().get(AlarmConstant.TASK_NAME));
            assertEquals("2024-05-30 11:11", info.getParam().get(AlarmConstant.ERROR_TIME));
        }

        @Test
        @DisplayName("INSPECT_COUNT_ERROR: should set TASK_NAME")
        void testFindAllParams_INSPECT_COUNT_ERROR() {
            AlarmInfo info = createAlarmInfo(AlarmKeyEnum.INSPECT_COUNT_ERROR);
            info.getParam().put(AlarmConstant.INSPECT_NAME, "Inspection Task B");
            AlarmTemplateData templateData = new AlarmTemplateData();

            alarmService.findAllParams(info, templateData);

            assertEquals("Inspection Task B", info.getParam().get(AlarmConstant.TASK_NAME));
        }

        @Test
        @DisplayName("Unknown alarm type: should set default Title and Content")
        void testFindAllParams_default() {
            AlarmInfo info = createAlarmInfo(SYSTEM_FLOW_EGINGE_DOWN);
            info.setSummary("Task $taskName encountered an exception");
            AlarmTemplateData templateData = new AlarmTemplateData();

            alarmService.findAllParams(info, templateData);

            assertEquals("Test Task exception occurs", templateData.getTitle());
            assertEquals("Task Test Task encountered an exception", templateData.getContent());
        }

        private AlarmInfo createAlarmInfo(AlarmKeyEnum metric) {
            AlarmInfo info = new AlarmInfo();
            info.setMetric(metric);
            info.setName("Test Task");
            info.setLastOccurrenceTime(new Date());
            info.setParam(new HashMap<>());
            return info;
        }
    }
    @Nested
    class TestGetTaskTitleAndContentByTemplate {


        private AlarmSettingService alarmSettingService;
        private AlarmServiceImpl alarmService;

        @BeforeEach
        void setUp() {
            alarmService = mock(AlarmServiceImpl.class);
            alarmSettingService = mock(AlarmSettingService.class);
            ReflectionTestUtils.setField(alarmService,"alarmSettingService",alarmSettingService);
            when(alarmService.getTaskTitleAndContentByTemplate(any(), any())).thenCallRealMethod();
            when(alarmService.renderTemplate(anyString(),anyMap())).thenCallRealMethod();
            doCallRealMethod().when(alarmService).findAllParams(any(),any());
        }

        @Test
        @DisplayName("Test getTaskTitleAndContentByTemplate normal renderTemplate")
        void testGetTaskTitleAndContentByTemplate() {
            // 准备数据
            AlarmInfo info = new AlarmInfo();
            info.setMetric(AlarmKeyEnum.TASK_STATUS_ERROR);
            info.setName("测试任务");
            info.setSummary("任务 $taskName 出现异常");
            info.setLastOccurrenceTime(new Date());
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(AlarmConstant.ERROR_EVENT, "异常详情");
            info.setParam(paramMap);
            info.setNode("节点A");

            UserDetail userDetail = mock(UserDetail.class);

            AlarmSettingDto settingDto = new AlarmSettingDto();
            settingDto.setEmailAlarmContent("任务 {taskName} 在 {errorTime} 出错，日志：{errorLog}");
            settingDto.setEmailAlarmTitle("告警：{taskName} 出现异常");

            when(alarmSettingService.findByKey(AlarmKeyEnum.TASK_STATUS_ERROR, userDetail))
                    .thenReturn(settingDto);

            Map<String, Object> oemConfig = new HashMap<>();

            try (MockedStatic<OEMReplaceUtil> mockedOEMUtil = Mockito.mockStatic(OEMReplaceUtil.class)) {
                mockedOEMUtil.when(() -> OEMReplaceUtil.getOEMConfigMap("email/replace.json"))
                        .thenReturn(oemConfig);
                mockedOEMUtil.when(() -> OEMReplaceUtil.replace(anyString(), anyMap()))
                        .thenAnswer(invocation -> invocation.getArgument(0));

                AlarmTemplateData result = alarmService.getTaskTitleAndContentByTemplate(info, userDetail);

                String errorTime = (String) info.getParam().get(AlarmConstant.ERROR_TIME);
                assertNotNull(errorTime);
                assertEquals("异常详情", info.getParam().get(AlarmConstant.ERROR_LOG));

                assertEquals("告警：测试任务 出现异常", result.getTitle());
                assertEquals("任务 测试任务 在 " + errorTime + " 出错，日志：异常详情", result.getContent());
            }
        }
    }
}
