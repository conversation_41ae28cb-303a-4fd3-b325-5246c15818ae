<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.5</version>
        <relativePath/>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.tapdata</groupId>
    <artifactId>iengine</artifactId>
    <version>0.5.2-SNAPSHOT</version>
    <name>IEngine Parent POM</name>
    <packaging>pom</packaging>

    <properties>
        <graalvm.py.artifactId>jython-standalone</graalvm.py.artifactId>

        <!-- Instruct the build to use only UTF-8 encoding for source code -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>

        <!-- Kafka and it's dependencies MUST reflect what the Kafka version uses -->
        <version.kafka>3.7.2</version.kafka>
        <version.kafka.scala>2.13</version.kafka.scala>
        <version.curator>2.11.0</version.curator>
        <version.zookeeper>3.8.4</version.zookeeper>
        <version.jackson>2.18.3</version.jackson>
        <version.jackson.databind>2.18.3</version.jackson.databind>
        <!--    <version.org.slf4j>1.7.26</version.org.slf4j>-->
        <!-- check new release version at https://github.com/confluentinc/schema-registry/releases -->
        <version.confluent.platform>5.1.2</version.confluent.platform>

        <!-- Databases -->
        <version.mysql.server>5.7</version.mysql.server>
        <version.hana.driver>2.3.58</version.hana.driver>
        <version.mysql.binlog>0.28.0</version.mysql.binlog>
        <version.mongo.server>3.2.12</version.mongo.server>
        <version.mongo.driver>5.2.1</version.mongo.driver>
        <!--        <version.oracle.driver>11.2.0.3</version.oracle.driver>-->
        <!--        <version.oracle.driver>12.1.0.2.0</version.oracle.driver>-->
        <version.oracle.driver>19.3.0.0.0</version.oracle.driver>
        <version.mssql.driver>9.4.0.jre8</version.mssql.driver>
        <version.spring>6.2.2</version.spring>

        <!-- Connectors -->
        <version.com.google.protobuf>4.29.3</version.com.google.protobuf>

        <!-- Testing -->
        <version.junit>4.12</version.junit>
        <version.fest>1.4</version.fest>

        <!-- Maven Plugins -->
        <version.resources.plugin>2.7</version.resources.plugin>
        <version.dependency.plugin>2.10</version.dependency.plugin>
        <version.assembly.plugin>2.4</version.assembly.plugin>
        <version.war.plugin>2.5</version.war.plugin>
        <version.codehaus.helper.plugin>1.8</version.codehaus.helper.plugin>
        <version.google.formatter.plugin>0.3.1</version.google.formatter.plugin>
        <version.docker.maven.plugin>0.20.1</version.docker.maven.plugin>
        <version.staging.plugin>1.6.3</version.staging.plugin>
        <version.protoc.maven.plugin>*******</version.protoc.maven.plugin>
        <jdbc.sybase.version>7.0</jdbc.sybase.version>

        <!-- Dockerfiles -->
        <docker.maintainer>Debezium community</docker.maintainer>

        <!-- Protobuf compiler options -->
        <protobuf.input.directory>${project.basedir}/src/main/proto</protobuf.input.directory>
        <protobuf.output.directory>${project.basedir}/generated-sources</protobuf.output.directory>

        <!--Skip long running tests by default-->
        <skipLongRunningTests>true</skipLongRunningTests>

        <!-- Don't skip integration tests by default -->
        <skipITs>false</skipITs>

        <!-- No debug options by default -->
        <debug.argline/>

        <!-- Java implementation of cryptographic algorithms -->
        <org.bouncycastle.version>1.80</org.bouncycastle.version>

        <!-- json lib -->
        <org.json.version>20250107</org.json.version>
        <version>2.12.1</version>

        <!-- apache commons net -->
        <commons.net.version>3.11.1</commons.net.version>

        <dom4j.version>2.1.4</dom4j.version>

        <avro.version>1.12.0</avro.version>

        <mq.version>2.21.74</mq.version>

        <jcifs.version>1.3.17</jcifs.version>

        <gbasedbt.version>3.3.0</gbasedbt.version>

        <version.hamcrest>1.3</version.hamcrest>

        <version.db2.driver>4.23.42</version.db2.driver>

        <version.postgresql.driver>42.2.14</version.postgresql.driver>

        <!-- ANTLR -->
        <antlr.version>4.7</antlr.version>

        <!-- jsqlparser -->
        <jsqlparser.version>4.3</jsqlparser.version>


        <jts.version>1.13</jts.version>

        <!-- hazelcast -->
        <!-- original hazelcast -->
        <!--    <hazelcast.version>5.0.2</hazelcast.version>-->
        <!-- hazelcast with tapdata persistence -->
        <hazelcast.version>5.5.0</hazelcast.version>
        <hazelcast-persistence.version>5.5.0-SNAPSHOT</hazelcast-persistence.version>

        <protobuf-dynamic.version>1.0.1</protobuf-dynamic.version>

        <tm-sdk.version>1.0.4-SNAPSHOT</tm-sdk.version>
        <tm-common.version>0.0.3-SNAPSHOT</tm-common.version>

        <okhttp.version>4.12.0</okhttp.version>

        <graalvm.js.version>24.1.2</graalvm.js.version>
        <graalvm.py.version>2.7.4</graalvm.py.version>

        <jna.version>5.10.0</jna.version>
        <log4j.version>2.17.1</log4j.version>

        <hutool.version>5.8.36</hutool.version>

        <!-- 依赖关系：compile,provided,runtime,test -->
        <scope.libs.db2>compile</scope.libs.db2>
        <scope.libs.clickhouse>compile</scope.libs.clickhouse>
        <scope.libs.custom>compile</scope.libs.custom>
        <scope.libs.gbasedbt>compile</scope.libs.gbasedbt>
        <scope.libs.hana>compile</scope.libs.hana>
        <scope.libs.hbase>compile</scope.libs.hbase>
        <scope.libs.hive>compile</scope.libs.hive>
        <scope.libs.jira>compile</scope.libs.jira>
        <scope.libs.kafka>compile</scope.libs.kafka>
        <scope.libs.kudu>compile</scope.libs.kudu>
        <scope.libs.sybase>compile</scope.libs.sybase>
        <scope.libs.tidb>compile</scope.libs.tidb>
        <scope.libs.udp>compile</scope.libs.udp>

        <!-- Debezium Artifacts -->
        <version.debezium.core.new>2.0.0</version.debezium.core.new>
        <version.debezium.embedded.new>2.0.1</version.debezium.embedded.new>
        <version.debezium.connector.postgres>2.0.0</version.debezium.connector.postgres>
        <version.debezium.connector.mysql>2.0.0</version.debezium.connector.mysql>
        <!-- lombok -->
        <lombok.version>1.18.36</lombok.version>

        <error-code-core.version>2.1-SNAPSHOT</error-code-core.version>
        <pdk-error-code.version>2.1-SNAPSHOT</pdk-error-code.version>
        <error-code-scanner.version>2.1-SNAPSHOT</error-code-scanner.version>
        <lucene-core.version>9.12.1</lucene-core.version>
        <junit-jupiter.version>5.8.1</junit-jupiter.version>
        <mockito.version>4.11.0</mockito.version>
        <iengine.impl.artifactId>iengine-oss</iengine.impl.artifactId>
        <iengine.oss.module>iengine-oss</iengine.oss.module>
        <tapdata-api.version>2.0.1-SNAPSHOT</tapdata-api.version>
        <modules-api.version>2.1-SNAPSHOT</modules-api.version>
        <websocket-client-module.version>2.1-SNAPSHOT</websocket-client-module.version>
        <service-skeleton-module.version>2.1-SNAPSHOT</service-skeleton-module.version>
        <tapdata-storage-module.version>2.1-SNAPSHOT</tapdata-storage-module.version>
        <tapdata-pdk-runner.version>2.1-SNAPSHOT</tapdata-pdk-runner.version>
        <tapdata-common.version>2.1-SNAPSHOT</tapdata-common.version>
        <async-tools-module.version>2.1-SNAPSHOT</async-tools-module.version>
        <script-engine-module.version>2.1-SNAPSHOT</script-engine-module.version>
        <gson.version>2.12.1</gson.version>
        <netty.version>4.1.119.Final</netty.version>
        <httpclient5.version>5.4.4</httpclient5.version>
    </properties>

    <modules>
        <module>iengine-common</module>
        <module>api</module>
        <module>${iengine.oss.module}</module>
        <module>iengine-app</module>
        <module>validator</module>
        <module>modules</module>
    </modules>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.tapdata</groupId>
                <artifactId>iengine-oss</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tapdata</groupId>
                <artifactId>iengine-enterprise</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- Major dependencies -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${version.jackson}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${version.jackson}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${version.jackson.databind}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-xml</artifactId>
                <version>${version.jackson}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${version.jackson}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.woodstox</groupId>
                <artifactId>woodstox-core</artifactId>
                <version>7.1.0</version>
            </dependency>


            <!-- Kafka Connect -->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-api</artifactId>
                <version>${version.kafka}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-runtime</artifactId>
                <version>${version.kafka}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-json</artifactId>
                <version>${version.kafka}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-file</artifactId>
                <version>${version.kafka}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-transforms</artifactId>
                <version>${version.kafka}</version>
            </dependency>

            <!-- Kafka -->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka_${version.kafka.scala}</artifactId>
                <version>${version.kafka}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${version.zookeeper}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>2.12.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka_${version.kafka.scala}</artifactId>
                <version>${version.kafka}</version>
                <classifier>test</classifier>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-test</artifactId>
                <version>${version.curator}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.confluent</groupId>
                <artifactId>kafka-connect-avro-converter</artifactId>
                <version>${version.confluent.platform}</version>
                <scope>test</scope>
            </dependency>

            <!--kafka-->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${version.kafka}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-streams</artifactId>
                <version>${version.kafka}</version>
            </dependency>

            <!--Make sure this version is compatible with the Protbuf-C version used on the server -->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${version.com.google.protobuf}</version>
            </dependency>

            <!-- MySQL JDBC Driver, Binlog reader, Geometry support -->
            <dependency>
                <groupId>com.zendesk</groupId>
                <artifactId>mysql-binlog-connector-java</artifactId>
                <version>${version.mysql.binlog}</version>
            </dependency>
            <dependency>
                <groupId>mil.nga</groupId>
                <artifactId>wkb</artifactId>
                <version>1.0.2</version>
            </dependency>

            <!-- MongoDB Java driver -->
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver-sync</artifactId>
                <version>${version.mongo.driver}</version>
            </dependency>

            <!-- Logging -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-1.2-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.18.0</version>
            </dependency>
            <dependency> <!-- 桥接：告诉commons logging使用Log4j2 -->
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-jcl</artifactId>
                <version>${log4j.version}</version>
            </dependency>


            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.5.0-M3</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.1.3</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.17.0</version>
            </dependency>

            <!-- jdbc driver -->
<!--            <dependency>-->
<!--                <groupId>com.oracle</groupId>-->
<!--                <artifactId>ojdbc</artifactId>-->
<!--                <version>${version.oracle.driver}</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${version.mssql.driver}</version>
            </dependency>

            <!-- Test dependencies -->
            <dependency>
                <groupId>org.easytesting</groupId>
                <artifactId>fest-assert</artifactId>
                <version>${version.fest}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>${junit-jupiter.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
					<dependency>
						<groupId>net.bytebuddy</groupId>
						<artifactId>byte-buddy</artifactId>
						<version>1.12.23</version>
						<scope>test</scope>
					</dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.tapdata</groupId>
                <artifactId>iengine-common</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tapdata</groupId>
                <artifactId>validator</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sap</groupId>
                <artifactId>jconn4</artifactId>
                <version>${jdbc.sybase.version}</version>
            </dependency>

            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>7.1</version>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm-tree</artifactId>
                <version>7.1</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.woodstox</groupId>
                <artifactId>stax2-api</artifactId>
                <version>4.2</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-mapper-asl</artifactId>
                <version>1.9.2</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-core-asl</artifactId>
                <version>1.9.2</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jettison</groupId>
                <artifactId>jettison</artifactId>
                <version>1.1</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.janino</groupId>
                <artifactId>janino</artifactId>
                <version>3.0.8</version>
            </dependency>

            <dependency>
                <groupId>org.jruby.jcodings</groupId>
                <artifactId>jcodings</artifactId>
                <version>1.0.55</version>
            </dependency>

            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>${org.bouncycastle.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${version.spring}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-csv</artifactId>
                <version>1.5</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-math3</artifactId>
                <version>3.1.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.27.1</version>
            </dependency>

            <dependency>
                <groupId>com.google.collections</groupId>
                <artifactId>google-collections</artifactId>
                <version>1.0</version>
            </dependency>

            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>${org.json.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>

            <!-- tapdata internal lib -->
            <dependency>
                <groupId>com.tapdata</groupId>
                <artifactId>api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.carrotsearch</groupId>
                <artifactId>hppc</artifactId>
                <version>0.8.1</version>
            </dependency>

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.10.1</version>
            </dependency>
            <dependency>
                <groupId>commons-cli</groupId>
                <artifactId>commons-cli</artifactId>
                <version>1.4</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.17.2</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>${commons.net.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.ws.rs</groupId>
                <artifactId>javax.ws.rs-api</artifactId>
                <version>2.1.1</version> <!--原2.0.1-->
            </dependency>
            <dependency>
                <groupId>javax.activation</groupId>
                <artifactId>activation</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>net.minidev</groupId>
                <artifactId>json-smart</artifactId>
                <version>2.3</version>
            </dependency>
            <dependency>
                <groupId>com.nimbusds</groupId>
                <artifactId>nimbus-jose-jwt</artifactId>
                <version>9.8.1</version>
            </dependency>
            <dependency>
                <groupId>org.jamon</groupId>
                <artifactId>jamon-runtime</artifactId>
                <version>2.3.1</version>
            </dependency>
            <dependency>
                <groupId>jline</groupId>
                <artifactId>jline</artifactId>
                <version>2.12</version>
            </dependency>
            <dependency>
                <groupId>org.xerial.snappy</groupId>
                <artifactId>snappy-java</artifactId>
                <version>1.1.10.7</version>
            </dependency>
            <dependency>
                <groupId>org.apache.orc</groupId>
                <artifactId>orc-core</artifactId>
                <version>1.5.6</version>
            </dependency>

            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>${dom4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.albfernandez</groupId>
                <artifactId>juniversalchardet</artifactId>
                <version>2.3.0</version>
            </dependency>

            <dependency>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro</artifactId>
                <version>${avro.version}</version>
            </dependency>

            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>antlr4</artifactId>
                <version>4.5</version>
            </dependency>

            <dependency>
                <groupId>org.samba.jcifs</groupId>
                <artifactId>jcifs</artifactId>
                <version>${jcifs.version}</version>
            </dependency>

            <dependency>
                <groupId>com.gbase</groupId>
                <artifactId>gbasedbt</artifactId>
                <version>${gbasedbt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ibm.db2.jcc</groupId>
                <artifactId>db2jcc</artifactId>
                <version>${version.db2.driver}</version>
            </dependency>

            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${version.postgresql.driver}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei</groupId>
                <artifactId>gauss200</artifactId>
                <version>1.0.0</version>
            </dependency>

            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>antlr4-runtime</artifactId>
                <version>${antlr.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>${jsqlparser.version}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>transport</artifactId>
                <version>7.6.2</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>7.6.2</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.plugin</groupId>
                <artifactId>transport-netty4-client</artifactId>
                <version>7.6.2</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>7.6.2</version>
            </dependency>


            <!--引入quartz依赖-->
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>2.5.0</version>
            </dependency>

            <!--引入hanlp依赖-->
            <dependency>
                <groupId>com.hankcs</groupId>
                <artifactId>hanlp</artifactId>
                <version>portable-1.5.3</version>
            </dependency>
            <dependency>
                <groupId>com.hankcs.nlp</groupId>
                <artifactId>hanlp-lucene-plugin</artifactId>
                <version>1.1.2</version>
            </dependency>

            <dependency>
                <groupId>org.simplejavamail</groupId>
                <artifactId>simple-java-mail</artifactId>
                <version>5.0.3</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/redis.clients/jedis -->
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>3.3.0</version>
            </dependency>

            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>3.4.2</version>
            </dependency>

            <!-- 解析Geometry类型数据 -->
            <dependency>
                <groupId>com.vividsolutions</groupId>
                <artifactId>jts</artifactId>
                <version>${jts.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>33.4.0-jre</version>
            </dependency>

            <!-- hazelcast -->
            <dependency>
                <groupId>com.hazelcast</groupId>
                <artifactId>hazelcast</artifactId>
                <version>${hazelcast.version}</version>
            </dependency>

            <!-- hazelcast persistence -->
            <dependency>
                <groupId>com.hazelcast</groupId>
                <artifactId>hazelcast-persistence</artifactId>
                <version>${hazelcast-persistence.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.os72</groupId>
                <artifactId>protobuf-dynamic</artifactId>
                <version>${protobuf-dynamic.version}</version>
            </dependency>

            <dependency>
                <groupId>io.tapdata</groupId>
                <artifactId>tapdata-api</artifactId>
                <version>${tapdata-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tapdata</groupId>
                <artifactId>tm-sdk</artifactId>
                <version>${tm-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tapdata</groupId>
                <artifactId>common</artifactId>
                <version>2.2.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.tapdata</groupId>
                <artifactId>tm-common</artifactId>
                <version>${tm-common.version}</version>
            </dependency>

            <dependency>
                <groupId>io.tapdata</groupId>
                <artifactId>tapdata-common</artifactId>
                <version>${tapdata-common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib</artifactId>
                <version>2.1.10</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.rocksdb/rocksdbjni -->
            <dependency>
                <groupId>org.rocksdb</groupId>
                <artifactId>rocksdbjni</artifactId>
                <version>7.3.1</version>
            </dependency>


            <dependency>
                <groupId>org.graalvm.js</groupId>
                <artifactId>js</artifactId>
                <version>${graalvm.js.version}</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>org.graalvm.js</groupId>
                <artifactId>js-scriptengine</artifactId>
                <version>${graalvm.js.version}</version>
            </dependency>
            <dependency>
                <groupId>io.tapdata</groupId>
                <artifactId>${graalvm.py.artifactId}</artifactId>
                <version>${graalvm.py.version}</version>
            </dependency>
            <dependency>
                <groupId>net.openhft</groupId>
                <artifactId>chronicle-queue</artifactId>
                <version>5.21.91</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>io.tapdata</groupId>
                <artifactId>error-code-core</artifactId>
                <version>${error-code-core.version}</version>
            </dependency>

            <dependency>
                <groupId>io.tapdata</groupId>
                <artifactId>pdk-error-code</artifactId>
                <version>${pdk-error-code.version}</version>
            </dependency>

            <dependency>
                <groupId>io.tapdata</groupId>
                <artifactId>error-code-scanner</artifactId>
                <version>${error-code-scanner.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-core</artifactId>
                <version>${lucene-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-analysis-common</artifactId>
                <version>${lucene-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-queryparser</artifactId>
                <version>${lucene-core.version}</version>
            </dependency>

            <dependency>
                <groupId>it.unimi.dsi</groupId>
                <artifactId>fastutil</artifactId>
                <version>8.5.13</version>
            </dependency>

            <dependency>
                <groupId>io.tapdata</groupId>
                <artifactId>modules-api</artifactId>
                <version>${modules-api.version}</version>
            </dependency>
            <dependency>
                <groupId>io.tapdata</groupId>
                <artifactId>service-skeleton-module</artifactId>
                <version>${service-skeleton-module.version}</version>
            </dependency>

            <dependency>
                <groupId>io.tapdata</groupId>
                <artifactId>websocket-client-module</artifactId>
                <version>${websocket-client-module.version}</version>
            </dependency>

            <dependency>
                <groupId>io.tapdata</groupId>
                <artifactId>tapdata-storage-module</artifactId>
                <version>${tapdata-storage-module.version}</version>
            </dependency>

            <dependency>
                <groupId>io.tapdata</groupId>
                <artifactId>script-engine-module</artifactId>
                <version>${script-engine-module.version}</version>
            </dependency>

            <dependency>
                <groupId>io.tapdata</groupId>
                <artifactId>async-tools-module</artifactId>
                <version>${async-tools-module.version}</version>
            </dependency>


        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>tapdata-tapdata-maven</id>
            <name>maven</name>
            <url>https://tapdata-maven.pkg.coding.net/repository/tapdata/maven/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>tapdata-wendangshujuku-mongo</id>
            <name>mongo</name>
            <url>https://tapdata-maven.pkg.coding.net/repository/wendangshujuku/mongo/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>private-repository</id>
            <name>Hazelcast Private Repository</name>
            <url>https://repository.hazelcast.com/release/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.0.0</version>
                    <configuration>
                        <argLine>
                            --add-opens=java.base/java.lang=ALL-UNNAMED
                            --add-opens=java.base/java.util=ALL-UNNAMED
                            --add-opens=java.base/java.security=ALL-UNNAMED
                            --add-opens=java.base/sun.security.rsa=ALL-UNNAMED
                            --add-opens=java.base/sun.security.x509=ALL-UNNAMED
                            --add-opens=java.base/sun.security.util=ALL-UNNAMED
                            --add-opens=java.xml/com.sun.org.apache.xerces.internal.jaxp.datatype=ALL-UNNAMED
                            -XX:+UnlockExperimentalVMOptions --add-exports=java.base/jdk.internal.ref=ALL-UNNAMED
                            --add-exports=java.base/sun.nio.ch=ALL-UNNAMED
                            --add-exports=jdk.unsupported/sun.misc=ALL-UNNAMED
                            --add-exports=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED
                            --add-opens=jdk.compiler/com.sun.tools.javac=ALL-UNNAMED
                            --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
                            --add-opens=java.base/java.io=ALL-UNNAMED
                            --add-opens=java.base/java.util=ALL-UNNAMED
                            --add-modules=java.se
                            --add-opens=java.management/sun.management=ALL-UNNAMED
                            --add-opens=jdk.management/com.sun.management.internal=ALL-UNNAMED
                            --add-opens=java.base/jdk.internal.loader=ALL-UNNAMED
                            ${surefireArgLine}
                        </argLine>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.12</version>
                    <executions>
                        <!--first execution : for preparing JaCoCo runtime agent-->
                        <execution>
                            <id>prepare-agent</id>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                            <configuration>
                                <propertyName>surefireArgLine</propertyName>
                            </configuration>
                        </execution>
                        <!--second execution : for creating code coverage reports-->
                        <execution>
                            <id>report</id>
                            <phase>test</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                            <configuration>
                                <formats>XML</formats>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>

                <plugin>
                    <groupId>io.fabric8</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>${version.docker.maven.plugin}</version>
                </plugin>
                <plugin>
                    <groupId>com.github.os72</groupId>
                    <artifactId>protoc-jar-maven-plugin</artifactId>
                    <version>${version.protoc.maven.plugin}</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <configuration>
                        <!-- executable>true</executable-->
                    </configuration>
                </plugin>

            </plugins>
        </pluginManagement>
<!--        <resources>-->
<!--            <resource>-->
<!--                <directory>src/main/java</directory>-->
<!--                <includes>-->
<!--                    <include>**/*.js</include>-->
<!--                </includes>-->
<!--                <targetPath>./</targetPath>-->
<!--            </resource>-->
<!--        </resources>-->
        <resources>
            <resource>
                <directory>src/main/resources/py-libs</directory>
                <includes>
                    <include>**/*.class</include>
                </includes>
            </resource>
        </resources>
    </build>

    <profiles>
        <profile>
            <id>dfs</id> <!-- DFS 数据源插件依赖排除 -->
            <properties>
                <scope.libs.db2>test</scope.libs.db2>
                <scope.libs.gbasedbt>test</scope.libs.gbasedbt>
                <scope.libs.hana>test</scope.libs.hana>
                <scope.libs.hbase>test</scope.libs.hbase>
                <scope.libs.hive>test</scope.libs.hive>
                <scope.libs.jira>test</scope.libs.jira>
                <scope.libs.kudu>test</scope.libs.kudu>
                <!--<scope.libs.tidb>test</scope.libs.tidb>-->
                <scope.libs.udp>test</scope.libs.udp>
            </properties>
        </profile>
        <profile>
            <id>Repository Proxy</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <repositories>
                <repository>
                    <id>tapdata-tapdata-maven</id>
                    <name>maven</name>
                    <url>https://tapdata-maven.pkg.coding.net/repository/tapdata/maven/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
        </profile>
        <profile>
            <id>oss</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>enterprise</id>
            <properties>
                <iengine.impl.artifactId>iengine-enterprise</iengine.impl.artifactId>
                <iengine.oss.module>../../tapdata-enterprise/iengine-enterprise</iengine.oss.module>
            </properties>
        </profile>
    </profiles>

</project>
