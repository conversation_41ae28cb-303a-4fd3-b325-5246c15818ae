<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.tapdata</groupId>
    <artifactId>iengine-app</artifactId>
    <packaging>jar</packaging>
    <name>IENGINE APP</name>

    <parent>
        <groupId>com.tapdata</groupId>
        <artifactId>iengine</artifactId>
        <version>0.5.2-SNAPSHOT</version>
    </parent>

    <properties>
        <!-- debezium -->
        <version.debezium>0.5.2-SNAPSHOT</version.debezium>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>
        <docker.image.prefix>tapdata</docker.image.prefix>
        <timestamp>${maven.build.timestamp}</timestamp>
        <maven.build.timestamp.format>yyyy-MM-dd'T'HH:mm:ss'Z'</maven.build.timestamp.format>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.tapdata</groupId>
            <artifactId>${iengine.impl.artifactId}</artifactId>
        </dependency>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>tapdata-storage-module</artifactId>
        </dependency>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>async-tools-module</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tapdata</groupId>
            <artifactId>proxy-client-module</artifactId>
            <version>0.5.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>io.tapdata</groupId>
                    <artifactId>websocket-client-module</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>websocket-client-module</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.tapdata</groupId>
                    <artifactId>modules-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>modules-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tapdata</groupId>
            <artifactId>observable-module</artifactId>
            <version>0.5.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.tapdata</groupId>
            <artifactId>milestone-module</artifactId>
            <version>0.5.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.tapdata</groupId>
            <artifactId>skip-error-event-module</artifactId>
            <version>0.5.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.tapdata</groupId>
            <artifactId>test-run-module</artifactId>
            <version>0.5.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.tapdata</groupId>
            <artifactId>deduction-module</artifactId>
            <version>0.5.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.tapdata</groupId>
            <artifactId>custom-sql-filter-module</artifactId>
            <version>0.5.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>service-skeleton-module</artifactId>
        </dependency>
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>script-engine-module</artifactId>
        </dependency>
        <dependency> <!-- exclude掉spring-boot的默认log配置 -->
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>9.0.0.CR1</version>
        </dependency>
        <dependency>  <!-- 加上这个才能辨认到log4j2.yml文件 -->
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-yaml</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-messaging</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- spring jdbc -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <!-- sql parser -->
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-1.2-api</artifactId>
        </dependency>

        <!-- mongo -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tapdata</groupId>
            <artifactId>iengine-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>lucene-queries</artifactId>
                    <groupId>org.apache.lucene</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>lucene-sandbox</artifactId>
                    <groupId>org.apache.lucene</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.tapdata</groupId>
            <artifactId>validator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
        </dependency>


        <dependency>
            <groupId>de.flapdoodle.embed</groupId>
            <artifactId>de.flapdoodle.embed.mongo</artifactId>
            <version>2.0.3</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-compress</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wix</groupId>
            <artifactId>wix-embedded-mysql</artifactId>
            <version>2.2.6</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>de.flapdoodle.embed.process</artifactId>
                    <groupId>de.flapdoodle.embed</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.simplejavamail</groupId>
            <artifactId>simple-java-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>org.samba.jcifs</groupId>
            <artifactId>jcifs</artifactId>
        </dependency>

        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>error-code-core</artifactId>
        </dependency>

        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>pdk-error-code</artifactId>
        </dependency>

        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>error-code-scanner</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tapdata</groupId>
            <artifactId>task-resource-supervisor-module</artifactId>
            <version>0.5.2-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-core</artifactId>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.11.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>4.11.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>4.11.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <version>1.12.23</version>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <version>1.12.23</version>
        </dependency>
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <version>2.0.0</version>
            <scope>test</scope>
        </dependency>
        <!-- Test Dependencies -->
    </dependencies>

    <build>
        <finalName>ie</finalName>
        <testSourceDirectory>src/test/java</testSourceDirectory>
        <plugins>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <outputDirectory>${basedir}/../</outputDirectory>
                </configuration>

                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <mainClass>io.tapdata.Application</mainClass>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>org.codehaus.mojo</groupId>-->
<!--                <artifactId>exec-maven-plugin</artifactId>-->
<!--                <version>3.0.0</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <phase>install</phase>-->
<!--                        <goals>-->
<!--                            <goal>exec</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    <executable>java</executable>-->
<!--                    <arguments>-->
<!--                        <argument>-jar</argument>-->
<!--                        <argument>../../tapdata-cli/target/pdk.jar</argument>-->
<!--                        <argument>py-install</argument>-->
<!--                        <argument>-j</argument>-->
<!--                        <argument>${graalvm.py.artifactId}-${graalvm.py.version}.jar</argument>-->
<!--                        <argument>-p</argument>-->
<!--                        <argument>${settings.localRepository}/io/tapdata/${graalvm.py.artifactId}/${graalvm.py.version}/</argument>-->
<!--                        <argument>-uz</argument>-->
<!--                        <argument>src/main/resources/py-libs</argument>-->
<!--                    </arguments>-->
<!--                    <environmentVariables>-->
<!--                        <LANG>en_US</LANG>-->
<!--                    </environmentVariables>-->
<!--                </configuration>-->
<!--            </plugin>-->
<!--            <plugin>-->
<!--                <groupId>org.springframework.boot</groupId>-->
<!--                <artifactId>spring-boot-maven-plugin</artifactId>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <goals>-->
<!--                            <goal>repackage</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <include>py-libs/**/*.class</include>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
        </plugins>
        <resources>
<!--            <resource>-->
<!--                <directory>src/main/resources/py-libs</directory>-->
<!--                <includes>-->
<!--                    <include>**</include>-->
<!--                </includes>-->
<!--                <targetPath>py-libs</targetPath>/-->
<!--            </resource>-->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.js</include>
                </includes>
                <targetPath>./${parent.artifactId}/${project.artifactId}/src/main/java/</targetPath>
            </resource>
            <resource>
                <directory>src/main/javascript</directory>
                <includes>
                    <include>**/*.js</include>
                </includes>
                <targetPath>./${project.parent.artifactId}/${project.artifactId}/src/main/javascript/</targetPath>
            </resource>
        </resources>
    </build>
    <repositories>
        <repository>
            <id>tapdata-tapdata-maven</id>
            <name>maven</name>
            <url>https://tapdata-maven.pkg.coding.net/repository/tapdata/maven/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
