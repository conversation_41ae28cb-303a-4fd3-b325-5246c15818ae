package io.tapdata.connector.oracle.cdc.bridge;

import io.tapdata.entity.logger.Log;
import io.tapdata.kit.EmptyKit;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.util.zip.Inflater;

public class BridgeService implements AutoCloseable {

    private final String taskId;
    private final Socket socket;
    private final InputStream inputStream;
    private final OutputStream outputStream;
    private int currentFno = 0;
    private int maxFno = -1;
    private Log tapLogger;

    public BridgeService(String host, int port, String taskId) throws IOException {
        this.taskId = taskId;
        this.socket = new Socket(host, port);
        this.inputStream = socket.getInputStream();
        this.outputStream = socket.getOutputStream();
    }

    public static BridgeService create(String host, int port, String taskId) throws IOException {
        return new BridgeService(host, port, taskId);
    }

    public void initTask() throws IOException {
        setTaskId();
        setClearFzs();
        setResultType();
    }

    public void setTapLogger(Log tapLogger) {
        this.tapLogger = tapLogger;
    }

    private void setTaskId() throws IOException {
        tapLogger.info("setting fzs taskId " + taskId + "");
        String command = "I" + taskId;
        outputStream.write(command.getBytes());
        byte[] response = new byte[3];
        int read = inputStream.read(response);
        if (-1 != read) {
            if (new String(response).equals("IOK")) {
                tapLogger.info("fzs set " + taskId + " taskId ok");
            } else {
                throw new BridgeException("set task id failed");
            }
        } else {
            throw new BridgeException("set task id failed");
        }
    }

    private void setClearFzs() throws IOException {
        String command = "D" + 0;
        outputStream.write(command.getBytes());
        byte[] response = new byte[3];
        int read = inputStream.read(response);
        if (-1 != read) {
            if (new String(response).equals("DOK")) {
                tapLogger.info("set save fzs time ok");
            } else {
                throw new BridgeException("set save fzs time failed");
            }
        } else {
            throw new BridgeException("set save fzs time failed");
        }
    }

    private void setResultType() throws IOException {
        String command = "M1";
        outputStream.write(command.getBytes());
        byte[] response = new byte[3];
        int read = inputStream.read(response);
        if (-1 != read) {
            if (new String(response).equals("MOK")) {
                tapLogger.info("set fzs data format ok");
            } else {
                throw new BridgeException("set fzs data format failed");
            }
        } else {
            throw new BridgeException("set fzs data format failed");
        }
    }

    public void start() throws IOException {
        byte[] command = new byte[1];
        command[0] = 'B';
        outputStream.write(command);
        byte[] response = new byte[3];
        int read = inputStream.read(response);
        if (-1 != read) {
            if (new String(response).equals("BOK")) {
                tapLogger.info("fzs service started");
            } else {
                throw new BridgeException("fzs service start failed");
            }
        } else {
            throw new BridgeException("fzs service start failed");
        }
    }

    public void setTableWithScn(String tableWithScn) throws IOException {
        String command = "T" + tableWithScn;
        outputStream.write(command.getBytes());
        byte[] response = new byte[3];
        int read = inputStream.read(response);
        if (-1 != read) {
            if (new String(response).equals("TOK")) {
                tapLogger.info("fzs set " + tableWithScn + " table and bgn scn ok");
            } else {
                throw new BridgeException("fzs set " + tableWithScn + " table and bgn scn failed");
            }
        } else {
            throw new BridgeException("fzs set " + tableWithScn + " table and bgn scn failed");
        }
    }

    public void setManyTablesWithScn(String tableWithScn) throws IOException {
        String command = "A" + tableWithScn.getBytes().length;
        outputStream.write(command.getBytes());
        byte[] response = new byte[3];
        int read = inputStream.read(response);
        if (-1 != read) {
            if (new String(response).equals("AOK")) {
                tapLogger.info("fzs set " + tableWithScn + " table and bgn scn size ok");
            } else {
                throw new BridgeException("fzs set " + tableWithScn + " table and bgn scn size failed");
            }
        } else {
            throw new BridgeException("fzs set " + tableWithScn + " table and bgn scn size failed");
        }
        outputStream.write(tableWithScn.getBytes());
        response = new byte[3];
        read = inputStream.read(response);
        if (-1 != read) {
            if (new String(response).equals("AOK")) {
                tapLogger.info("fzs set " + tableWithScn + " table and bgn scn ok");
            } else {
                throw new BridgeException("fzs set " + tableWithScn + " table and bgn scn failed");
            }
        } else {
            throw new BridgeException("fzs set " + tableWithScn + " table and bgn scn failed");
        }
    }

    private void updateMaxFno() throws IOException {
        byte[] command = new byte[1];
        command[0] = 'N';
        outputStream.write(command);
        byte[] response = new byte[48];
        int read = inputStream.read(response);
        if (-1 != read) {
            String strFnoMax = new String(response).trim();
            maxFno = Integer.parseInt(strFnoMax);
        } else {
            maxFno = 0;
        }
    }

    public void setCurrentFno(int currentFno) {
        this.currentFno = currentFno;
    }

    public byte[] pullRedoLog() throws IOException {
        byte[] res = null;
        if (maxFno < currentFno) {
            String errorMsg = pullErrorMsg();
            if (EmptyKit.isNotBlank(errorMsg)) {
                throw new BridgeException(errorMsg);
            }
            updateMaxFno();
        } else {
            int sqlLength = 0;
            int ifzip = 0;
            int orglen = 0;
            String command = "F" + currentFno;
            outputStream.write(command.getBytes());
            while (true) {
                byte[] response = new byte[36];
                int read = inputStream.read(response);
                if (-1 != read) {
                    if (new String(response).startsWith("F")) {
                        String strRes = new String(response).trim();
                        int fno_skip = 0;
                        if (strRes.length() > 3) {
                            fno_skip = Integer.parseInt(strRes.substring(3));
                        }
                        if (strRes.startsWith("FOK")) {
                        } else if (strRes.startsWith("FNO")) {
                            tapLogger.warn("receive fzs fno:" + currentFno + " not exist");
                        } else {
                            throw new BridgeException("receive fzs fno:" + currentFno + " all data failed");
                        }
                        currentFno += fno_skip;
                        break;
                    } else {
                        String resp = new String(response).trim();
                        String[] str = resp.split(":");
                        if (3 == str.length) {
                            ifzip = Integer.parseInt(str[0]);
                            orglen = Integer.parseInt(str[1]);
                            sqlLength = Integer.parseInt(str[2]);
                        }
//                        sqlLength = Integer.parseInt(new String(response).trim());
                    }
                    command = "LOK";
                    outputStream.write(command.getBytes());
                } else {
                    command = "LNO";
                    outputStream.write(command.getBytes());
                    break;
                }
                if (sqlLength > 0) {
                    byte[] data = new byte[sqlLength];
                    int readed = 0;
                    command = "LOK";
                    while (readed < sqlLength) {
                        read = inputStream.read(data, readed, sqlLength - readed);
                        if (-1 == read) {
                            command = "LNO";
                            break;
                        }
                        readed += read;
                    }
                    outputStream.write(command.getBytes());
                    byte[] zipData = new byte[orglen];
                    if (1 == ifzip) {
                        Inflater decompress = new Inflater();
                        decompress.reset();
                        decompress.setInput(data);

                        ByteArrayOutputStream out = new ByteArrayOutputStream(orglen);

                        try {
                            while (!decompress.needsInput()) {
                                int clen = decompress.inflate(zipData);
                                if (clen > 0) {
                                    out.write(zipData, 0, clen);    // clen = orglen
                                }
                            }
                        } catch (Exception e) {
                            throw new BridgeException("fzs unzip failed");
                        }
                        out.close();
                        decompress.end();
                        zipData = out.toByteArray();
                    } else
                        zipData = data;
                    res = zipData;
                }
            }
            currentFno++;
        }
        return res;
    }

    private String pullErrorMsg() throws IOException {
        StringBuilder allErrorMsg = new StringBuilder();
        int errorSize = 1024;
        int msgLength;
        String commnad = "E" + errorSize;
        outputStream.write(commnad.getBytes());
        while (true) {
            byte[] response = new byte[36];
            int read = inputStream.read(response);
            if (-1 != read) {
                if (new String(response).startsWith("E")) {
                    break;
                } else {
                    msgLength = Integer.parseInt(new String(response).trim());
                }
                commnad = "MOK";
                outputStream.write(commnad.getBytes());
            } else {
                commnad = "MNO";
                outputStream.write(commnad.getBytes());
                break;
            }
            if (msgLength > 0) {
                byte[] data = new byte[msgLength];
                int readed = 0;

                commnad = "MOK";
                while (readed < msgLength) {
                    read = inputStream.read(data, readed, msgLength - readed);
                    if (-1 == read) {
                        commnad = "MNO";
                        break;
                    }

                    readed += read;
                }
                outputStream.write(commnad.getBytes());
                allErrorMsg.append(new String(data)).append("\n");
            }
        }
        return allErrorMsg.toString();
    }

    public void clearTask() throws IOException {
        String command = "0";
        outputStream.write(command.getBytes());
        byte[] response = new byte[3];
        int read = inputStream.read(response);
        if (-1 != read) {
            if (new String(response).equals("0OK")) {
                tapLogger.info("fzs clear task ok");
            } else {
                tapLogger.warn("fzs clean task failed");
            }
        }
    }

    public void setEnableFzsZip(boolean enableFzsZip) throws IOException {
        String command = "Z" + (enableFzsZip ? 1 : 0);
        outputStream.write(command.getBytes());
        byte[] response = new byte[3];
        int read = inputStream.read(response);
        if (-1 != read) {
            if (new String(response).equals("ZOK")) {
                tapLogger.info("set {} fzs zip ok", enableFzsZip ? "enable" : "disable");
            } else {
                throw new BridgeException("set fzs zip failed");
            }
        } else {
            throw new BridgeException("set fzs zip failed");
        }
    }

    public void setFzsSocketTimeout(int timeout) throws IOException {
        String command = "O" + timeout;
        outputStream.write(command.getBytes());
        byte[] response = new byte[3];
        int read = inputStream.read(response);
        if (-1 != read) {
            if (new String(response).equals("OOK")) {
                tapLogger.info("set fzs socket timeout {} ok", timeout);
            } else {
                throw new BridgeException("set fzs socket timeout failed");
            }
        } else {
            throw new BridgeException("set fzs socket timeout failed");
        }
    }

    public void setFzsPollingInterval(int milliSecond) throws IOException {
        String command = "t" + milliSecond;
        outputStream.write(command.getBytes());
        byte[] response = new byte[3];
        int read = inputStream.read(response);
        if (-1 != read) {
            if (new String(response).equals("tOK")) {
                tapLogger.info("set fzs polling interval {} ok", milliSecond * 2);
            } else {
                throw new BridgeException("set fzs polling interval failed");
            }
        } else {
            throw new BridgeException("set fzs polling interval failed");
        }
    }

    @Override
    public void close() {
        EmptyKit.closeQuietly(inputStream);
        EmptyKit.closeQuietly(outputStream);
        EmptyKit.closeQuietly(socket);
    }
}
