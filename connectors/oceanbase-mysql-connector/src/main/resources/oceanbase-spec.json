{"properties": {"name": "Oceanbase", "icon": "icons/oceanbase.png", "id": "oceanbase", "doc": "${doc}", "tags": ["Database"]}, "configOptions": {"capabilities": [{"id": "dml_insert_policy", "alternatives": ["update_on_exists", "ignore_on_exists"]}, {"id": "dml_update_policy", "alternatives": ["ignore_on_nonexists", "insert_on_nonexists"]}, {"id": "batch_read_hash_split"}], "supportDDL": {"events": ["new_field_event", "alter_field_name_event", "alter_field_attributes_event", "drop_field_event"]}, "node": {"type": "object", "properties": {"createAutoInc": {"type": "boolean", "title": "${createAutoInc}", "default": false, "x-index": 5, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${createAutoIncTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{$deps[0].length > 0 ? \"visible\":\"hidden\"}}"}}}]}, "autoIncJumpValue": {"required": true, "type": "string", "title": "${autoIncJumpValue}", "default": 1000000, "x-index": 6, "x-decorator": "FormItem", "x-component": "InputNumber", "x-reactions": [{"dependencies": ["$inputs", ".createAutoInc"], "fulfill": {"state": {"display": "{{$deps[0].length > 0 && $deps[1] ? \"visible\":\"hidden\"}}"}}}]}, "applyDefault": {"type": "boolean", "title": "${applyDefault}", "default": false, "x-index": 7, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${applyDefaultTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{$deps[0].length > 0 ? \"visible\":\"hidden\"}}"}}}]}, "hashSplit": {"type": "boolean", "title": "${hashSplit}", "default": false, "x-index": 10, "x-decorator": "FormItem", "x-component": "Switch", "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "maxSplit": {"required": true, "type": "string", "title": "${maxSplit}", "default": 16, "x-index": 12, "x-decorator": "FormItem", "x-component": "InputNumber", "x-component-props": {"min": 2, "max": 1000000}, "x-reactions": [{"dependencies": ["$inputs", ".hashSplit"], "fulfill": {"state": {"display": "{{!$deps[0].length && $deps[1] ? \"visible\":\"hidden\"}}"}}}]}, "batchReadThreadSize": {"required": true, "type": "string", "title": "${batchReadThreadSize}", "default": 8, "x-index": 13, "x-decorator": "FormItem", "x-component": "InputNumber", "x-component-props": {"min": 1, "max": 32}, "x-reactions": [{"dependencies": ["$inputs", ".hashSplit"], "fulfill": {"state": {"display": "{{!$deps[0].length && $deps[1] ? \"visible\":\"hidden\"}}"}}}]}}}, "connection": {"type": "object", "properties": {"host": {"type": "string", "title": "${host}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_host", "x-index": 1, "required": true}, "port": {"type": "string", "title": "${port}", "default": 2881, "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "database_port", "x-index": 2, "required": true}, "tenant": {"type": "string", "title": "${tenant}", "x-decorator": "FormItem", "x-component": "Input", "x-index": 3, "required": true}, "database": {"type": "string", "title": "${database}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_name", "x-index": 4, "required": true}, "user": {"type": "string", "title": "${user}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_username", "x-index": 5, "required": true}, "password": {"type": "string", "title": "${password}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "database_password", "x-index": 6}, "extParams": {"type": "string", "title": "${extParams}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "extParams", "x-index": 7}, "rootServerList": {"required": true, "type": "string", "title": "${rootServerList}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "rootServerList", "x-decorator-props": {"tooltip": "${rootServerListTooltip}"}, "x-index": 8}, "logProxyHost": {"type": "string", "title": "${logProxyHost}", "x-decorator": "FormItem", "x-component": "Input", "x-index": 9, "required": true}, "logProxyPort": {"type": "string", "title": "${logProxyPort}", "default": 2983, "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "proxy_port", "x-index": 10, "required": true}, "timezone": {"type": "string", "title": "${timezone}", "default": "", "x-decorator": "FormItem", "x-component": "Select", "x-index": 11, "enum": [{"label": "", "value": ""}, {"label": "UTC -11", "value": "-11:00"}, {"label": "UTC -10", "value": "-10:00"}, {"label": "UTC -09", "value": "-09:00"}, {"label": "UTC -08", "value": "-08:00"}, {"label": "UTC -07", "value": "-07:00"}, {"label": "UTC -06", "value": "-06:00"}, {"label": "UTC -05", "value": "-05:00"}, {"label": "UTC -04", "value": "-04:00"}, {"label": "UTC -03", "value": "-03:00"}, {"label": "UTC -02", "value": "-02:00"}, {"label": "UTC -01", "value": "-01:00"}, {"label": "UTC", "value": "+00:00"}, {"label": "UTC +01", "value": "+01:00"}, {"label": "UTC +02", "value": "+02:00"}, {"label": "UTC +03", "value": "+03:00"}, {"label": "UTC +04", "value": "+04:00"}, {"label": "UTC +05", "value": "+05:00"}, {"label": "UTC +06", "value": "+06:00"}, {"label": "UTC +07", "value": "+07:00"}, {"label": "UTC +08", "value": "+08:00"}, {"label": "UTC +09", "value": "+09:00"}, {"label": "UTC +10", "value": "+10:00"}, {"label": "UTC +11", "value": "+11:00"}, {"label": "UTC +12", "value": "+12:00"}, {"label": "UTC +13", "value": "+13:00"}, {"label": "UTC +14", "value": "+14:00"}]}}}}, "messages": {"default": "en_US", "en_US": {"host": "Host", "port": "Port", "tenant": "Tenant", "database": "database", "user": "user", "password": "password", "extParams": "Connection Parameter String", "rootServerList": "OceanBase Root Server List", "rootServerListTooltip": "format demo: *************:2882:2881,*************:2882:2881,*************:2882:2881", "logProxyHost": "Log Proxy Host", "logProxyPort": "Log Proxy Port", "timezone": "timezone", "doc": "docs/oceanbase_mysql_en_US.md", "hashSplit": "Single Table Concurrent Read", "maxSplit": "Max Split Count for Single Table", "batchReadThreadSize": "Single Table Concurrent Read Thread Size"}, "zh_CN": {"host": "地址", "port": "端口", "tenant": "租户", "database": "数据库", "user": "账号", "password": "密码", "extParams": "连接参数", "rootServerList": "OceanBase多节点列表", "rootServerListTooltip": "格式案例：*************:2882:2881,*************:2882:2881,*************:2882:2881", "logProxyHost": "日志代理地址", "logProxyPort": "日志代理端口", "timezone": "时区", "doc": "docs/oceanbase_mysql_zh_CN.md", "hashSplit": "单表并发读", "maxSplit": "单表最大分片数", "batchReadThreadSize": "单表并发读线程数"}, "zh_TW": {"host": "地址", "port": "端口", "tenant": "租戶", "database": "數據庫", "user": "賬號", "password": "密碼", "extParams": "連接參數", "rootServerList": "OceanBase多节点列表", "rootServerListTooltip": "格式案例：*************:2882:2881,*************:2882:2881,*************:2882:2881", "logProxyHost": "日誌代理地址", "logProxyPort": "日誌代理端口", "timezone": "時區", "doc": "docs/oceanbase_mysql_zh_TW.md", "hashSplit": "單表並發讀", "maxSplit": "單表最大分片數", "batchReadThreadSize": "單表並發讀線程數"}}, "dataTypes": {"char[($byte)]": {"to": "TapString", "byte": 255, "defaultByte": 1, "fixed": true}, "varchar($byte)": {"name": "<PERSON><PERSON><PERSON>", "to": "TapString", "byte": 16358, "defaultByte": 1, "byteRatio": 4}, "tinytext": {"to": "TapString", "byte": 255, "pkEnablement": false}, "text": {"to": "TapString", "byte": "64k", "pkEnablement": false}, "mediumtext": {"to": "TapString", "byte": "16m", "pkEnablement": false}, "longtext": {"to": "TapString", "byte": "4g", "pkEnablement": false}, "json": {"to": "TapMap", "byte": "4g", "pkEnablement": false}, "binary[($byte)]": {"to": "TapBinary", "byte": 255, "defaultByte": 1, "fixed": true}, "varbinary[($byte)]": {"to": "TapBinary", "byte": 65532, "defaultByte": 1}, "tinyblob": {"to": "TapBinary", "byte": 255}, "blob": {"to": "TapBinary", "byte": "64k"}, "mediumblob": {"to": "TapBinary", "byte": "16m"}, "longblob": {"to": "TapBinary", "byte": "4g"}, "bit(1)": {"to": "TapBoolean", "bit": 1, "queryOnly": true}, "bit[($bit)]": {"to": "TapNumber", "bit": 64, "queryOnly": true}, "tinyint(1)": {"to": "TapBoolean", "bit": 1}, "tinyint[($zerofill)]": {"to": "TapNumber", "bit": 8, "precision": 3, "value": [-128, 127]}, "tinyint[($zerofill)] unsigned": {"to": "TapNumber", "bit": 8, "precision": 3, "value": [0, 255], "unsigned": "unsigned"}, "smallint[($zerofill)]": {"to": "TapNumber", "bit": 16, "value": [-32768, 32767], "precision": 5}, "smallint[($zerofill)] unsigned": {"to": "TapNumber", "bit": 16, "precision": 5, "value": [0, 65535], "unsigned": "unsigned"}, "mediumint[($zerofill)]": {"to": "TapNumber", "bit": 24, "precision": 7, "value": [-8388608, 8388607]}, "mediumint[($zerofill)] unsigned": {"to": "TapNumber", "bit": 24, "precision": 8, "value": [0, 16777215], "unsigned": "unsigned"}, "int[($zerofill)]": {"to": "TapNumber", "bit": 32, "precision": 10, "value": [-2147483648, 2147483647]}, "int[($zerofill)] unsigned": {"to": "TapNumber", "bit": 32, "precision": 10, "value": [0, 4294967295]}, "bigint[($zerofill)]": {"to": "TapNumber", "bit": 64, "precision": 19, "value": [-9223372036854775808, 9223372036854775807]}, "bigint[($zerofill)] unsigned": {"to": "TapNumber", "bit": 64, "precision": 20, "value": [0, 18446744073709551615]}, "decimal[($precision,$scale)][unsigned]": {"to": "TapNumber", "precision": [1, 65], "scale": [0, 30], "defaultPrecision": 10, "defaultScale": 0, "unsigned": "unsigned", "fixed": true}, "float($precision,$scale)[unsigned]": {"to": "TapNumber", "name": "float", "precision": [1, 30], "scale": [0, 30], "value": ["-3.402823466E+38", "3.402823466E+38"], "unsigned": "unsigned", "fixed": false}, "float": {"to": "TapNumber", "precision": [1, 6], "scale": [0, 6], "fixed": false}, "double": {"to": "TapNumber", "precision": [1, 17], "preferPrecision": 11, "preferScale": 4, "scale": [0, 17], "fixed": false}, "date": {"to": "TapDate", "range": ["1000-01-01", "9999-12-31"], "pattern": "yyyy-MM-dd"}, "time[($fraction)]": {"to": "TapTime", "fraction": [0, 6], "defaultFraction": 0, "range": ["-838:59:59", "838:59:59"], "pattern": "HH:mm:ss"}, "datetime[($fraction)]": {"to": "TapDateTime", "range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "defaultFraction": 0}, "timestamp[($fraction)]": {"to": "TapDateTime", "range": ["1970-01-01 00:00:01", "2038-01-19 03:14:07"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 6], "defaultFraction": 0, "withTimeZone": true}, "year[($fraction)]": {"to": "TapYear", "range": ["1901", "2155"], "fraction": [0, 4], "defaultFraction": 4, "pattern": "yyyy"}, "enum($enums)": {"name": "enum", "to": "TapString", "queryOnly": true, "byte": 16383}, "set($sets)": {"name": "set", "to": "TapString", "queryOnly": true, "byte": 16383}, "point": {"to": "TapBinary", "queryOnly": true}, "linestring": {"to": "TapBinary", "queryOnly": true}, "polygon": {"to": "TapBinary", "queryOnly": true}, "geometry": {"to": "TapBinary", "queryOnly": true}, "multipoint": {"to": "TapBinary", "queryOnly": true}, "multilinestring": {"to": "TapBinary", "queryOnly": true}, "multipolygon": {"to": "TapBinary", "queryOnly": true}, "geomcollection": {"to": "TapBinary", "queryOnly": true}}}