package io.tapdata.connector.mssql;

import com.microsoft.sqlserver.jdbc.SQLServerException;
import com.microsoft.sqlserver.jdbc.StringUtils;
import io.tapdata.common.CommonDbConnector;
import io.tapdata.common.CommonSqlMaker;
import io.tapdata.common.SqlExecuteCommandFunction;
import io.tapdata.common.ddl.DDLSqlGenerator;
import io.tapdata.common.dml.NormalRecordWriter;
import io.tapdata.connector.mssql.cdc.CdcOffset;
import io.tapdata.connector.mssql.cdc.MssqlCdcRunner;
import io.tapdata.connector.mssql.config.MssqlConfig;
import io.tapdata.connector.mssql.ddl.MssqlDDLSqlGenerator;
import io.tapdata.connector.mssql.dml.MssqlRecordWriterV2;
import io.tapdata.connector.mssql.exception.MssqlExceptionCollector;
import io.tapdata.connector.mssql.partition.MssqlPartitionContext;
import io.tapdata.connector.mssql.partition.vo.MSSQLPartitionInfo;
import io.tapdata.connector.mssql.partition.vo.PartitionFunctionAndStageResult;
import io.tapdata.entity.codec.TapCodecsRegistry;
import io.tapdata.entity.codec.ToTapValueCodec;
import io.tapdata.entity.error.CoreException;
import io.tapdata.entity.event.ddl.index.TapCreateIndexEvent;
import io.tapdata.entity.event.ddl.table.*;
import io.tapdata.entity.event.dml.TapRecordEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapIndex;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.schema.partition.TapPartition;
import io.tapdata.entity.schema.type.TapDateTime;
import io.tapdata.entity.schema.type.TapType;
import io.tapdata.entity.schema.value.*;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.simplify.pretty.BiClassHandlers;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.entity.utils.cache.Entry;
import io.tapdata.exception.TapDbCdcConfigInvalidEx;
import io.tapdata.kit.DbKit;
import io.tapdata.kit.EmptyKit;
import io.tapdata.pdk.apis.annotations.TapConnectorClass;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import io.tapdata.pdk.apis.context.TapConnectionContext;
import io.tapdata.pdk.apis.context.TapConnectorContext;
import io.tapdata.pdk.apis.entity.*;
import io.tapdata.pdk.apis.functions.ConnectorFunctions;
import io.tapdata.pdk.apis.functions.PDKMethod;
import io.tapdata.pdk.apis.functions.connection.ConnectionCheckItem;
import io.tapdata.pdk.apis.functions.connection.RetryOptions;
import io.tapdata.pdk.apis.functions.connection.TableInfo;
import io.tapdata.pdk.apis.functions.connector.common.vo.TapHashResult;
import io.tapdata.pdk.apis.functions.connector.target.CreateTableOptions;
import microsoft.sql.DateTimeOffset;

import java.sql.Date;
import java.sql.*;
import java.time.Instant;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@TapConnectorClass("mssql-spec.json")
public class MssqlConnector extends CommonDbConnector {

    private static final int BATCH_ADVANCE_READ_LIMIT = 1000;

    private String version;
    private Long timeZoneOffset;
    private MssqlConfig config;
    private MssqlJdbcRunner jdbcRunner;
    private MssqlTest mssqlTest;
    private MssqlCdcRunner cdcRunner;
    private BiClassHandlers<TapFieldBaseEvent, TapConnectorContext, List<String>> fieldDDLHandlers;
    private DDLSqlGenerator ddlSqlGenerator;
    protected MssqlPartitionContext mssqlPartitionContext;


    @Override
    public void onStart(TapConnectionContext connectionContext) {
        config = new MssqlConfig().log(connectionContext.getLog()).load(connectionContext.getConnectionConfig());
        config.load(connectionContext.getNodeConfig());
        mssqlTest = new MssqlTest(config, testItem -> {
        }, null);
        jdbcRunner = new MssqlJdbcRunner(config);
        commonDbConfig = config;
        jdbcContext = jdbcRunner;
        isConnectorStarted(connectionContext, tapConnectorContext -> {
            config.load(tapConnectorContext.getNodeConfig());
            mssqlPartitionContext = new MssqlPartitionContext(tapConnectorContext)
                    .withMssqlConfig(config)
                    .withJdbcRunner(jdbcRunner);
        });
        version = jdbcRunner.queryVersion();
        timeZoneOffset = jdbcRunner.queryTimeZoneOffset();
        ddlSqlGenerator = new MssqlDDLSqlGenerator();
        exceptionCollector = new MssqlExceptionCollector();
        tapLogger = connectionContext.getLog();
        fieldDDLHandlers = new BiClassHandlers<>();
        fieldDDLHandlers.register(TapNewFieldEvent.class, this::newField);
        fieldDDLHandlers.register(TapAlterFieldAttributesEvent.class, this::alterFieldAttr);
        fieldDDLHandlers.register(TapAlterFieldNameEvent.class, this::alterFieldName);
        fieldDDLHandlers.register(TapDropFieldEvent.class, this::dropField);
        commonSqlMaker = new CommonSqlMaker();
    }

    @Override
    public void onStop(TapConnectionContext connectionContext) {
        if (EmptyKit.isNotNull(cdcRunner)) {
            cdcRunner.close();
        }
        if (EmptyKit.isNotNull(mssqlTest)) {
            mssqlTest.close();
        }
        if (EmptyKit.isNotNull(jdbcRunner)) {
            jdbcRunner.close();
        }
    }

    @Override
    public void registerCapabilities(ConnectorFunctions connectorFunctions, TapCodecsRegistry codecRegistry) {
        // test functions
        connectorFunctions.supportConnectionCheckFunction(this::checkConnection);
        // target functions
        connectorFunctions.supportCreateTableV2(this::createTableV2);
        connectorFunctions.supportClearTable(this::clearTable);
        connectorFunctions.supportDropTable(this::dropTable);
        connectorFunctions.supportQueryIndexes(this::queryIndexes);
        connectorFunctions.supportCreateIndex(this::createIndex);
        connectorFunctions.supportQueryConstraints(this::queryConstraint);
        connectorFunctions.supportCreateConstraint(this::createConstraint);
        connectorFunctions.supportWriteRecord(this::writeRecord);
        connectorFunctions.supportAfterInitialSync(this::afterInitialSync);
        // source functions
        connectorFunctions.supportBatchCount(this::batchCount);
        connectorFunctions.supportBatchRead(this::batchReadWithoutOffset);
        connectorFunctions.supportStreamRead(this::streamRead);
        connectorFunctions.supportTimestampToStreamOffset(this::timestampToStreamOffset);
        // query functions
        connectorFunctions.supportQueryByFilter(this::queryByFilter);
        connectorFunctions.supportQueryByAdvanceFilter(this::queryByAdvanceFilter);
        connectorFunctions.supportCountByPartitionFilterFunction(this::countByAdvanceFilter);
        connectorFunctions.supportGetTableNamesFunction(this::getTableNames);
        // ddl functions
        connectorFunctions.supportNewFieldFunction(this::fieldDDLHandler);
        connectorFunctions.supportAlterFieldNameFunction(this::fieldDDLHandler);
        connectorFunctions.supportAlterFieldAttributesFunction(this::fieldDDLHandler);
        connectorFunctions.supportDropFieldFunction(this::fieldDDLHandler);
        connectorFunctions.supportErrorHandleFunction(this::errorHandle);
        connectorFunctions.supportExecuteCommandFunction((a, b, c) -> SqlExecuteCommandFunction.executeCommand(a, b, () -> jdbcRunner.getConnection(), this::isAlive, c));
        connectorFunctions.supportRunRawCommandFunction(this::runRawCommand);
        connectorFunctions.supportCountRawCommandFunction(this::countRawCommand);
        connectorFunctions.supportCreatePartitionTableFunction(this::createPartitionTable);
        connectorFunctions.supportDropPartitionTableFunction(this::dropPartitionTable);
        connectorFunctions.supportCreateSubPartitionTableFunction(this::createSubPartitionTable);


        //TapTimeValue, TapDateTimeValue and TapDateValue's value is DateTime, need convert into Date object.
        codecRegistry.registerFromTapValue(TapTimeValue.class, tapTimeValue -> {
            if (config.getOldVersionTimezone()) {
                return tapTimeValue.getValue().toTime();
            } else {
                return tapTimeValue.getValue().toInstant().atZone(config.getZoneId()).toLocalTime();
            }
        });
        codecRegistry.registerFromTapValue(TapDateTimeValue.class, tapDateTimeValue -> {
            if (config.getOldVersionTimezone()) {
                return tapDateTimeValue.getValue().toTimestamp();
            } else {
                if (EmptyKit.isNotNull(tapDateTimeValue.getValue().getTimeZone())) {
                    TimeZone timeZone = tapDateTimeValue.getValue().getTimeZone();
                    return DateTimeOffset.valueOf(tapDateTimeValue.getValue().toTimestamp(), timeZone.getRawOffset() / 1000 / 60);
                } else {
                    return tapDateTimeValue.getValue().toInstant().atZone(config.getZoneId()).toLocalDateTime();
                }
            }
        });
        codecRegistry.registerFromTapValue(TapDateValue.class, tapDateValue -> tapDateValue.getValue().toSqlDate());
        codecRegistry.registerFromTapValue(TapYearValue.class, "char(4)", TapValue::getOriginValue);
        codecRegistry.registerFromTapValue(TapBooleanValue.class, "tinyint", TapValue::getValue);
        codecRegistry.registerFromTapValue(TapBooleanValue.class, "bit(1)", TapValue::getValue);
        codecRegistry.registerFromTapValue(TapBooleanValue.class, "bit", TapValue::getValue);
        codecRegistry.registerToTapValue(DateTimeOffset.class, (ToTapValueCodec<TapValue<?, ?>>) (value, tapType) -> {
            TapDateTimeValue tapDateTimeValue = new TapDateTimeValue();
            tapDateTimeValue.setOriginValue(value);
            tapDateTimeValue.setTapType((TapDateTime) tapType);
            tapDateTimeValue.setOriginType("datetimeoffset");
            if (null != value) {
                DateTime dateTime = new DateTime(((DateTimeOffset) value).getTimestamp());
                dateTime.setTimeZone(TimeZone.getTimeZone("GMT" + ((DateTimeOffset) value).getOffsetDateTime().getOffset().getId()));
                tapDateTimeValue.setValue(dateTime);
            } else {
                tapDateTimeValue.setValue(null);
            }
            return tapDateTimeValue;
        });
        connectorFunctions.supportGetTableInfoFunction(this::getTableInfo);
        connectorFunctions.supportQueryHashByAdvanceFilterFunction(this::queryTableHash);
        connectorFunctions.supportTransactionBeginFunction(this::beginTransaction);
        connectorFunctions.supportTransactionCommitFunction(this::commitTransaction);
        connectorFunctions.supportTransactionRollbackFunction(this::rollbackTransaction);

    }

    protected void dropPartitionTable(TapConnectorContext context, TapDropTableEvent dropPartitionTableEvent) throws SQLException {
        List<String> sql = mssqlPartitionContext.getDropPartitionTableSql(dropPartitionTableEvent);
        try {
            jdbcRunner.batchExecute(sql);
        } catch (Throwable e) {
            throw new RuntimeException(String.format("Execute drop table sql failed: %s\n Sql: %s", e.getMessage(), sql), e);
        }
    }

    //仅支持仅追加新分区方式的新增分区，新增分区会导致旧分区分区策略被修改的场景暂不支持
    protected CreateTableOptions createPartitionTable(TapConnectorContext context, TapCreateTableEvent partitionTableEvent) throws SQLException {
        TapTable table = partitionTableEvent.getTable();
        CreateTableOptions createTableOptions = new CreateTableOptions();
        if (!jdbcRunner.queryAllTables(Collections.singletonList(table.getId())).isEmpty()) {
            createTableOptions.setTableExists(true);
            return createTableOptions;
        }
        List<String> sqls = new ArrayList<>();
        try {
            Optional.ofNullable(mssqlPartitionContext.getPartitionTableSql(partitionTableEvent)).ifPresent(sqls::addAll);
        } catch (Exception e) {
            throw new CoreException(e, String.format("Append create table sql(s) failed: %s | Table: %s", e.getMessage(), table.getId()));
        }
        if (!sqls.isEmpty()) {
            try {
                jdbcRunner.batchExecute(sqls);
            } catch (Throwable e) {
                throw new CoreException(e, String.format("Execute create table sql failed: %s\n Sql: %s", e.getMessage(), String.join("\n - ", sqls)));
            }
        }
        createTableOptions.setTableExists(false);
        return createTableOptions;
    }


    protected CreateTableOptions createSubPartitionTable(TapConnectorContext context, TapCreateTableEvent tableEvent, String subTableId) throws SQLException {
        Log log = context.getLog();
        if (null == subTableId) return CreateTableOptions.create();
        final TapTable table = tableEvent.getTable();
        final String masterTableId = table.getPartitionMasterTableId();
        // 查询分区函数
        PartitionFunctionAndStageResult result = mssqlPartitionContext.queryPartitionFunctions(masterTableId);
        if (Objects.isNull(result.getPartitionSchemaName()) || Objects.isNull(result.getPartitionFunctionName())) {
            return createPartitionTable(context, tableEvent);
        }
        String sql = "ALTER PARTITION SCHEME [" + result.getPartitionSchemaName() + "] NEXT USED [PRIMARY]";
        try {
            jdbcRunner.execute(sql);
        } catch (Exception e) {
            throw new CoreException(e, String.format("Update partition schema next used file group failed, sql: %s, message: %s", sql, e.getMessage()));
        }
        TapPartition partitionInfo = table.getPartitionInfo();
        String originalSubPartitionTableName = table.getAncestorsName() != null ? table.getAncestorsName() : table.getId();
        MSSQLPartitionInfo mssqlPartitionInfo = mssqlPartitionContext.getPartitionInfo(partitionInfo, originalSubPartitionTableName, log);
        if (null == mssqlPartitionInfo) {
            log.warn("Unable find any partition info, can not create sub partition table");
            return CreateTableOptions.create();
        }
        if (mssqlPartitionContext.getUniqueBoundaries().contains(mssqlPartitionInfo.getLeftValue())) {
            log.warn("Partition function already exists");
            return CreateTableOptions.create();
        }
        mssqlPartitionContext.getUniqueBoundaries().add(mssqlPartitionInfo.getLeftValue());
        sql = String.format("ALTER PARTITION FUNCTION %s() SPLIT RANGE (%s)", result.getPartitionFunctionName(), mssqlPartitionInfo.getLeftValue());
        //修改分区函数
        try {
            jdbcRunner.execute(sql);
        } catch (Exception e) {
            throw new CoreException(e, String.format("Update partition function failed, sql: %s, message: %s", sql, e.getMessage()));
        }
        return CreateTableOptions.create().tableExists(true);
    }

    protected RetryOptions errorHandle(TapConnectionContext tapConnectionContext, PDKMethod pdkMethod, Throwable throwable) {
        RetryOptions retryOptions = RetryOptions.create();
        Throwable sqlserverException = matchThrowable(throwable, SQLServerException.class);
        if (null != sqlserverException
                && null != ((SQLServerException) sqlserverException).getSQLState() && "08S01".equals(((SQLServerException) sqlserverException).getSQLState())) {
            retryOptions.needRetry(true);
        }
        Throwable SQLRecoverableException = matchThrowable(throwable, SQLRecoverableException.class);
        if (null != SQLRecoverableException) {
            retryOptions.needRetry(true);
        }
        Throwable sqlException = matchThrowable(throwable, SQLException.class);
        if (null != sqlException) {
            String message = sqlException.getMessage();
            if (null != message) {
                Pattern pattern = Pattern.compile("HikariDataSource.*has been closed");
                if (pattern.matcher(message).find()) {
                    retryOptions.needRetry(true);
                }
            }
        }
        return retryOptions;
    }

    protected void getTableNames(TapConnectionContext tapConnectionContext, int batchSize, Consumer<List<String>> listConsumer) throws SQLException {
        jdbcRunner.queryAllTables(list(), batchSize, listConsumer);
    }

    protected TapField makeTapField(DataMap dataMap) {
        return new MssqlColumn(dataMap).getTapField();
    }

    @Override
    public ConnectionOptions connectionTest(TapConnectionContext connectionContext, Consumer<TestItem> consumer) {
        config = new MssqlConfig().log(connectionContext.getLog()).load(connectionContext.getConnectionConfig());
        ConnectionOptions connectionOptions = ConnectionOptions.create();
        connectionOptions.connectionString(config.getConnectionString());
        try (
                MssqlTest mssqlTest = new MssqlTest(config, consumer, connectionOptions)
        ) {
            mssqlTest.testOneByOne();
        }
        return connectionOptions;
    }

    protected String getSchemaAndTable(String tableId) {
        return String.format("[%s].[%s]", MssqlMaker.escape(config.getSchema(), "]"), MssqlMaker.escape(tableId, "]"));
    }

    protected CreateTableOptions createTableV2(TapConnectorContext tapConnectorContext, TapCreateTableEvent tapCreateTableEvent) throws SQLException {
        if (config.getDoubleActive()) {
            createDoubleActiveTempTable();
        }
        TapTable table = tapCreateTableEvent.getTable();
        CreateTableOptions createTableOptions = new CreateTableOptions();
        if (jdbcRunner.queryAllTables(Collections.singletonList(table.getId())).size() > 0) {
            createTableOptions.setTableExists(true);
            return createTableOptions;
        }
        List<String> sqls;
        try {
            sqls = MssqlMaker.createTable(table, config);
        } catch (Exception e) {
            throw new RuntimeException(String.format("Append create table sql(s) failed: %s | Table: %s", e.getMessage(), table.getId()), e);
        }
        try {
            jdbcRunner.batchExecute(sqls);
        } catch (Throwable e) {
            throw new RuntimeException(String.format("Execute create table sql failed: %s\n Sql: %s", e.getMessage(), String.join("\n - ", sqls)), e);
        }
        createTableOptions.setTableExists(false);
        return createTableOptions;
    }

    protected void clearTable(TapConnectorContext tapConnectorContext, TapClearTableEvent tapClearTableEvent) {
        String database = config.getDatabase();
        String schema = config.getSchema();
        String table = tapClearTableEvent.getTableId();
        try {
            jdbcRunner.execute(MssqlMaker.truncateTable(database, schema, table));
        } catch (Throwable e) {
            // TRUNCATE is not allowed by mssql in some cases, here we use delete instead of truncate to clear the
            // table, more details at:
            // https://docs.microsoft.com/en-us/sql/t-sql/statements/truncate-table-transact-sql?view=sql-server-2017#restrictions
            tapLogger.warn("Failed to TRUNCATE the table, msg: " + e.getMessage() + "; Try to delete instead");
            String sql;
            try {
                sql = MssqlMaker.deleteTable(database, schema, table);
            } catch (Exception ex) {
                throw new RuntimeException(String.format("Append delete table sql failed: %s | Database: %s | Schema: %s | Table name: %s", ex.getMessage(), database, schema, table), ex);
            }
            try {
                jdbcRunner.execute(sql);
            } catch (Throwable e2) {
                throw new RuntimeException(String.format("Execute delete table sql failed: %s\n Sql: %s", e2.getMessage(), sql), e2);
            }
        }
    }

    protected void dropTable(TapConnectorContext tapConnectorContext, TapDropTableEvent tapDropTableEvent) {
        String sql;
        try {
            sql = MssqlMaker.dropTable(config.getDatabase(), config.getSchema(), tapDropTableEvent.getTableId());
        } catch (Exception e) {
            throw new RuntimeException(String.format("Append drop table sql failed: %s | Database: %s | Schema: %s | Table name: %s", e.getMessage(), config.getDatabase(), config.getSchema(), tapDropTableEvent.getTableId()));
        }
        try {
            jdbcRunner.execute(sql);
        } catch (Throwable e) {
            throw new RuntimeException(String.format("Execute drop table sql failed: %s\n Sql: %s", e.getMessage(), sql), e);
        }
    }

    protected TapIndex makeTapIndex(String key, List<DataMap> value) {
        TapIndex tapIndex = super.makeTapIndex(key, value);
        tapIndex.setCluster(value.stream().anyMatch(v -> ("CLUSTERED".equals(v.getString("indexType")))));
        return tapIndex;
    }

    protected void createIndex(TapConnectorContext tapConnectorContext, TapTable tapTable, TapCreateIndexEvent createIndexEvent) {
        List<TapIndex> indexes = createIndexEvent.getIndexList().stream()
                .filter(v -> discoverIndex(tapTable.getId()).stream().noneMatch(i -> DbKit.ignoreCreateIndex(i, v))).collect(Collectors.toList());
        if (EmptyKit.isEmpty(indexes)) {
            return;
        }
        List<String> sqls;
        try {
            sqls = mssqlPartitionContext.createTableIndexes(config.getSchema(), tapTable, indexes);
        } catch (Exception e) {
            tapLogger.warn(String.format("Append create index sql(s) failed: %s | Database: %s | Schema: %s | Table name: %s", e.getMessage(), config.getDatabase(), config.getSchema(), tapTable.getId()), e);
            return;
        }
        try {
            jdbcRunner.batchExecute(sqls);
        } catch (Throwable e) {
            tapLogger.warn(String.format("Execute create index sql(s) failed: %s\n Sql(s): %s", e.getMessage(), String.join("\n - ", sqls)));
        }
    }

    protected void beforeWriteRecord(TapTable tapTable) throws SQLException {
        List<String> removedColumn = new ArrayList<>();
        if (EmptyKit.isNull(writtenTableMap.get(tapTable.getId()))) {
            List<String> physicalColumn = jdbcContext.queryAllColumns(Collections.singletonList(tapTable.getId())).stream().map(v -> v.getString("columnName").toLowerCase()).collect(Collectors.toList());
            if (EmptyKit.isNotEmpty(physicalColumn)) {
                removedColumn.addAll(tapTable.getNameFieldMap().keySet().stream().filter(v -> !physicalColumn.contains(v.toLowerCase())).collect(Collectors.toList()));
            }
            writtenTableMap.put(tapTable.getId(), DataMap.create().kv(HAS_REMOVED_COLUMN, removedColumn));
        } else {
            removedColumn.addAll(writtenTableMap.get(tapTable.getId()).getValue(HAS_REMOVED_COLUMN, new ArrayList<>()));
        }
        List<String> autoIncFields = new ArrayList<>();
        if (config.getCreateAutoInc()) {
            if (EmptyKit.isNull(writtenTableMap.get(tapTable.getId()).get(HAS_AUTO_INCR))) {
                List<TapField> fields = tapTable.getNameFieldMap().values().stream().filter(TapField::getAutoInc).collect(Collectors.toList());
                autoIncFields.addAll(fields.stream().map(TapField::getName).collect(Collectors.toList()));
                writtenTableMap.get(tapTable.getId()).put(HAS_AUTO_INCR, autoIncFields);
            } else {
                autoIncFields.addAll(writtenTableMap.get(tapTable.getId()).getValue(HAS_AUTO_INCR, new ArrayList<>()));
            }
        }
    }

    protected void writeRecord(TapConnectorContext connectorContext, List<TapRecordEvent> tapRecordEvents, TapTable tapTable, Consumer<WriteListResult<TapRecordEvent>> writeListResultConsumer) throws SQLException {
        beforeWriteRecord(tapTable);
        List<String> removedColumn = writtenTableMap.get(tapTable.getId()).getValue(HAS_REMOVED_COLUMN, new ArrayList<>());
        List<String> autoIncFields = writtenTableMap.get(tapTable.getId()).getValue(HAS_AUTO_INCR, new ArrayList<>());
        String insertDmlPolicy = connectorContext.getConnectorCapabilities().getCapabilityAlternative(ConnectionOptions.DML_INSERT_POLICY);
        if (insertDmlPolicy == null) {
            insertDmlPolicy = ConnectionOptions.DML_INSERT_POLICY_UPDATE_ON_EXISTS;
        }
        String updateDmlPolicy = connectorContext.getConnectorCapabilities().getCapabilityAlternative(ConnectionOptions.DML_UPDATE_POLICY);
        if (updateDmlPolicy == null) {
            updateDmlPolicy = ConnectionOptions.DML_UPDATE_POLICY_IGNORE_ON_NON_EXISTS;
        }
        NormalRecordWriter mssqlRecordWriterV2;
        if (isTransaction) {
            String threadName = Thread.currentThread().getName();
            Connection connection;
            if (transactionConnectionMap.containsKey(threadName)) {
                connection = transactionConnectionMap.get(threadName);
            } else {
                connection = jdbcRunner.getConnection();
                transactionConnectionMap.put(threadName, connection);
            }
            mssqlRecordWriterV2 = new MssqlRecordWriterV2(jdbcRunner, connection, tapTable)
                    .setVersion(version)
                    .setInsertPolicy(insertDmlPolicy)
                    .setUpdatePolicy(updateDmlPolicy)
                    .setTapLogger(tapLogger)
                    .setRemovedColumn(removedColumn);
        } else {
            mssqlRecordWriterV2 = new MssqlRecordWriterV2(jdbcRunner, tapTable)
                    .setVersion(version)
                    .setInsertPolicy(insertDmlPolicy)
                    .setUpdatePolicy(updateDmlPolicy)
                    .setTapLogger(tapLogger)
                    .setRemovedColumn(removedColumn);
        }
        if (config.getCreateAutoInc() && EmptyKit.isNotEmpty((List) writtenTableMap.get(tapTable.getId()).get(HAS_AUTO_INCR))) {
            mssqlRecordWriterV2.closeIdentity();
        }
        mssqlRecordWriterV2.closeConstraintCheck();
        if (config.getCreateAutoInc() && EmptyKit.isNotEmpty(autoIncFields)
                && "CDC".equals(tapRecordEvents.get(0).getInfo().get(TapRecordEvent.INFO_KEY_SYNC_STAGE))) {
            mssqlRecordWriterV2.setAutoIncFields(autoIncFields);
            mssqlRecordWriterV2.write(tapRecordEvents, writeListResultConsumer, this::isAlive);
            if (EmptyKit.isNotEmpty(mssqlRecordWriterV2.getAutoIncMap())) {
                List<String> alterSqls = new ArrayList<>();
                mssqlRecordWriterV2.getAutoIncMap().forEach((k, v) -> {
                    alterSqls.add("DBCC CHECKIDENT ('" + getSchemaAndTable(tapTable.getId()) + "', RESEED, " + (Long.parseLong(String.valueOf(v)) + config.getAutoIncJumpValue() - tapTable.getNameFieldMap().get(k).getAutoIncrementValue()) + ")");
                });
                jdbcContext.batchExecute(alterSqls);
            }
        } else {
            mssqlRecordWriterV2.write(tapRecordEvents, writeListResultConsumer, this::isAlive);
        }
    }

    protected void afterInitialSync(TapConnectorContext connectorContext, TapTable tapTable) throws Throwable {
        beforeWriteRecord(tapTable);
        List<String> autoIncFields = writtenTableMap.get(tapTable.getId()).getValue(HAS_AUTO_INCR, new ArrayList<>());
        autoIncFields.forEach(field -> {
            try (
                    Connection connection = jdbcContext.getConnection();
                    Statement statement = connection.createStatement();
                    ResultSet resultSet = statement.executeQuery("select max(" + field + ") from " + getSchemaAndTable(tapTable.getId()))
            ) {
                if (resultSet.next()) {
                    statement.execute("DBCC CHECKIDENT ('" + getSchemaAndTable(tapTable.getId()) + "', RESEED, " + (resultSet.getLong(1) + config.getAutoIncJumpValue() - tapTable.getNameFieldMap().get(field).getAutoIncrementValue()) + ")");
                    connection.commit();
                }
            } catch (SQLException e) {
                tapLogger.warn("Failed to get auto increment value for table {} field {}", tapTable.getId(), field, e);
            }
        });
    }

//    protected long batchCount(TapConnectorContext tapConnectorContext, TapTable tapTable) throws Throwable {
//        AtomicLong count = new AtomicLong(0);
//        jdbcRunner.queryWithNext(MssqlMaker.countOnTable(config.getDatabase(), config.getSchema(), tapTable.getName()),
//                rs -> count.set(rs.getLong(1)));
//
//        return count.get();
//    }

//    protected void batchRead(TapConnectorContext tapConnectorContext, TapTable tapTable, Object offsetState, int eventBatchSize, BiConsumer<List<TapEvent>, Object> eventsOffsetConsumer) throws Throwable {
//        SnapshotOffset snapshotOffset;
//        // completely blank task
//        if (null == offsetState) {
//            snapshotOffset = new SnapshotOffset(new CommonSqlMaker().getOrderByUniqueKey(tapTable), 0L);
//        }
//        //with offset
//        else {
//            snapshotOffset = (SnapshotOffset) offsetState;
//        }
//
//        String sql;
//        if (version.contains("2008") || version.contains("2005")) {
//            sql = String.format("SELECT * FROM(SELECT ROW_NUMBER() OVER(%s) rowNumber, * FROM %s) myTable WHERE rowNumber > %s",
//                    snapshotOffset.getSortString(), MssqlMaker.formatTableName(tapTable, config), snapshotOffset.getOffsetValue());
//        } else {
//            // OFFSET is not supported until SQL Server 2012
//            sql = "SELECT * FROM " + MssqlMaker.formatTableName(tapTable, config) + snapshotOffset.getSortString() + " OFFSET " + snapshotOffset.getOffsetValue() + " ROWS";
//        }
//
//        jdbcRunner.query(sql, rs -> {
//            List<TapEvent> tapEvents = list();
//            //get all column names
//            List<String> columnNames = DbKit.getColumnsFromResultSet(rs);
//            while (isAlive() && rs.next()) {
//                tapEvents.add(insertRecordEvent(DbKit.getRowFromResultSet(rs, columnNames), tapTable.getId()));
//                if (tapEvents.size() == eventBatchSize) {
//                    snapshotOffset.setOffsetValue(snapshotOffset.getOffsetValue() + eventBatchSize);
//                    eventsOffsetConsumer.accept(tapEvents, snapshotOffset);
//                    tapEvents = list();
//                }
//            }
//            //last events those less than eventBatchSize
//            if (EmptyKit.isNotEmpty(tapEvents)) {
//                snapshotOffset.setOffsetValue(snapshotOffset.getOffsetValue() + tapEvents.size());
//                eventsOffsetConsumer.accept(tapEvents, snapshotOffset);
//            }
//        });
//
//    }

    private void streamRead(TapConnectorContext nodeContext, List<String> tableList, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        cdcRunner = new MssqlCdcRunner(jdbcRunner, tapLogger);
        if (offsetState instanceof String) {
            offsetState = TapSimplify.fromJson((String) offsetState, CdcOffset.class);
        }
        cdcRunner.withConsumer(recordSize, consumer)
                .withTables(tableList, nodeContext.getTableMap())
                .withOffset((CdcOffset) offsetState)
                .withTimeZoneOffset(timeZoneOffset);
        cdcRunner.startCdcRunner();
    }

    public Set<String> getCdcTables(MssqlJdbcRunner jdbcRunner) {
        Set<String> cdcTables = new HashSet<>();
        try {
            jdbcRunner.query("sys.sp_cdc_help_change_data_capture", rs -> {
                String schema, table;
                while (rs.next()) {
                    schema = rs.getString("source_schema").trim();
                    table = rs.getString("source_table").trim();
                    if (jdbcRunner.getConfig().getSchema().equalsIgnoreCase(schema)) {
                        cdcTables.add(table);
                    }
                }
            });
        } catch (Throwable e) {
            tapLogger.warn("Failed to get cdc tables for {} failed", jdbcRunner.getConfig().getSchema());
        }
        return cdcTables;
    }

    protected void buildCT(TapTable table) {
        tapLogger.info("building CT table for table {}", table.getId());
        try {
            Collection<String> primaryKeys = table.primaryKeys();
            jdbcRunner.execute(String.format(MssqlCdcRunner.ENABLE_CDC_TABLE, MssqlMaker.escape(config.getSchema(), "'"), MssqlMaker.escape(table.getId(), "'"), MssqlMaker.escape(config.getSchema(), "'"), MssqlMaker.escape(table.getId(), "'"), EmptyKit.isEmpty(primaryKeys) ? "0" : "1"));
        } catch (SQLException e) {
            tapLogger.warn("auto build CT table for table {} failed, please check", table.getId());
        }
    }

    private Object timestampToStreamOffset(TapConnectorContext connectorContext, Long offsetStartTime) throws SQLException {
        Set<String> cdcTables = getCdcTables(jdbcRunner);
        io.tapdata.entity.utils.cache.Iterator<Entry<TapTable>> iterator = connectorContext.getTableMap().iterator();
        while (iterator.hasNext()) {
            Entry<TapTable> entry = iterator.next();
            if (cdcTables.contains(entry.getKey())) {
                continue;
            }
            buildCT(entry.getValue());
        }
        if (Boolean.TRUE.equals(config.getDoubleActive())) {
            createDoubleActiveTempTable();
            TapTable doubleActive = new TapTable("_tap_double_active");
            doubleActive.add(new TapField("c1", "int").isPrimaryKey(true));
            buildCT(doubleActive);
        }
        try {
            return (new MssqlCdcRunner(jdbcRunner, tapLogger)).getOffsetByTs(offsetStartTime);
        } catch (SQLException e) {
            throw new TapDbCdcConfigInvalidEx("sqlserver", new Exception("database does not open CDC mode"));
        } catch (Throwable e) {
            throw new RuntimeException("failed to get offset, err: " + e.getMessage());
        }

    }

    protected void queryByFilter(TapConnectorContext connectorContext, List<TapFilter> filters, TapTable tapTable, Consumer<List<FilterResult>> listConsumer) {
        Set<String> columnNames = tapTable.getNameFieldMap().keySet();
        List<FilterResult> filterResults = new LinkedList<>();
        for (TapFilter filter : filters) {
            String sql = "SELECT * FROM " + MssqlMaker.formatTableName(tapTable, config) + " WHERE " + MssqlMaker.buildKeyAndValue(filter.getMatch(), "AND", "=");
            FilterResult filterResult = new FilterResult();
            try {
                jdbcRunner.queryWithNext(sql, resultSet -> {
                    DataMap dataMap = DbKit.getRowFromResultSet(resultSet, columnNames);
                    processDataMap(dataMap, tapTable);
                    filterResult.setResult(dataMap);
                });
            } catch (Throwable e) {
                filterResult.setError(e);
            } finally {
                filterResults.add(filterResult);
            }
        }
        listConsumer.accept(filterResults);
    }

    private void queryByAdvanceFilter(TapConnectorContext connectorContext, TapAdvanceFilter filter, TapTable table, Consumer<FilterResults> consumer) throws Throwable {
        String sql = MssqlMaker.buildSqlByAdvanceFilter(table, config, filter);
        jdbcRunner.query(sql, resultSet -> {
            FilterResults filterResults = new FilterResults();
            while (resultSet.next()) {
                List<String> allColumn = DbKit.getColumnsFromResultSet(resultSet);
                DataMap dataMap = DbKit.getRowFromResultSet(resultSet, allColumn);
                processDataMap(dataMap, table);
                filterResults.add(dataMap);
                if (filterResults.getResults().size() == BATCH_ADVANCE_READ_LIMIT) {
                    consumer.accept(filterResults);
                    filterResults = new FilterResults();
                }
            }
            if (EmptyKit.isNotEmpty(filterResults.getResults())) {
                consumer.accept(filterResults);
            }
        });
    }

    protected void fieldDDLHandler(TapConnectorContext tapConnectorContext, TapFieldBaseEvent tapFieldBaseEvent) throws SQLException {
        List<String> sqls = fieldDDLHandlers.handle(tapFieldBaseEvent, tapConnectorContext);
        if (null == sqls) {
            return;
        }
        try {
            jdbcRunner.batchExecute(sqls);
        } catch (SQLException e) {
            String message = e.getMessage();
            if (Pattern.compile(".*The object 'DF_.*' is dependent on column.*", Pattern.CASE_INSENSITIVE).matcher(message).matches()) {
                tapLogger.warn("To delete this field, run command in sql server 'alter table TBL drop constraint <constraint name>'");
            }
            throw e;
        }
    }

    protected List<String> alterFieldAttr(TapFieldBaseEvent tapFieldBaseEvent, TapConnectorContext tapConnectorContext) {
        if (!(tapFieldBaseEvent instanceof TapAlterFieldAttributesEvent)) {
            return null;
        }
        TapAlterFieldAttributesEvent tapAlterFieldAttributesEvent = (TapAlterFieldAttributesEvent) tapFieldBaseEvent;
        return ddlSqlGenerator.alterColumnAttr(config, tapAlterFieldAttributesEvent);
    }

    protected List<String> dropField(TapFieldBaseEvent tapFieldBaseEvent, TapConnectorContext tapConnectorContext) {
        if (!(tapFieldBaseEvent instanceof TapDropFieldEvent)) {
            return null;
        }
        TapDropFieldEvent tapDropFieldEvent = (TapDropFieldEvent) tapFieldBaseEvent;
        return ddlSqlGenerator.dropColumn(config, tapDropFieldEvent);
    }

    protected List<String> alterFieldName(TapFieldBaseEvent tapFieldBaseEvent, TapConnectorContext tapConnectorContext) {
        if (!(tapFieldBaseEvent instanceof TapAlterFieldNameEvent)) {
            return null;
        }
        TapAlterFieldNameEvent tapAlterFieldNameEvent = (TapAlterFieldNameEvent) tapFieldBaseEvent;
        return ddlSqlGenerator.alterColumnName(config, tapAlterFieldNameEvent);
    }

    protected List<String> newField(TapFieldBaseEvent tapFieldBaseEvent, TapConnectorContext tapConnectorContext) {
        if (!(tapFieldBaseEvent instanceof TapNewFieldEvent)) {
            return null;
        }
        TapNewFieldEvent tapNewFieldEvent = (TapNewFieldEvent) tapFieldBaseEvent;
        return ddlSqlGenerator.addColumn(config, tapNewFieldEvent);
    }

    private void checkConnection(TapConnectionContext connectionContext, List<String> items, Consumer<ConnectionCheckItem> consumer) {
        if (null == mssqlTest) {
            return;
        }
        ConnectionCheckItem testPing = mssqlTest.testPing();
        consumer.accept(testPing);
        if (testPing.getResult() == ConnectionCheckItem.RESULT_FAILED) {
            return;
        }
        ConnectionCheckItem testConnection = mssqlTest.testConnection();
        consumer.accept(testConnection);
    }

    @Override
    protected String getBatchReadSelectSql(TapTable tapTable) {
        if (tapTable.getNameFieldMap().size() > 50) {
            return String.format("SELECT * FROM [%s].[%s]", MssqlMaker.escape(config.getSchema(), "]"), MssqlMaker.escape(tapTable.getId(), "]"));
        } else {
            String columns = tapTable.getNameFieldMap().keySet().stream().map(c -> "[" + c.replace("]", "]]") + "]").collect(Collectors.joining(","));
            return String.format("SELECT %s FROM [%s].[%s]", columns, MssqlMaker.escape(config.getSchema(), "]"), MssqlMaker.escape(tapTable.getId(), "]"));
        }
    }

    private TableInfo getTableInfo(TapConnectionContext tapConnectorContext, String tableName) throws Throwable {
        DataMap dataMap = jdbcRunner.getTableInfo(tableName);
        TableInfo tableInfo = TableInfo.create();
        String rows = getNumber(dataMap.getString("rows"));
        long nums = 0l;
        if (StringUtils.isNumeric((rows))) {
            nums = Long.valueOf(rows);
        }
        tableInfo.setNumOfRows(nums);
        String data = getNumber(dataMap.getString("data"));
        long size = 0l;
        if (StringUtils.isNumeric((data))) {
            size = Long.valueOf(data);
        }
        tableInfo.setStorageSize(size * 1024);
        return tableInfo;
    }


    private String getNumber(String value) {
        if (value == null) {
            return "0";
        }
        String regex = "[^0-9]";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(value);
        return matcher.replaceAll("").trim();
    }

    private String buildHashSql(TapAdvanceFilter filter, TapTable table) {
        StringBuilder sql = new StringBuilder("select sum(mod.md5) from( " +
                "select case when t.num < 0 then t.num + 18446744073709551616 when t.num > 0 then t.num end %64 as md5 from ( SELECT ");
        sql.append("CONVERT(bigint, CONVERT(VARBINARY(50), substring(CONVERT(NVARCHAR(32), HASHBYTES('MD5',CAST(CONCAT_WS('',");
        LinkedHashMap<String, TapField> nameFieldMap = table.getNameFieldMap();
        java.util.Iterator<Map.Entry<String, TapField>> entryIterator = nameFieldMap.entrySet().iterator();
        while (entryIterator.hasNext()) {
            Map.Entry<String, TapField> next = entryIterator.next();
            String fieldName = next.getKey();
            TapField field = nameFieldMap.get(next.getKey());
            byte type = next.getValue().getTapType().getType();
            if (type == TapType.TYPE_NUMBER) {
                if (field.getDataType().toLowerCase().contains("decimal")
                        || field.getDataType().toLowerCase().contains("numeric")
                        || field.getDataType().toLowerCase().contains("real")
                        || field.getDataType().toLowerCase().contains("float")
                        || field.getDataType().toLowerCase().contains("double")) {
                    sql.append(String.format("CAST(%s as bigint)", fieldName)).append(",");
                    continue;
                }
                if (field.getDataType().toLowerCase().contains("money")) {
                    sql.append(String.format("CAST(FLOOR( %s ) as bigint)", fieldName)).append(",");
                    continue;
                }
            }
            if (type == TapType.TYPE_STRING && (field.getDataType().toLowerCase().startsWith("char(") ||
                    field.getDataType().toLowerCase().startsWith("nchar("))) {
                sql.append(String.format("TRIM( %s )", fieldName)).append(",");
                continue;
            }

            switch (type) {
                case TapType.TYPE_DATETIME:
                    sql.append(String.format(JdbcUtil.DATE_TIME_FILTER_SQL,
                            JdbcUtil.BEGIN_DATE_TIME, fieldName)).append(",");
                    break;
                case TapType.TYPE_BINARY:
                    break;
                case TapType.TYPE_TIME:
                    sql.append("cast(" + fieldName + " as varchar(8))").append(",");
                    break;
                default:
                    sql.append(fieldName).append(",");
                    break;
            }
        }
        sql = new StringBuilder(sql.substring(0, sql.length() - 1));
        sql.append(" ) as varchar(max) )), 2) , 1, 16), 2),2)  as num from ").append("\"" + table.getName() + "\" ");
        sql.append(commonSqlMaker.buildCommandWhereSql(filter, ""));
        sql.append(") t) mod");
        return sql.toString();
    }

    protected void queryTableHash(TapConnectorContext connectorContext, TapAdvanceFilter filter, TapTable table, Consumer<TapHashResult<String>> consumer) throws Throwable {
        String sql = buildHashSql(filter, table);
        jdbcContext.query(sql, resultSet -> {
            if (isAlive() && resultSet.next()) {
                consumer.accept(TapHashResult.create().withHash(resultSet.getString(1)));
            }
        });
    }

    @Override
    protected void processDataMap(DataMap dataMap, TapTable tapTable) {
        if (!config.getOldVersionTimezone()) {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                Object value = entry.getValue();
                if (value instanceof Timestamp) {
                    if (!tapTable.getNameFieldMap().containsKey(entry.getKey())) {
                        continue;
                    }
                    if (!tapTable.getNameFieldMap().get(entry.getKey()).getDataType().startsWith("datetimeoffset")) {
                        entry.setValue(((Timestamp) value).toLocalDateTime().minusHours(config.getZoneOffsetHour()));
                    } else {
                        entry.setValue(((Timestamp) value).toLocalDateTime().atZone(ZoneId.systemDefault()));
                    }
                } else if (value instanceof java.sql.Date) {
                    entry.setValue(Instant.ofEpochMilli(((Date) value).getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime());
                } else if (value instanceof Time) {
                    entry.setValue(Instant.ofEpochMilli(((Time) value).getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime().minusHours(config.getZoneOffsetHour()));
                }
            }
        }
    }

    @Override
    protected String getHashSplitModConditions(TapTable tapTable, int maxSplit, int currentSplit) {
        return "ABS(" + getHashSplitStringSql(tapTable) + "%" + maxSplit + ")=" + currentSplit;
    }

    @Override
    protected String getHashSplitStringSql(TapTable tapTable) {
        Collection<String> pks = tapTable.primaryKeys();
        if (pks.isEmpty()) {
            //无主键时，取前10个字段
            return "CONVERT(BIGINT, HASHBYTES('MD5', CONCAT('', [" + tapTable.getNameFieldMap().entrySet().stream()
                    .filter(v -> v.getValue().getDataType().contains("char") || v.getValue().getDataType().contains("int") || v.getValue().getDataType().contains("numeric"))
                    .map(v -> MssqlMaker.escape(v.getKey(), "]'")).limit(10).collect(Collectors.joining("], [")) + "])))";
        }
        return "CONVERT(BIGINT, HASHBYTES('MD5', CONCAT('', [" + pks.stream().map(v -> MssqlMaker.escape(v, "]'")).collect(Collectors.joining("], [")) + "])))";
    }
}
