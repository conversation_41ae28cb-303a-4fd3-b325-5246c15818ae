apply plugin: 'idea'
apply plugin: 'eclipse'
{{#sourceFolder}}
apply plugin: 'java'
{{/sourceFolder}}

group = '{{groupId}}'
version = '{{artifactVersion}}'

buildscript {
    repositories {
        mavenCentral()
        jcenter()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:2.3.+'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:1.5'
    }
}

repositories {
    jcenter()
}
{{#sourceFolder}}
sourceSets {
    main.java.srcDirs = ['{{sourceFolder}}']
}

{{/sourceFolder}}
if(hasProperty('target') && target == 'android') {

    apply plugin: 'com.android.library'
    apply plugin: 'com.github.dcendents.android-maven'

    android {
        compileSdkVersion 25
        buildToolsVersion '25.0.2'
        defaultConfig {
            minSdkVersion 14
            targetSdkVersion 25
        }
        compileOptions {
            {{#supportJava6}}
            sourceCompatibility JavaVersion.VERSION_1_6
            targetCompatibility JavaVersion.VERSION_1_6
            {{/supportJava6}}
            {{^supportJava6}}
            {{#java8}}
            sourceCompatibility JavaVersion.VERSION_1_8
            targetCompatibility JavaVersion.VERSION_1_8
            {{/java8}}
            {{^java8}}
            sourceCompatibility JavaVersion.VERSION_1_7
            targetCompatibility JavaVersion.VERSION_1_7
            {{/java8}}
            {{/supportJava6}}
        }

        // Rename the aar correctly
        libraryVariants.all { variant ->
            variant.outputs.each { output ->
                def outputFile = output.outputFile
                if (outputFile != null && outputFile.name.endsWith('.aar')) {
                    def fileName = "${project.name}-${variant.baseName}-${version}.aar"
                    output.outputFile = new File(outputFile.parent, fileName)
                }
            }
        }

        dependencies {
            provided 'javax.annotation:jsr250-api:1.0'
        }
    }

    afterEvaluate {
        android.libraryVariants.all { variant ->
            def task = project.tasks.create "jar${variant.name.capitalize()}", Jar
            task.description = "Create jar artifact for ${variant.name}"
            task.dependsOn variant.javaCompile
            task.from variant.javaCompile.destinationDir
            task.destinationDir = project.file("${project.buildDir}/outputs/jar")
            task.archiveName = "${project.name}-${variant.baseName}-${version}.jar"
            artifacts.add('archives', task);
        }
    }

    task sourcesJar(type: Jar) {
        from android.sourceSets.main.java.srcDirs
        classifier = 'sources'
    }

    artifacts {
        archives sourcesJar
    }

} else {

    apply plugin: 'java'
    apply plugin: 'maven'

    {{#supportJava6}}
    sourceCompatibility = JavaVersion.VERSION_1_6
    targetCompatibility = JavaVersion.VERSION_1_6
    {{/supportJava6}}
    {{^supportJava6}}
    {{#java8}}
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
    {{/java8}}
    {{^java8}}
    sourceCompatibility = JavaVersion.VERSION_1_7
    targetCompatibility = JavaVersion.VERSION_1_7
    {{/java8}}
    {{/supportJava6}}

    install {
        repositories.mavenInstaller {
            pom.artifactId = '{{artifactId}}'
        }
    }

    task execute(type:JavaExec) {
       main = System.getProperty('mainClass')
       classpath = sourceSets.main.runtimeClasspath
    }
}

dependencies {
    compile 'io.swagger:swagger-annotations:1.5.22'
    compile 'com.squareup.okhttp3:okhttp:3.14.2'
    compile 'com.squareup.okhttp3:logging-interceptor:3.14.2'
    compile 'com.google.code.gson:gson:2.8.5'
    compile 'io.gsonfire:gson-fire:1.8.3'
    {{#hasOAuthMethods}}
    compile group: 'org.apache.oltu.oauth2', name: 'org.apache.oltu.oauth2.client', version: '1.0.1'
    {{/hasOAuthMethods}}
    compile group: 'org.apache.commons', name: 'commons-lang3', version: '3.9'
    {{#joda}}
    compile 'joda-time:joda-time:2.9.9'
    {{/joda}}
    {{#threetenbp}}
    compile 'org.threeten:threetenbp:1.3.5'
    {{/threetenbp}}
    testCompile 'junit:junit:4.12'
}
