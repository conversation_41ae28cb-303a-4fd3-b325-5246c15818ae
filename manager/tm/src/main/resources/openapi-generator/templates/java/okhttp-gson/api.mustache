{{>licenseInfo}}

package {{package}};

import {{invokerPackage}}.ApiCallback;
import {{invokerPackage}}.ApiClient;
import {{invokerPackage}}.ApiException;
import {{invokerPackage}}.ApiResponse;
import {{invokerPackage}}.Configuration;
import {{invokerPackage}}.Pair;
import {{invokerPackage}}.ProgressRequestBody;
import {{invokerPackage}}.ProgressResponseBody;
{{#performBeanValidation}}
import {{invokerPackage}}.BeanValidationException;
{{/performBeanValidation}}

import com.google.gson.reflect.TypeToken;

import java.io.IOException;

{{#useBeanValidation}}
import javax.validation.constraints.*;
{{/useBeanValidation}}
{{#performBeanValidation}}
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.ValidatorFactory;
import javax.validation.executable.ExecutableValidator;
import java.util.Set;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
{{/performBeanValidation}}

import {{invokerPackage}}.model.*;

import java.lang.reflect.*;
{{^fullJavaUtil}}
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
{{/fullJavaUtil}}

{{#operations}}
public class {{classname}} {
    private ApiClient localVarApiClient;

    public {{classname}}() {
        this(Configuration.getDefaultApiClient());
    }

    public {{classname}}(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    {{#operation}}
    {{^vendorExtensions.x-group-parameters}}/**
     * Build call for {{operationId}}{{#allParams}}
     * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}}){{/required}}{{/allParams}}
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     {{#responses.0}}
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        {{#responses}}
        <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
        {{/responses}}
     </table>
     {{/responses.0}}
     {{#isDeprecated}}
     * @deprecated
     {{/isDeprecated}}
        {{#externalDocs}}
     * {{description}}
     * @see <a href="{{url}}">{{summary}} Documentation</a>
        {{/externalDocs}}
     */
    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    public{{/vendorExtensions.x-group-parameters}}{{#vendorExtensions.x-group-parameters}}private{{/vendorExtensions.x-group-parameters}} okhttp3.Call {{operationId}}Call({{#allParams}}{{{dataType}}} {{paramName}}, {{/allParams}}final ApiCallback _callback) throws ApiException {
        Object localVarPostBody = {{#bodyParam}}{{paramName}}{{/bodyParam}}{{^bodyParam}}new Object(){{/bodyParam}};

        // create path and map variables
        String localVarPath = "{{{path}}}"{{#pathParams}}
            .replaceAll("\\{" + "{{baseName}}" + "\\}", localVarApiClient.escapeString({{#collectionFormat}}localVarApiClient.collectionPathParameterToString("{{{collectionFormat}}}", {{{paramName}}}){{/collectionFormat}}{{^collectionFormat}}{{{paramName}}}.toString(){{/collectionFormat}})){{/pathParams}};

        {{javaUtilPrefix}}List<Pair> localVarQueryParams = new {{javaUtilPrefix}}ArrayList<Pair>();
        {{javaUtilPrefix}}List<Pair> localVarCollectionQueryParams = new {{javaUtilPrefix}}ArrayList<Pair>();
        {{#queryParams}}
        if ({{paramName}} != null) {
            localVarCollectionQueryParams.addAll(localVarApiClient.parameterToPair(buildQuery({{paramName}})));
        }

        {{/queryParams}}
        {{javaUtilPrefix}}Map<String, String> localVarHeaderParams = new {{javaUtilPrefix}}HashMap<String, String>();
        {{#headerParams}}
        if ({{paramName}} != null) {
            localVarHeaderParams.put("{{baseName}}", localVarApiClient.parameterToString({{paramName}}));
        }

        {{/headerParams}}
        {{javaUtilPrefix}}Map<String, Object> localVarFormParams = new {{javaUtilPrefix}}HashMap<String, Object>();
        {{#formParams}}
        if ({{paramName}} != null) {
            localVarFormParams.put("{{baseName}}", {{paramName}});
        }

        {{/formParams}}
        final String[] localVarAccepts = {
            {{#produces}}"{{{mediaType}}}"{{#hasMore}}, {{/hasMore}}{{/produces}}
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            {{#consumes}}"{{{mediaType}}}"{{#hasMore}}, {{/hasMore}}{{/consumes}}
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        localVarHeaderParams.put("Content-Type", localVarContentType);

        String[] localVarAuthNames = new String[] { {{#authMethods}}"{{name}}"{{#hasMore}}, {{/hasMore}}{{/authMethods}} };
        return localVarApiClient.buildCall(localVarPath, "{{httpMethod}}", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, _callback);
    }

    {{#queryParams}}
    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    private Map<String, String> buildQuery({{{dataType}}} {{paramName}}) {
        Map<String, String> filterMap = new HashMap<>();
        Object where = {{paramName}}.getWhere();
        Integer limit = {{paramName}}.getLimit();
        Integer skip = {{paramName}}.getSkip();
        List<String> order = {{paramName}}.getOrder();
        Object fields = {{paramName}}.getFields();

        if (where != null && where instanceof Map && !((Map) where).isEmpty()) {
            for (Map.Entry<String, Object> entry : ((Map<String, Object>) where).entrySet()) {
                filterMap.put("filter[where]" + entry.getKey(), entry.getValue().toString());
            }
        }

        if (limit != null && limit > 0) {
            filterMap.put("filter[limit]", limit.toString());
        }

        if (skip != null && skip >= 0) {
            filterMap.put("filter[skip]", skip.toString());
        }

        if (order != null && !order.isEmpty()) {
            for (int i = 0; i < order.size(); i++) {
                filterMap.put("filter[order][" + i + "]", order.get(i));
            }
        }

        if (fields != null) {
            Field[] declaredFields = fields.getClass().getDeclaredFields();
            if (declaredFields != null) {
                for (Field declaredField : declaredFields) {
                    if (!Modifier.isStatic(declaredField.getModifiers())) {
                        String originfieldName = declaredField.getName();
                        String fieldName = originfieldName.replace("_", "");
                        fieldName = fieldName.replace("-", "");
                        fieldName = fieldName.replace(".", "");
                        String getter = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                        try {
                            Method method = fields.getClass().getMethod(getter);
                            if (method != null) {
                                Object value = method.invoke(fields);
                                if (value != null
                                    && (value.equals(true)
                                    || value.equals(false))) {
                                    filterMap.put("filter[fields][" + (originfieldName.equals("id") ? "_id" : originfieldName) + "]", value.toString());
                                }
                            }
                        } catch (Exception e) {
                            continue;
                        }
                    }
                }
            }
        }

        return filterMap;
    }
    {{/queryParams}}

    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    @SuppressWarnings("rawtypes")
    private okhttp3.Call {{operationId}}ValidateBeforeCall({{#allParams}}{{{dataType}}} {{paramName}}, {{/allParams}}final ApiCallback _callback) throws ApiException {
        {{^performBeanValidation}}
        {{#allParams}}{{#required}}
        // verify the required parameter '{{paramName}}' is set
        if ({{paramName}} == null) {
            throw new ApiException("Missing the required parameter '{{paramName}}' when calling {{operationId}}(Async)");
        }
        {{/required}}{{/allParams}}

        okhttp3.Call localVarCall = {{operationId}}Call({{#allParams}}{{paramName}}, {{/allParams}}_callback);
        return localVarCall;

        {{/performBeanValidation}}
        {{#performBeanValidation}}
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            ExecutableValidator executableValidator = factory.getValidator().forExecutables();

            Object[] parameterValues = { {{#allParams}}{{paramName}}{{#hasMore}}, {{/hasMore}}{{/allParams}} };
            Method method = this.getClass().getMethod("{{operationId}}WithHttpInfo"{{#allParams}}, {{#isListContainer}}java.util.List{{/isListContainer}}{{#isMapContainer}}java.util.Map{{/isMapContainer}}{{^isListContainer}}{{^isMapContainer}}{{{dataType}}}{{/isMapContainer}}{{/isListContainer}}.class{{/allParams}});
            Set<ConstraintViolation<{{classname}}>> violations = executableValidator.validateParameters(this, method,
                    parameterValues);

            if (violations.size() == 0) {
                okhttp3.Call localVarCall = {{operationId}}Call({{#allParams}}{{paramName}}, {{/allParams}}_callback);
                return localVarCall;

            } else {
                throw new BeanValidationException((Set) violations);
            }
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
            throw new ApiException(e.getMessage());
        } catch (SecurityException e) {
            e.printStackTrace();
            throw new ApiException(e.getMessage());
        }

        {{/performBeanValidation}}
    }

    {{^vendorExtensions.x-group-parameters}}
    /**
     * {{summary}}
     * {{notes}}{{#allParams}}
     * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}}){{/required}}{{/allParams}}{{#returnType}}
     * @return {{returnType}}{{/returnType}}
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     {{#responses.0}}
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        {{#responses}}
        <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
        {{/responses}}
     </table>
     {{/responses.0}}
     {{#isDeprecated}}
     * @deprecated
     {{/isDeprecated}}
        {{#externalDocs}}
     * {{description}}
     * @see <a href="{{url}}">{{summary}} Documentation</a>
        {{/externalDocs}}
     */
    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    public {{#returnType}}{{{returnType}}} {{/returnType}}{{^returnType}}void {{/returnType}}{{operationId}}({{#allParams}}{{{dataType}}} {{paramName}}{{#hasMore}}, {{/hasMore}}{{/allParams}}) throws ApiException {
        {{#returnType}}ApiResponse<{{{returnType}}}> localVarResp = {{/returnType}}{{operationId}}WithHttpInfo({{#allParams}}{{paramName}}{{#hasMore}}, {{/hasMore}}{{/allParams}});{{#returnType}}
        return localVarResp.getData();{{/returnType}}
    }
    {{/vendorExtensions.x-group-parameters}}

    {{^vendorExtensions.x-group-parameters}}/**
     * {{summary}}
     * {{notes}}{{#allParams}}
     * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}}){{/required}}{{/allParams}}
     * @return ApiResponse&lt;{{#returnType}}{{returnType}}{{/returnType}}{{^returnType}}Void{{/returnType}}&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     {{#responses.0}}
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        {{#responses}}
        <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
        {{/responses}}
     </table>
     {{/responses.0}}
     {{#isDeprecated}}
     * @deprecated
     {{/isDeprecated}}
        {{#externalDocs}}
     * {{description}}
     * @see <a href="{{url}}">{{summary}} Documentation</a>
        {{/externalDocs}}
     */
    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    public{{/vendorExtensions.x-group-parameters}}{{#vendorExtensions.x-group-parameters}}private{{/vendorExtensions.x-group-parameters}} ApiResponse<{{#returnType}}{{{returnType}}}{{/returnType}}{{^returnType}}Void{{/returnType}}> {{operationId}}WithHttpInfo({{#allParams}}{{#useBeanValidation}}{{>beanValidationQueryParams}}{{/useBeanValidation}}{{{dataType}}} {{paramName}}{{#hasMore}}, {{/hasMore}}{{/allParams}}) throws ApiException {
        okhttp3.Call localVarCall = {{operationId}}ValidateBeforeCall({{#allParams}}{{paramName}}, {{/allParams}}null);
        {{#returnType}}Type localVarReturnType = new TypeToken<{{{returnType}}}>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);{{/returnType}}{{^returnType}}return localVarApiClient.execute(localVarCall);{{/returnType}}
    }

    {{^vendorExtensions.x-group-parameters}}/**
     * {{summary}} (asynchronously)
     * {{notes}}{{#allParams}}
     * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}}){{/required}}{{/allParams}}
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     {{#responses.0}}
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        {{#responses}}
        <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
        {{/responses}}
     </table>
     {{/responses.0}}
     {{#isDeprecated}}
     * @deprecated
     {{/isDeprecated}}
        {{#externalDocs}}
     * {{description}}
     * @see <a href="{{url}}">{{summary}} Documentation</a>
        {{/externalDocs}}
     */
    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    public{{/vendorExtensions.x-group-parameters}}{{#vendorExtensions.x-group-parameters}}private{{/vendorExtensions.x-group-parameters}} okhttp3.Call {{operationId}}Async({{#allParams}}{{{dataType}}} {{paramName}}, {{/allParams}}final ApiCallback<{{#returnType}}{{{returnType}}}{{/returnType}}{{^returnType}}Void{{/returnType}}> _callback) throws ApiException {

        okhttp3.Call localVarCall = {{operationId}}ValidateBeforeCall({{#allParams}}{{paramName}}, {{/allParams}}_callback);
        {{#returnType}}Type localVarReturnType = new TypeToken<{{{returnType}}}>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);{{/returnType}}{{^returnType}}localVarApiClient.executeAsync(localVarCall, _callback);{{/returnType}}
        return localVarCall;
    }
    {{#vendorExtensions.x-group-parameters}}

    public class API{{operationId}}Request {
        {{#requiredParams}}
        private final {{{dataType}}} {{paramName}};
        {{/requiredParams}}
        {{#optionalParams}}
        private {{{dataType}}} {{paramName}};
        {{/optionalParams}}

        private API{{operationId}}Request({{#requiredParams}}{{{dataType}}} {{paramName}}{{#hasMore}}, {{/hasMore}}{{/requiredParams}}) {
            {{#requiredParams}}
            this.{{paramName}} = {{paramName}};
            {{/requiredParams}}
        }

        {{#optionalParams}}
        /**
         * Set {{paramName}}
         * @param {{paramName}} {{description}} (optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}})
         * @return API{{operationId}}Request
         */
        public API{{operationId}}Request {{paramName}}({{{dataType}}} {{paramName}}) {
            this.{{paramName}} = {{paramName}};
            return this;
        }

        {{/optionalParams}}
        /**
         * Build call for {{operationId}}
         * @param _callback ApiCallback API callback
         * @return Call to execute
         * @throws ApiException If fail to serialize the request body object
         {{#responses.0}}
         * @http.response.details
         <table summary="Response Details" border="1">
            <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
            {{#responses}}
            <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
            {{/responses}}
         </table>
         {{/responses.0}}
         {{#isDeprecated}}
         * @deprecated
         {{/isDeprecated}}
         */
        {{#isDeprecated}}
        @Deprecated
        {{/isDeprecated}}
        public okhttp3.Call buildCall(final ApiCallback _callback) throws ApiException {
            return {{operationId}}Call({{#allParams}}{{paramName}}, {{/allParams}}_callback);
        }

        /**
         * Execute {{operationId}} request{{#returnType}}
         * @return {{returnType}}{{/returnType}}
         * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
         {{#responses.0}}
         * @http.response.details
         <table summary="Response Details" border="1">
            <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
            {{#responses}}
            <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
            {{/responses}}
         </table>
         {{/responses.0}}
         {{#isDeprecated}}
         * @deprecated
         {{/isDeprecated}}
         */
        {{#isDeprecated}}
        @Deprecated
        {{/isDeprecated}}
        public {{#returnType}}{{{returnType}}}{{/returnType}}{{^returnType}}void{{/returnType}} execute() throws ApiException {
            {{#returnType}}ApiResponse<{{{returnType}}}> localVarResp = {{/returnType}}{{operationId}}WithHttpInfo({{#allParams}}{{paramName}}{{#hasMore}}, {{/hasMore}}{{/allParams}});{{#returnType}}
            return localVarResp.getData();{{/returnType}}
        }

        /**
         * Execute {{operationId}} request with HTTP info returned
         * @return ApiResponse&lt;{{#returnType}}{{returnType}}{{/returnType}}{{^returnType}}Void{{/returnType}}&gt;
         * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
         {{#responses.0}}
         * @http.response.details
         <table summary="Response Details" border="1">
            <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
            {{#responses}}
            <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
            {{/responses}}
         </table>
         {{/responses.0}}
         {{#isDeprecated}}
         * @deprecated
         {{/isDeprecated}}
         */
        {{#isDeprecated}}
        @Deprecated
        {{/isDeprecated}}
        public ApiResponse<{{#returnType}}{{{returnType}}}{{/returnType}}{{^returnType}}Void{{/returnType}}> executeWithHttpInfo() throws ApiException {
            return {{operationId}}WithHttpInfo({{#allParams}}{{paramName}}{{#hasMore}}, {{/hasMore}}{{/allParams}});
        }

        /**
         * Execute {{operationId}} request (asynchronously)
         * @param _callback The callback to be executed when the API call finishes
         * @return The request call
         * @throws ApiException If fail to process the API call, e.g. serializing the request body object
         {{#responses.0}}
         * @http.response.details
         <table summary="Response Details" border="1">
            <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
            {{#responses}}
            <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
            {{/responses}}
         </table>
         {{/responses.0}}
         {{#isDeprecated}}
         * @deprecated
         {{/isDeprecated}}
         */
        {{#isDeprecated}}
        @Deprecated
        {{/isDeprecated}}
        public okhttp3.Call executeAsync(final ApiCallback<{{#returnType}}{{{returnType}}}{{/returnType}}{{^returnType}}Void{{/returnType}}> _callback) throws ApiException {
            return {{operationId}}Async({{#allParams}}{{paramName}}, {{/allParams}}_callback);
        }
    }

    /**
     * {{summary}}
     * {{notes}}{{#requiredParams}}
     * @param {{paramName}} {{description}} (required){{/requiredParams}}
     * @return API{{operationId}}Request
     {{#responses.0}}
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        {{#responses}}
        <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
        {{/responses}}
     </table>
     {{/responses.0}}
     {{#isDeprecated}}
     * @deprecated
     {{/isDeprecated}}
        {{#externalDocs}}
     * {{description}}
     * @see <a href="{{url}}">{{summary}} Documentation</a>
        {{/externalDocs}}
     */
    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    public API{{operationId}}Request {{operationId}}({{#requiredParams}}{{{dataType}}} {{paramName}}{{#hasMore}}, {{/hasMore}}{{/requiredParams}}) {
        return new API{{operationId}}Request({{#requiredParams}}{{paramName}}{{#hasMore}}, {{/hasMore}}{{/requiredParams}});
    }
    {{/vendorExtensions.x-group-parameters}}
    {{/operation}}
}
{{/operations}}
