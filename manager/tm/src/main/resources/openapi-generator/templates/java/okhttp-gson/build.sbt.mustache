lazy val root = (project in file(".")).
  settings(
    organization := "{{groupId}}",
    name := "{{artifactId}}",
    version := "{{artifactVersion}}",
    scalaVersion := "2.11.4",
    scalacOptions ++= Seq("-feature"),
    javacOptions in compile ++= Seq("-Xlint:deprecation"),
    publishArtifact in (Compile, packageDoc) := false,
    resolvers += Resolver.mavenLocal,
    libraryDependencies ++= Seq(
      "io.swagger" % "swagger-annotations" % "1.5.22",
      "com.squareup.okhttp3" % "okhttp" % "3.14.2",
      "com.squareup.okhttp3" % "logging-interceptor" % "3.14.2",
      "com.google.code.gson" % "gson" % "2.8.5",
      "org.apache.commons" % "commons-lang3" % "3.9",
      {{#hasOAuthMethods}}
      "org.apache.oltu.oauth2" % "org.apache.oltu.oauth2.client" % "1.0.1",
      {{/hasOAuthMethods}}
      {{#joda}}
      "joda-time" % "joda-time" % "2.9.9" % "compile",
      {{/joda}}
      {{#threetenbp}}
      "org.threeten" % "threetenbp" % "1.3.5" % "compile",
      {{/threetenbp}}
      "io.gsonfire" % "gson-fire" % "1.8.3" % "compile",
      "junit" % "junit" % "4.12" % "test",
      "com.novocode" % "junit-interface" % "0.10" % "test"
    )
  )
