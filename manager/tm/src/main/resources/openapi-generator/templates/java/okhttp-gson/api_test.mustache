{{>licenseInfo}}

package {{package}};

import {{invokerPackage}}.ApiException;
import {{invokerPackage}}.ApiClient;
{{#imports}}import {{import}};
{{/imports}}
import org.junit.Test;
import org.junit.Ignore;

{{^fullJavaUtil}}
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
{{/fullJavaUtil}}

/**
 * API tests for {{classname}}
 */
@Ignore
public class {{classname}}Test {

    private final ApiClient apiClient = new ApiClient(
        "your_client_id",
        "your_client_secret",
        "api_url", // http://127.0.0.1:3080
        "auth_url" // http://127.0.0.1:3030/oauth/token
    );
    private final {{classname}} api = new {{classname}}(apiClient);

    {{#operations}}{{#operation}}
    /**
     * {{summary}}
     *
     * {{notes}}
     *
     * @throws ApiException
     *          if the Api call fails
     */
    @Test
    public void {{operationId}}Test() throws ApiException {
        {{#allParams}}
        {{{dataType}}} {{paramName}} = new {{{dataType}}}();
        {{/allParams}}
        {{#returnType}}{{{returnType}}} response = {{/returnType}}api.{{operationId}}{{^vendorExtensions.x-group-parameters}}({{#allParams}}{{paramName}}{{#hasMore}}, {{/hasMore}}{{/allParams}});{{/vendorExtensions.x-group-parameters}}{{#vendorExtensions.x-group-parameters}}({{#requiredParams}}{{paramName}}{{#hasMore}}, {{/hasMore}}{{/requiredParams}}){{#optionalParams}}
                .{{paramName}}({{paramName}}){{/optionalParams}}
                .execute();{{/vendorExtensions.x-group-parameters}}

        // TODO: test validations
    }
    {{/operation}}{{/operations}}
}
