package com.tapdata.tm.openapi.generator.controller;

import com.tapdata.tm.base.controller.BaseController;
import com.tapdata.tm.base.dto.ResponseMessage;
import com.tapdata.tm.openapi.generator.dto.CodeGenerationRequest;
import com.tapdata.tm.openapi.generator.exception.CodeGenerationException;
import com.tapdata.tm.openapi.generator.service.OpenApiGeneratorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * OpenAPI代码生成器Controller
 * 提供基于OpenAPI规范生成SDK代码的HTTP API
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Tag(name = "OpenAPI Generator", description = "OpenAPI代码生成相关接口")
@RestController
@RequestMapping("/api/openapi/generator")
@Slf4j
public class OpenApiGeneratorController extends BaseController {

    @Autowired
    private OpenApiGeneratorService openApiGeneratorService;

    /**
     * 生成SDK代码并下载ZIP压缩包
     * 
     * @param request 代码生成请求参数
     * @return ZIP文件下载响应
     */
    @Operation(
        summary = "生成SDK代码", 
        description = "根据OpenAPI规范生成指定语言的SDK代码，并以ZIP压缩包形式下载"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "代码生成成功，返回ZIP文件",
            content = @Content(
                mediaType = "application/octet-stream",
                schema = @Schema(type = "string", format = "binary")
            )
        ),
        @ApiResponse(
            responseCode = "400", 
            description = "请求参数错误",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ResponseMessage.class)
            )
        ),
        @ApiResponse(
            responseCode = "500", 
            description = "服务器内部错误",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ResponseMessage.class)
            )
        )
    })
    @PostMapping("/generate")
    public ResponseEntity<InputStreamResource> generateCode(
            @Valid @RequestBody CodeGenerationRequest request) {
        
        try {
            log.info("收到代码生成请求: oas={}, lan={}, packageName={}, artifactId={}, groupId={}", 
                    request.getOas(), request.getLan(), request.getPackageName(), 
                    request.getArtifactId(), request.getGroupId());
            
            return openApiGeneratorService.generateCode(request);
            
        } catch (CodeGenerationException e) {
            log.error("代码生成失败", e);
            throw e; // 重新抛出自定义异常
        } catch (Exception e) {
            log.error("代码生成失败", e);
            throw new CodeGenerationException("代码生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用GET方式生成代码（简化版本）
     * 
     * @param oas OpenAPI规范文件的URL
     * @param lan 生成的编程语言，默认为java
     * @return ZIP文件下载响应
     */
    @Operation(
        summary = "生成SDK代码（GET方式）", 
        description = "使用GET请求生成SDK代码，参数通过URL传递"
    )
    @GetMapping("/generate")
    public ResponseEntity<InputStreamResource> generateCodeByGet(
            @Parameter(description = "OpenAPI规范文件的URL", required = true, example = "https://example.com/api/openapi.json")
            @RequestParam String oas,
            
            @Parameter(description = "生成的编程语言", example = "java")
            @RequestParam(defaultValue = "java") String lan) {
        
        try {
            log.info("收到GET代码生成请求: oas={}, lan={}", oas, lan);
            
            CodeGenerationRequest request = new CodeGenerationRequest();
            request.setOas(oas);
            request.setLan(lan);
            // 使用默认值
            request.setPackageName("io.tapdata.sdk");
            request.setArtifactId("tapdata-sdk");
            request.setGroupId("io.tapdata");
            
            return openApiGeneratorService.generateCode(request);
            
        } catch (CodeGenerationException e) {
            log.error("代码生成失败", e);
            throw e; // 重新抛出自定义异常
        } catch (Exception e) {
            log.error("代码生成失败", e);
            throw new CodeGenerationException("代码生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取支持的编程语言列表
     * 
     * @return 支持的语言列表
     */
    @Operation(summary = "获取支持的编程语言", description = "返回OpenAPI Generator支持的编程语言列表")
    @GetMapping("/languages")
    public ResponseMessage<String[]> getSupportedLanguages() {
        // 常用的OpenAPI Generator支持的语言
        String[] languages = {
            "java", "javascript", "typescript-node", "python", "csharp", 
            "go", "php", "ruby", "swift", "kotlin", "scala", "dart"
        };
        return success(languages);
    }

    /**
     * 健康检查接口
     * 
     * @return 服务状态
     */
    @Operation(summary = "健康检查", description = "检查OpenAPI Generator服务是否正常运行")
    @GetMapping("/health")
    public ResponseMessage<String> health() {
        return success("OpenAPI Generator服务运行正常");
    }
}
