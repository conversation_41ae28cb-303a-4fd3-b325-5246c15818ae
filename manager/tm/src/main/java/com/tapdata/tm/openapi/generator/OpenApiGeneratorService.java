package com.tapdata.tm.openapi.generator;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * OpenAPI Generator Service
 * 提供代码生成和相关功能
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class OpenApiGeneratorService {

    @Value("${openapi.generator.template.path:/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/src/main/resources/openapi-generator}")
    private String templatePath;

    @Value("${openapi.generator.temp.dir:#{systemProperties['java.io.tmpdir']}}")
    private String tempDir;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 生成代码并返回zip压缩包字节数组
     */
    public byte[] generateCodeAsZip(String openApiSpec, String generatorName, String packageName, String clientName) throws Exception {
        String workDir = null;
        try {
            // 创建临时工作目录
            workDir = createTempWorkDirectory();
            log.info("创建临时工作目录: {}", workDir);

            // 保存OpenAPI规范到文件
            String specFilePath = saveOpenApiSpecToFile(openApiSpec, workDir);
            log.info("保存OpenAPI规范到文件: {}", specFilePath);

            // 生成代码
            String outputDir = generateCode(specFilePath, generatorName, packageName, clientName, workDir);
            log.info("代码生成完成，输出目录: {}", outputDir);

            // 创建zip压缩包
            byte[] zipData = createZipFromDirectory(outputDir);
            log.info("创建zip压缩包完成，大小: {} bytes", zipData.length);

            return zipData;

        } finally {
            // 清理临时目录
            if (workDir != null) {
                cleanupTempDirectory(workDir);
            }
        }
    }

    /**
     * 获取支持的生成器列表
     */
    public String[] getSupportedGenerators() {
        return new String[]{
                "java", "javascript", "typescript-node", "python", "csharp", "go",
                "php", "ruby", "swift", "kotlin", "scala", "rust", "dart",
                "android", "objc", "cpp-qt-client", "cpp-restsdk"
        };
    }

    /**
     * 验证OpenAPI规范
     */
    public boolean validateOpenApiSpec(String openApiSpec) {
        try {
            // 尝试解析JSON/YAML格式
            objectMapper.readTree(openApiSpec);
            
            // 基本验证：检查是否包含必要的字段
            return openApiSpec.contains("openapi") || openApiSpec.contains("swagger");
        } catch (Exception e) {
            log.warn("OpenAPI规范验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 创建临时工作目录
     */
    private String createTempWorkDirectory() throws IOException {
        String uniqueId = UUID.randomUUID().toString().substring(0, 8);
        Path tempPath = Paths.get(tempDir, "openapi-generator-" + uniqueId);
        Files.createDirectories(tempPath);
        return tempPath.toString();
    }

    /**
     * 保存OpenAPI规范到文件
     */
    private String saveOpenApiSpecToFile(String openApiSpec, String workDir) throws IOException {
        String fileName = "openapi-spec.json";
        Path specFile = Paths.get(workDir, fileName);
        Files.write(specFile, openApiSpec.getBytes());
        return specFile.toString();
    }

    /**
     * 生成代码
     */
    private String generateCode(String specFilePath, String generatorName, String packageName, String clientName, String workDir) throws Exception {
        String outputDir = Paths.get(workDir, "generated").toString();
        Files.createDirectories(Paths.get(outputDir));

        // 检查是否有OpenAPI Generator CLI可用
        if (isOpenApiGeneratorCliAvailable()) {
            // 使用CLI方式生成
            generateWithCli(specFilePath, generatorName, packageName, clientName, outputDir);
        } else {
            // 使用简化的模板生成方式
            generateWithTemplate(specFilePath, generatorName, packageName, clientName, outputDir);
        }

        return outputDir;
    }

    /**
     * 检查OpenAPI Generator CLI是否可用
     */
    private boolean isOpenApiGeneratorCliAvailable() {
        try {
            ProcessBuilder pb = new ProcessBuilder("openapi-generator-cli", "version");
            Process process = pb.start();
            int exitCode = process.waitFor();
            return exitCode == 0;
        } catch (Exception e) {
            log.debug("OpenAPI Generator CLI不可用: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 使用CLI生成代码
     */
    private void generateWithCli(String specFilePath, String generatorName, String packageName, String clientName, String outputDir) throws Exception {
        List<String> command = buildGeneratorCommand(specFilePath, generatorName, packageName, clientName, outputDir);
        
        log.info("执行OpenAPI Generator命令: {}", String.join(" ", command));
        
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true);
        
        Process process = processBuilder.start();
        
        // 读取输出
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
                log.debug("OpenAPI Generator输出: {}", line);
            }
        }
        
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new RuntimeException("OpenAPI Generator执行失败，退出码: " + exitCode + "，输出: " + output.toString());
        }
    }

    /**
     * 使用模板生成代码（简化版本）
     */
    private void generateWithTemplate(String specFilePath, String generatorName, String packageName, String clientName, String outputDir) throws Exception {
        log.info("使用模板生成代码，生成器: {}", generatorName);
        
        // 读取OpenAPI规范
        String specContent = Files.readString(Paths.get(specFilePath));
        
        // 根据生成器类型创建基本的代码结构
        switch (generatorName.toLowerCase()) {
            case "java":
                generateJavaClient(specContent, packageName, clientName, outputDir);
                break;
            case "javascript":
            case "typescript-node":
                generateJavaScriptClient(specContent, packageName, clientName, outputDir);
                break;
            default:
                generateGenericClient(specContent, packageName, clientName, outputDir, generatorName);
                break;
        }
    }

    /**
     * 构建OpenAPI Generator命令
     */
    private List<String> buildGeneratorCommand(String specFilePath, String generatorName, String packageName, String clientName, String outputDir) {
        List<String> command = new ArrayList<>();
        
        command.add("openapi-generator-cli");
        command.add("generate");
        command.add("-i");
        command.add(specFilePath);
        command.add("-g");
        command.add(generatorName);
        command.add("-o");
        command.add(outputDir);
        
        // 添加模板目录
        Path templateDir = Paths.get(templatePath, "templates", generatorName);
        if (Files.exists(templateDir)) {
            command.add("-t");
            command.add(templateDir.toString());
        }
        
        // 添加配置参数
        command.add("--additional-properties");
        command.add(String.format("packageName=%s,clientPackage=%s,artifactId=%s", 
                packageName, packageName, clientName.toLowerCase()));
        
        return command;
    }

    /**
     * 从目录创建zip压缩包
     */
    private byte[] createZipFromDirectory(String sourceDir) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        
        try (ZipOutputStream zos = new ZipOutputStream(baos)) {
            Path sourcePath = Paths.get(sourceDir);
            
            Files.walk(sourcePath)
                    .filter(path -> !Files.isDirectory(path))
                    .forEach(path -> {
                        try {
                            String entryName = sourcePath.relativize(path).toString();
                            ZipEntry zipEntry = new ZipEntry(entryName);
                            zos.putNextEntry(zipEntry);
                            Files.copy(path, zos);
                            zos.closeEntry();
                        } catch (IOException e) {
                            log.error("添加文件到zip时发生错误: {}", path, e);
                        }
                    });
        }
        
        return baos.toByteArray();
    }

    /**
     * 生成Java客户端代码
     */
    private void generateJavaClient(String specContent, String packageName, String clientName, String outputDir) throws IOException {
        // 创建Java项目结构
        String srcDir = outputDir + "/src/main/java/" + packageName.replace(".", "/");
        Files.createDirectories(Paths.get(srcDir));

        // 生成基本的API客户端类
        String clientCode = generateJavaClientClass(packageName, clientName, specContent);
        Files.write(Paths.get(srcDir, clientName + "Client.java"), clientCode.getBytes());

        // 生成pom.xml
        String pomContent = generatePomXml(packageName, clientName);
        Files.write(Paths.get(outputDir, "pom.xml"), pomContent.getBytes());

        // 生成README.md
        String readmeContent = generateReadme(clientName, "java");
        Files.write(Paths.get(outputDir, "README.md"), readmeContent.getBytes());
    }

    /**
     * 生成JavaScript客户端代码
     */
    private void generateJavaScriptClient(String specContent, String packageName, String clientName, String outputDir) throws IOException {
        // 创建JavaScript项目结构
        Files.createDirectories(Paths.get(outputDir, "src"));

        // 生成基本的API客户端类
        String clientCode = generateJavaScriptClientClass(packageName, clientName, specContent);
        Files.write(Paths.get(outputDir, "src", clientName.toLowerCase() + "-client.js"), clientCode.getBytes());

        // 生成package.json
        String packageJsonContent = generatePackageJson(packageName, clientName);
        Files.write(Paths.get(outputDir, "package.json"), packageJsonContent.getBytes());

        // 生成README.md
        String readmeContent = generateReadme(clientName, "javascript");
        Files.write(Paths.get(outputDir, "README.md"), readmeContent.getBytes());
    }

    /**
     * 生成通用客户端代码
     */
    private void generateGenericClient(String specContent, String packageName, String clientName, String outputDir, String generatorName) throws IOException {
        // 创建基本目录结构
        Files.createDirectories(Paths.get(outputDir, "src"));

        // 生成基本的客户端文件
        String clientCode = generateGenericClientClass(packageName, clientName, specContent, generatorName);
        String extension = getFileExtension(generatorName);
        Files.write(Paths.get(outputDir, "src", clientName.toLowerCase() + "-client" + extension), clientCode.getBytes());

        // 生成README.md
        String readmeContent = generateReadme(clientName, generatorName);
        Files.write(Paths.get(outputDir, "README.md"), readmeContent.getBytes());
    }

    /**
     * 生成Java客户端类代码
     */
    private String generateJavaClientClass(String packageName, String clientName, String specContent) {
        return String.format("""
            package %s;

            import java.io.IOException;
            import java.net.http.HttpClient;
            import java.net.http.HttpRequest;
            import java.net.http.HttpResponse;
            import java.net.URI;
            import java.time.Duration;

            /**
             * %s API Client
             * Generated by Tapdata OpenAPI Generator
             */
            public class %sClient {

                private final HttpClient httpClient;
                private final String baseUrl;

                public %sClient(String baseUrl) {
                    this.baseUrl = baseUrl;
                    this.httpClient = HttpClient.newBuilder()
                        .connectTimeout(Duration.ofSeconds(30))
                        .build();
                }

                /**
                 * 发送GET请求
                 */
                public String get(String path) throws IOException, InterruptedException {
                    HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(baseUrl + path))
                        .GET()
                        .build();

                    HttpResponse<String> response = httpClient.send(request,
                        HttpResponse.BodyHandlers.ofString());

                    return response.body();
                }

                /**
                 * 发送POST请求
                 */
                public String post(String path, String body) throws IOException, InterruptedException {
                    HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(baseUrl + path))
                        .header("Content-Type", "application/json")
                        .POST(HttpRequest.BodyPublishers.ofString(body))
                        .build();

                    HttpResponse<String> response = httpClient.send(request,
                        HttpResponse.BodyHandlers.ofString());

                    return response.body();
                }
            }
            """, packageName, clientName, clientName, clientName);
    }

    /**
     * 生成JavaScript客户端类代码
     */
    private String generateJavaScriptClientClass(String packageName, String clientName, String specContent) {
        return String.format("""
            /**
             * %s API Client
             * Generated by Tapdata OpenAPI Generator
             */
            class %sClient {

                constructor(baseUrl) {
                    this.baseUrl = baseUrl;
                }

                /**
                 * 发送GET请求
                 */
                async get(path) {
                    const response = await fetch(this.baseUrl + path, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return await response.json();
                }

                /**
                 * 发送POST请求
                 */
                async post(path, data) {
                    const response = await fetch(this.baseUrl + path, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return await response.json();
                }
            }

            module.exports = %sClient;
            """, clientName, clientName, clientName);
    }

    /**
     * 生成通用客户端类代码
     */
    private String generateGenericClientClass(String packageName, String clientName, String specContent, String generatorName) {
        return String.format("""
            # %s API Client
            # Generated by Tapdata OpenAPI Generator for %s

            This is a generated API client for %s.

            ## Usage

            Please refer to the documentation for specific usage instructions.

            ## Configuration

            Base URL: Configure your API base URL
            Authentication: Configure your authentication method

            ## Generated from OpenAPI Specification

            This client was generated from the provided OpenAPI specification.
            """, clientName, generatorName, clientName);
    }

    /**
     * 生成pom.xml文件
     */
    private String generatePomXml(String packageName, String clientName) {
        return String.format("""
            <?xml version="1.0" encoding="UTF-8"?>
            <project xmlns="http://maven.apache.org/POM/4.0.0"
                     xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                     http://maven.apache.org/xsd/maven-4.0.0.xsd">
                <modelVersion>4.0.0</modelVersion>

                <groupId>%s</groupId>
                <artifactId>%s</artifactId>
                <version>1.0.0</version>
                <packaging>jar</packaging>

                <name>%s</name>
                <description>Generated API client by Tapdata OpenAPI Generator</description>

                <properties>
                    <maven.compiler.source>11</maven.compiler.source>
                    <maven.compiler.target>11</maven.compiler.target>
                    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
                </properties>

                <dependencies>
                    <!-- Add your dependencies here -->
                </dependencies>

                <build>
                    <plugins>
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-compiler-plugin</artifactId>
                            <version>3.8.1</version>
                            <configuration>
                                <source>11</source>
                                <target>11</target>
                            </configuration>
                        </plugin>
                    </plugins>
                </build>
            </project>
            """, packageName, clientName.toLowerCase(), clientName);
    }

    /**
     * 生成package.json文件
     */
    private String generatePackageJson(String packageName, String clientName) {
        return String.format("""
            {
              "name": "%s",
              "version": "1.0.0",
              "description": "Generated API client by Tapdata OpenAPI Generator",
              "main": "src/%s-client.js",
              "scripts": {
                "test": "echo \\"Error: no test specified\\" && exit 1"
              },
              "keywords": ["api", "client", "tapdata"],
              "author": "Tapdata OpenAPI Generator",
              "license": "MIT",
              "dependencies": {
                "node-fetch": "^3.0.0"
              }
            }
            """, clientName.toLowerCase(), clientName.toLowerCase());
    }

    /**
     * 生成README.md文件
     */
    private String generateReadme(String clientName, String generatorType) {
        return String.format("""
            # %s API Client

            This is a generated API client for %s, created using Tapdata OpenAPI Generator.

            ## Installation

            %s

            ## Usage

            %s

            ## Generated by

            Tapdata OpenAPI Generator - %s client

            ## Support

            For support and documentation, please refer to the Tapdata documentation.
            """,
            clientName,
            clientName,
            getInstallationInstructions(generatorType),
            getUsageInstructions(clientName, generatorType),
            generatorType);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String generatorName) {
        return switch (generatorName.toLowerCase()) {
            case "java" -> ".java";
            case "javascript", "typescript-node" -> ".js";
            case "python" -> ".py";
            case "csharp" -> ".cs";
            case "go" -> ".go";
            case "php" -> ".php";
            case "ruby" -> ".rb";
            case "swift" -> ".swift";
            case "kotlin" -> ".kt";
            case "scala" -> ".scala";
            case "rust" -> ".rs";
            case "dart" -> ".dart";
            default -> ".txt";
        };
    }

    /**
     * 获取安装说明
     */
    private String getInstallationInstructions(String generatorType) {
        return switch (generatorType.toLowerCase()) {
            case "java" -> "Add this dependency to your Maven pom.xml or Gradle build file.";
            case "javascript", "typescript-node" -> "```bash\\nnpm install\\n```";
            case "python" -> "```bash\\npip install -r requirements.txt\\n```";
            default -> "Please refer to the specific language documentation for installation instructions.";
        };
    }

    /**
     * 获取使用说明
     */
    private String getUsageInstructions(String clientName, String generatorType) {
        return switch (generatorType.toLowerCase()) {
            case "java" -> String.format("""
                ```java
                %sClient client = new %sClient("https://api.example.com");
                String response = client.get("/endpoint");
                ```""", clientName, clientName);
            case "javascript", "typescript-node" -> String.format("""
                ```javascript
                const %sClient = require('./src/%s-client');
                const client = new %sClient('https://api.example.com');
                const response = await client.get('/endpoint');
                ```""", clientName, clientName.toLowerCase(), clientName);
            default -> "Please refer to the generated code for usage examples.";
        };
    }

    /**
     * 清理临时目录
     */
    private void cleanupTempDirectory(String workDir) {
        try {
            Path path = Paths.get(workDir);
            if (Files.exists(path)) {
                Files.walk(path)
                        .sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                        .forEach(p -> {
                            try {
                                Files.delete(p);
                            } catch (IOException e) {
                                log.warn("删除临时文件失败: {}", p, e);
                            }
                        });
            }
        } catch (Exception e) {
            log.warn("清理临时目录失败: {}", workDir, e);
        }
    }
}
