package com.tapdata.tm.openapi.generator.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * OpenAPI代码生成请求DTO
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@Schema(description = "OpenAPI代码生成请求")
public class CodeGenerationRequest {

    @NotBlank(message = "OpenAPI规范URL不能为空")
    @Schema(description = "OpenAPI规范文件的URL", example = "https://example.com/api/openapi.json", required = true)
    private String oas;

    @Schema(description = "生成的编程语言", example = "java", defaultValue = "java")
    private String lan = "java";

    @Schema(description = "生成代码的包名", example = "io.tapdata.sdk", defaultValue = "io.tapdata.sdk")
    private String packageName = "io.tapdata.sdk";

    @Schema(description = "Artifact ID", example = "tapdata-sdk", defaultValue = "tapdata-sdk")
    private String artifactId = "tapdata-sdk";

    @Schema(description = "Group ID", example = "io.tapdata", defaultValue = "io.tapdata")
    private String groupId = "io.tapdata";
}
