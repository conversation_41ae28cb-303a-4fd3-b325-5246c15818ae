package com.tapdata.tm.openapi.generator.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * OpenAPI Generator configuration properties
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@Component
@ConfigurationProperties(prefix = "openapi.generator")
public class OpenApiGeneratorProperties {

    /**
     * JAR file configuration
     */
    private Jar jar = new Jar();

    /**
     * Template configuration
     */
    private Template template = new Template();

    /**
     * Temporary directory configuration
     */
    private Temp temp = new Temp();

    @Data
    public static class Jar {
        /**
         * Path to OpenAPI Generator CLI JAR file
         */
        private String path = "classpath:openapi-generator/openapi-generator-cli.jar";
    }

    @Data
    public static class Template {
        /**
         * Path to template directory
         */
        private String path = "classpath:openapi-generator";
    }

    @Data
    public static class Temp {
        /**
         * Temporary directory for code generation
         */
        private String dir = System.getProperty("java.io.tmpdir");
    }
}
