package com.tapdata.tm.openapi.generator;

import com.tapdata.tm.base.controller.BaseController;
import com.tapdata.tm.base.dto.ResponseMessage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * OpenAPI Generator Controller
 * 用于生成代码并以zip压缩包形式下载
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/openapi-generator")
@Tag(name = "OpenApiGenerator", description = "OpenAPI代码生成器接口")
public class OpenApiGeneratorController extends BaseController {

    @Autowired
    private OpenApiGeneratorService openApiGeneratorService;

    /**
     * 生成代码并下载zip压缩包
     *
     * @param openApiSpec OpenAPI规范内容
     * @param generatorName 生成器名称 (如: java, javascript, python等)
     * @param packageName 包名
     * @param clientName 客户端名称
     * @param response HTTP响应对象
     */
    @PostMapping(value = "/generate", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @Operation(summary = "生成代码并下载zip压缩包", description = "使用OpenAPI Generator工具生成代码，并以zip压缩包形式下载")
    public void generateAndDownload(
            @Parameter(description = "OpenAPI规范内容", required = true)
            @RequestParam("openApiSpec") String openApiSpec,
            
            @Parameter(description = "生成器名称 (如: java, javascript, python等)", required = true)
            @RequestParam("generatorName") String generatorName,
            
            @Parameter(description = "包名", required = false)
            @RequestParam(value = "packageName", required = false, defaultValue = "com.tapdata.generated") String packageName,
            
            @Parameter(description = "客户端名称", required = false)
            @RequestParam(value = "clientName", required = false, defaultValue = "TapdataClient") String clientName,
            
            HttpServletResponse response) throws IOException {
        
        try {
            log.info("开始生成代码，生成器: {}, 包名: {}, 客户端名称: {}", generatorName, packageName, clientName);
            
            // 生成代码并创建zip文件
            byte[] zipData = openApiGeneratorService.generateCodeAsZip(openApiSpec, generatorName, packageName, clientName);
            
            // 设置响应头
            String filename = String.format("%s-client-%s.zip", clientName.toLowerCase(), generatorName);
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
            response.setContentLength(zipData.length);
            
            // 写入响应
            response.getOutputStream().write(zipData);
            response.getOutputStream().flush();
            
            log.info("代码生成完成，文件名: {}, 大小: {} bytes", filename, zipData.length);
            
        } catch (Exception e) {
            log.error("生成代码时发生错误", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("生成代码时发生错误: " + e.getMessage());
        }
    }

    /**
     * 获取支持的生成器列表
     *
     * @return 支持的生成器列表
     */
    @GetMapping("/generators")
    @Operation(summary = "获取支持的生成器列表", description = "返回OpenAPI Generator支持的所有生成器类型")
    public ResponseMessage<String[]> getSupportedGenerators() {
        try {
            String[] generators = openApiGeneratorService.getSupportedGenerators();
            return success(generators);
        } catch (Exception e) {
            log.error("获取支持的生成器列表时发生错误", e);
            return fail("获取支持的生成器列表失败: " + e.getMessage());
        }
    }

    /**
     * 验证OpenAPI规范
     *
     * @param openApiSpec OpenAPI规范内容
     * @return 验证结果
     */
    @PostMapping("/validate")
    @Operation(summary = "验证OpenAPI规范", description = "验证提供的OpenAPI规范是否有效")
    public ResponseMessage<Boolean> validateOpenApiSpec(
            @Parameter(description = "OpenAPI规范内容", required = true)
            @RequestParam("openApiSpec") String openApiSpec) {
        try {
            boolean isValid = openApiGeneratorService.validateOpenApiSpec(openApiSpec);
            return success(isValid);
        } catch (Exception e) {
            log.error("验证OpenAPI规范时发生错误", e);
            return fail("验证OpenAPI规范失败: " + e.getMessage());
        }
    }
}
