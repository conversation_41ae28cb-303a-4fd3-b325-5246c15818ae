### OpenAPI Generator API 测试文件
### 使用 IntelliJ IDEA 或 VS Code 的 REST Client 插件运行这些测试

### 1. 健康检查
GET http://localhost:3000/api/openapi/generator/health
Accept: application/json

### 2. 获取支持的语言列表
GET http://localhost:3000/api/openapi/generator/languages
Accept: application/json

### 3. 使用GET方式生成Java SDK (使用Swagger Petstore示例)
GET http://localhost:3000/api/openapi/generator/generate?oas=https://petstore3.swagger.io/api/v3/openapi.json&lan=java
Accept: application/octet-stream

### 4. 使用POST方式生成Java SDK
POST http://localhost:3000/api/openapi/generator/generate
Content-Type: application/json
Accept: application/octet-stream

{
  "oas": "https://petstore3.swagger.io/api/v3/openapi.json",
  "lan": "java",
  "packageName": "io.tapdata.sdk",
  "artifactId": "tapdata-sdk",
  "groupId": "io.tapdata"
}

### 5. 生成Python SDK
POST http://localhost:3000/api/openapi/generator/generate
Content-Type: application/json
Accept: application/octet-stream

{
  "oas": "https://petstore3.swagger.io/api/v3/openapi.json",
  "lan": "python",
  "packageName": "tapdata_sdk",
  "artifactId": "tapdata-sdk",
  "groupId": "io.tapdata"
}

### 6. 生成JavaScript SDK
POST http://localhost:3000/api/openapi/generator/generate
Content-Type: application/json
Accept: application/octet-stream

{
  "oas": "https://petstore3.swagger.io/api/v3/openapi.json",
  "lan": "javascript",
  "packageName": "tapdata-sdk",
  "artifactId": "tapdata-sdk",
  "groupId": "io.tapdata"
}

### 7. 测试错误情况 - 无效的OpenAPI URL
POST http://localhost:3000/api/openapi/generator/generate
Content-Type: application/json
Accept: application/json

{
  "oas": "https://invalid-url.example.com/openapi.json",
  "lan": "java"
}

### 8. 测试错误情况 - 不支持的语言
POST http://localhost:3000/api/openapi/generator/generate
Content-Type: application/json
Accept: application/json

{
  "oas": "https://petstore3.swagger.io/api/v3/openapi.json",
  "lan": "unsupported-language"
}
