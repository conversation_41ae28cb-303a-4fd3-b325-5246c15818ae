### OpenAPI Generator API Test File
### Use IntelliJ IDEA or VS Code REST Client plugin to run these tests

### 1. Health Check
GET http://localhost:3000/api/openapi/generator/health
Accept: application/json

### 2. Get Supported Languages List
GET http://localhost:3000/api/openapi/generator/languages
Accept: application/json

### 3. Generate Java SDK using GET method (using Swagger Petstore example)
GET http://localhost:3000/api/openapi/generator/generate?oas=https://petstore3.swagger.io/api/v3/openapi.json&lan=java
Accept: application/octet-stream

### 4. Generate Java SDK using POST method
POST http://localhost:3000/api/openapi/generator/generate
Content-Type: application/json
Accept: application/octet-stream

{
  "oas": "http://*************:3080/openapi.json",
  "lan": "java",
  "packageName": "io.tapdata.sdk",
  "artifactId": "tapdata-sdk",
  "groupId": "io.tapdata"
}

### 5. Generate Python SDK
POST http://localhost:3000/api/openapi/generator/generate
Content-Type: application/json
Accept: application/octet-stream

{
  "oas": "https://petstore3.swagger.io/api/v3/openapi.json",
  "lan": "python",
  "packageName": "tapdata_sdk",
  "artifactId": "tapdata-sdk",
  "groupId": "io.tapdata"
}

### 6. Generate JavaScript SDK
POST http://localhost:3000/api/openapi/generator/generate
Content-Type: application/json
Accept: application/octet-stream

{
  "oas": "https://petstore3.swagger.io/api/v3/openapi.json",
  "lan": "javascript",
  "packageName": "tapdata-sdk",
  "artifactId": "tapdata-sdk",
  "groupId": "io.tapdata"
}

### 7. Test Error Case - Invalid OpenAPI URL
POST http://localhost:3000/api/openapi/generator/generate
Content-Type: application/json
Accept: application/json

{
  "oas": "https://invalid-url.example.com/openapi.json",
  "lan": "java"
}

### 8. Test Error Case - Unsupported Language
POST http://localhost:3000/api/openapi/generator/generate
Content-Type: application/json
Accept: application/json

{
  "oas": "https://petstore3.swagger.io/api/v3/openapi.json",
  "lan": "unsupported-language"
}
