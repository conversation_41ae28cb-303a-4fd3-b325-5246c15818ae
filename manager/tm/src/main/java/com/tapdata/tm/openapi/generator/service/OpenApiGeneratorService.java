package com.tapdata.tm.openapi.generator.service;

import com.tapdata.tm.openapi.generator.dto.CodeGenerationRequest;
import com.tapdata.tm.openapi.generator.exception.CodeGenerationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * OpenAPI代码生成服务
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Service
@Slf4j
public class OpenApiGeneratorService {

    @Value("${openapi.generator.jar.path:classpath:openapi-generator/openapi-generator-cli.jar}")
    private String generatorJarPath;

    @Value("${openapi.generator.template.path:classpath:openapi-generator}")
    private String templatePath;

    @Value("${openapi.generator.temp.dir:${java.io.tmpdir}}")
    private String tempDir;

    /**
     * 生成代码并返回ZIP文件
     */
    public ResponseEntity<InputStreamResource> generateCode(CodeGenerationRequest request) throws Exception {
        log.info("开始生成代码，请求参数: {}", request);
        
        // 创建临时目录
        String sessionId = UUID.randomUUID().toString();
        Path outputDir = Paths.get(tempDir, "openapi-generator", sessionId);
        Files.createDirectories(outputDir);
        
        try {
            // 执行代码生成
            executeGenerator(request, outputDir.toString());
            
            // 创建ZIP文件
            ByteArrayOutputStream zipOutput = new ByteArrayOutputStream();
            createZipFile(outputDir, zipOutput);
            
            // 准备响应
            String fileName = String.format("%s-%s.zip", request.getArtifactId(), request.getLan());
            ByteArrayInputStream zipInput = new ByteArrayInputStream(zipOutput.toByteArray());
            InputStreamResource resource = new InputStreamResource(zipInput);
            
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
            headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(zipOutput.size()));
            
            log.info("代码生成完成，文件大小: {} bytes", zipOutput.size());
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
                    
        } finally {
            // 清理临时文件
            cleanupTempDirectory(outputDir);
        }
    }

    /**
     * 解析资源路径，支持classpath和绝对路径
     */
    private String resolveResourcePath(String path) throws IOException {
        if (StringUtils.hasText(path) && path.startsWith("classpath:")) {
            String resourcePath = path.substring("classpath:".length());
            Resource resource = new ClassPathResource(resourcePath);
            if (resource.exists()) {
                return resource.getFile().getAbsolutePath();
            } else {
                throw new IOException("Classpath resource not found: " + resourcePath);
            }
        }
        return path;
    }

    /**
     * 执行OpenAPI Generator
     */
    private void executeGenerator(CodeGenerationRequest request, String outputDir) throws Exception {
        // 解析JAR包路径
        String resolvedJarPath = resolveResourcePath(generatorJarPath);

        List<String> command = new ArrayList<>();
        command.add("java");
        command.add("-jar");
        command.add(resolvedJarPath);
        command.add("generate");
        command.add("-i");
        command.add(request.getOas());
        command.add("-g");
        command.add(request.getLan());
        command.add("-o");
        command.add(outputDir);
        command.add("--package-name");
        command.add(request.getPackageName());
        command.add("--artifact-id");
        command.add(request.getArtifactId());
        command.add("--group-id");
        command.add(request.getGroupId());
        
        // 如果有模板目录，添加模板参数
        if (StringUtils.hasText(templatePath)) {
            String resolvedTemplatePath = resolveResourcePath(templatePath);
            command.add("-t");
            command.add(resolvedTemplatePath);
        }
        
        log.info("执行命令: {}", String.join(" ", command));
        
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true);
        
        Process process = processBuilder.start();
        
        // 读取输出
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
                log.debug("Generator output: {}", line);
            }
        }
        
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            log.error("代码生成失败，退出码: {}, 输出: {}", exitCode, output.toString());
            throw new CodeGenerationException("代码生成失败: " + output.toString());
        }
        
        log.info("代码生成成功");
    }

    /**
     * 创建ZIP文件
     */
    private void createZipFile(Path sourceDir, ByteArrayOutputStream zipOutput) throws IOException {
        try (ZipOutputStream zipOut = new ZipOutputStream(zipOutput)) {
            Files.walk(sourceDir)
                    .filter(path -> !Files.isDirectory(path))
                    .forEach(path -> {
                        try {
                            String relativePath = sourceDir.relativize(path).toString();
                            ZipEntry zipEntry = new ZipEntry(relativePath);
                            zipOut.putNextEntry(zipEntry);
                            Files.copy(path, zipOut);
                            zipOut.closeEntry();
                        } catch (IOException e) {
                            log.error("添加文件到ZIP失败: {}", path, e);
                            throw new CodeGenerationException("创建ZIP文件失败", e);
                        }
                    });
        }
    }

    /**
     * 清理临时目录
     */
    private void cleanupTempDirectory(Path tempDir) {
        try {
            if (Files.exists(tempDir)) {
                Files.walk(tempDir)
                        .sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                        .forEach(path -> {
                            try {
                                Files.deleteIfExists(path);
                            } catch (IOException e) {
                                log.warn("删除临时文件失败: {}", path, e);
                            }
                        });
            }
        } catch (IOException e) {
            log.warn("清理临时目录失败: {}", tempDir, e);
        }
    }
}
