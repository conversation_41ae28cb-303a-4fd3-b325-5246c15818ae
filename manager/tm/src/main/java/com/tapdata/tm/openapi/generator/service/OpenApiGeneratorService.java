package com.tapdata.tm.openapi.generator.service;

import com.tapdata.tm.openapi.generator.dto.CodeGenerationRequest;
import com.tapdata.tm.openapi.generator.exception.CodeGenerationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * OpenAPI code generation service
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Service
@Slf4j
public class OpenApiGeneratorService {

    @Value("${openapi.generator.jar.path:classpath:openapi-generator/openapi-generator-cli.jar}")
    private String generatorJarPath;

    @Value("${openapi.generator.template.path:classpath:openapi-generator}")
    private String templatePath;

    @Value("${openapi.generator.temp.dir:${java.io.tmpdir}}")
    private String tempDir;

    /**
     * Generate code and return ZIP file
     */
    public ResponseEntity<InputStreamResource> generateCode(CodeGenerationRequest request) throws Exception {
        log.info("Starting code generation with request parameters: {}", request);

        // Create temporary directory
        String sessionId = UUID.randomUUID().toString();
        Path outputDir = Paths.get(tempDir, "openapi-generator", sessionId);
        Files.createDirectories(outputDir);

        try {
            // Execute code generation
            log.info("Generator parameters: {}, output dir: {}", request, outputDir);
            executeGenerator(request, outputDir.toString());

            // Create ZIP file
            ByteArrayOutputStream zipOutput = new ByteArrayOutputStream();
            createZipFile(outputDir, zipOutput);

            // Prepare response
            String fileName = String.format("%s-%s.zip", request.getArtifactId(), request.getLan());
            ByteArrayInputStream zipInput = new ByteArrayInputStream(zipOutput.toByteArray());
            InputStreamResource resource = new InputStreamResource(zipInput);

            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
            headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(zipOutput.size()));

            log.info("Code generation completed, file size: {} bytes", zipOutput.size());

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);

        } finally {
            // Clean up temporary files
            cleanupTempDirectory(outputDir);
        }
    }

    /**
     * Resolve resource path, supports classpath and absolute paths
     */
    private String resolveResourcePath(String path) throws IOException {
        if (StringUtils.hasText(path) && path.startsWith("classpath:")) {
            String resourcePath = path.substring("classpath:".length());
            Resource resource = new ClassPathResource(resourcePath);
            if (resource.exists()) {
                return resource.getFile().getAbsolutePath();
            } else {
                throw new IOException("Classpath resource not found: " + resourcePath);
            }
        }
        return path;
    }

    /**
     * Execute OpenAPI Generator
     */
    private void executeGenerator(CodeGenerationRequest request, String outputDir) throws Exception {
        // Resolve JAR path
        String resolvedJarPath = resolveResourcePath(generatorJarPath);

        List<String> command = new ArrayList<>();
        command.add("java");
        command.add("-jar");
        command.add(resolvedJarPath);
        command.add("generate");
        command.add("-i");
        command.add(request.getOas());
        command.add("-g");
        command.add(request.getLan());
        command.add("-o");
        command.add(outputDir);
        command.add("--invoker-package");
        command.add(request.getPackageName());
        command.add("--api-package");
        command.add(request.getPackageName() + ".api");
        command.add("--model-package");
        command.add(request.getPackageName() + ".model");
        command.add("--artifact-id");
        command.add(request.getArtifactId());
        command.add("--group-id");
        command.add(request.getGroupId());
        command.add("--skip-validate-spec");
        
        // Add template parameters if template directory exists
        if (StringUtils.hasText(templatePath)) {
            String resolvedTemplatePath = resolveResourcePath(templatePath);
            command.add("-t");
            command.add(resolvedTemplatePath);
        }

        log.info("Executing command: {}", String.join(" ", command));

        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true);

        Process process = processBuilder.start();

        // Read output
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
                log.debug("Generator output: {}", line);
            }
        }

        int exitCode = process.waitFor();
        if (exitCode != 0) {
            log.error("Code generation failed, exit code: {}, output: {}", exitCode, output);
            throw new CodeGenerationException("Code generation failed: " + output);
        }

        log.info("Code generation successful");
    }

    /**
     * Create ZIP file
     */
    private void createZipFile(Path sourceDir, ByteArrayOutputStream zipOutput) throws IOException {
        try (ZipOutputStream zipOut = new ZipOutputStream(zipOutput)) {
            Files.walk(sourceDir)
                    .filter(path -> !Files.isDirectory(path))
                    .forEach(path -> {
                        try {
                            String relativePath = sourceDir.relativize(path).toString();
                            ZipEntry zipEntry = new ZipEntry(relativePath);
                            zipOut.putNextEntry(zipEntry);
                            Files.copy(path, zipOut);
                            zipOut.closeEntry();
                        } catch (IOException e) {
                            log.error("Failed to add file to ZIP: {}", path, e);
                            throw new CodeGenerationException("Failed to create ZIP file", e);
                        }
                    });
        }
    }

    /**
     * Clean up temporary directory
     */
    private void cleanupTempDirectory(Path tempDir) {
        try {
            if (Files.exists(tempDir)) {
                Files.walk(tempDir)
                        .sorted((a, b) -> b.compareTo(a)) // Delete files first, then directories
                        .forEach(path -> {
                            try {
                                Files.deleteIfExists(path);
                            } catch (IOException e) {
                                log.warn("Failed to delete temporary file: {}", path, e);
                            }
                        });
            }
        } catch (IOException e) {
            log.warn("Failed to clean up temporary directory: {}", tempDir, e);
        }
    }
}
