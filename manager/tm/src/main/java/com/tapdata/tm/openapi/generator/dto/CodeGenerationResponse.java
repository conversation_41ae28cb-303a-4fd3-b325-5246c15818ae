package com.tapdata.tm.openapi.generator.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * OpenAPI代码生成响应DTO
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@Schema(description = "OpenAPI代码生成响应")
public class CodeGenerationResponse {

    @Schema(description = "生成状态", example = "success")
    private String status;

    @Schema(description = "响应消息", example = "代码生成成功")
    private String message;

    @Schema(description = "生成的文件名", example = "tapdata-sdk-java.zip")
    private String fileName;

    @Schema(description = "文件大小（字节）", example = "1024000")
    private Long fileSize;

    public static CodeGenerationResponse success(String fileName, Long fileSize) {
        CodeGenerationResponse response = new CodeGenerationResponse();
        response.setStatus("success");
        response.setMessage("代码生成成功");
        response.setFileName(fileName);
        response.setFileSize(fileSize);
        return response;
    }

    public static CodeGenerationResponse error(String message) {
        CodeGenerationResponse response = new CodeGenerationResponse();
        response.setStatus("error");
        response.setMessage(message);
        return response;
    }
}
