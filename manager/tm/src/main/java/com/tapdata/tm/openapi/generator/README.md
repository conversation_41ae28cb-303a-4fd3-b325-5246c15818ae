# OpenAPI Generator API

这是一个基于OpenAPI Generator的代码生成服务，可以根据OpenAPI规范自动生成各种编程语言的SDK代码。

## 功能特性

- 支持多种编程语言的SDK生成
- 自动下载生成的代码为ZIP压缩包
- 支持自定义包名、artifact-id、group-id等参数
- 提供RESTful API接口
- 支持GET和POST两种请求方式

## API接口

### 1. 生成代码 (POST)

**接口地址**: `POST /api/openapi/generator/generate`

**请求体**:
```json
{
  "oas": "https://example.com/api/openapi.json",
  "lan": "java",
  "packageName": "io.tapdata.sdk",
  "artifactId": "tapdata-sdk",
  "groupId": "io.tapdata"
}
```

**参数说明**:
- `oas`: OpenAPI规范文件的URL (必填)
- `lan`: 生成的编程语言，默认为"java"
- `packageName`: 生成代码的包名，默认为"io.tapdata.sdk"
- `artifactId`: Artifact ID，默认为"tapdata-sdk"
- `groupId`: Group ID，默认为"io.tapdata"

**响应**: 返回ZIP文件下载

### 2. 生成代码 (GET)

**接口地址**: `GET /api/openapi/generator/generate`

**请求参数**:
- `oas`: OpenAPI规范文件的URL (必填)
- `lan`: 生成的编程语言，默认为"java"

**示例**:
```
GET /api/openapi/generator/generate?oas=https://example.com/api/openapi.json&lan=java
```

### 3. 获取支持的语言

**接口地址**: `GET /api/openapi/generator/languages`

**响应**:
```json
{
  "code": "ok",
  "data": ["java", "javascript", "typescript-node", "python", "csharp", "go", "php", "ruby", "swift", "kotlin", "scala", "dart"]
}
```

### 4. 健康检查

**接口地址**: `GET /api/openapi/generator/health`

**响应**:
```json
{
  "code": "ok",
  "data": "OpenAPI Generator服务运行正常"
}
```

## 配置说明

在 `application.properties` 或 `application.yml` 中添加以下配置：

```properties
# OpenAPI Generator JAR包路径
openapi.generator.jar.path=/path/to/openapi-generator-cli.jar

# 模板目录路径
openapi.generator.template.path=/path/to/templates

# 临时目录
openapi.generator.temp.dir=${java.io.tmpdir}
```

## 支持的编程语言

- Java
- JavaScript
- TypeScript (Node.js)
- Python
- C#
- Go
- PHP
- Ruby
- Swift
- Kotlin
- Scala
- Dart

## 使用示例

### curl 示例

```bash
# POST 请求
curl -X POST "http://localhost:3000/api/openapi/generator/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "oas": "https://petstore3.swagger.io/api/v3/openapi.json",
    "lan": "java"
  }' \
  --output generated-sdk.zip

# GET 请求
curl "http://localhost:3000/api/openapi/generator/generate?oas=https://petstore3.swagger.io/api/v3/openapi.json&lan=java" \
  --output generated-sdk.zip
```

### JavaScript 示例

```javascript
// 使用 fetch API
fetch('/api/openapi/generator/generate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    oas: 'https://petstore3.swagger.io/api/v3/openapi.json',
    lan: 'java'
  })
})
.then(response => response.blob())
.then(blob => {
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'generated-sdk.zip';
  a.click();
});
```

## 注意事项

1. 确保OpenAPI Generator CLI JAR文件存在且可执行
2. 确保有足够的磁盘空间用于临时文件
3. 生成大型SDK时可能需要较长时间，建议设置合适的超时时间
4. 临时文件会在生成完成后自动清理

## 错误处理

API会返回标准的HTTP状态码：
- 200: 成功
- 400: 请求参数错误
- 500: 服务器内部错误

错误响应格式：
```json
{
  "code": "error",
  "message": "错误描述信息"
}
```
