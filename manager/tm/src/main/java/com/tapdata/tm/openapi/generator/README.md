# OpenAPI Generator Controller

这是一个用于生成代码并以zip压缩包形式下载的Spring Boot Controller。

## 功能特性

1. **代码生成**: 使用OpenAPI Generator工具生成客户端代码
2. **多语言支持**: 支持Java、JavaScript、Python等多种编程语言
3. **ZIP下载**: 生成的代码自动打包成ZIP文件供下载
4. **模板支持**: 使用自定义模板目录进行代码生成

## API端点

### 1. 生成代码并下载

```
POST /api/openapi-generator/generate
```

**参数:**
- `openApiSpec` (必需): OpenAPI规范内容
- `generatorName` (必需): 生成器名称 (如: java, javascript, python等)
- `packageName` (可选): 包名，默认为 "com.tapdata.generated"
- `clientName` (可选): 客户端名称，默认为 "TapdataClient"

**响应:** ZIP文件下载

### 2. 获取支持的生成器列表

```
GET /api/openapi-generator/generators
```

**响应:** 支持的生成器类型数组

### 3. 验证OpenAPI规范

```
POST /api/openapi-generator/validate
```

**参数:**
- `openApiSpec` (必需): OpenAPI规范内容

**响应:** 验证结果 (true/false)

## 使用示例

### 使用curl生成Java客户端

```bash
curl -X POST "http://localhost:8080/api/openapi-generator/generate" \
  -F "openApiSpec=@openapi.json" \
  -F "generatorName=java" \
  -F "packageName=com.example.api" \
  -F "clientName=MyApiClient" \
  -o generated-client.zip
```

### 使用curl获取支持的生成器

```bash
curl -X GET "http://localhost:8080/api/openapi-generator/generators"
```

### 使用curl验证OpenAPI规范

```bash
curl -X POST "http://localhost:8080/api/openapi-generator/validate" \
  -F "openApiSpec=@openapi.json"
```

## 配置

可以通过以下配置项自定义行为：

```properties
# 模板目录路径
openapi.generator.template.path=/path/to/templates

# 临时目录路径
openapi.generator.temp.dir=/tmp
```

## 支持的生成器类型

- java
- javascript
- typescript-node
- python
- csharp
- go
- php
- ruby
- swift
- kotlin
- scala
- rust
- dart
- android
- objc
- cpp-qt-client
- cpp-restsdk

## 模板目录结构

```
/path/to/openapi-generator/
├── templates/
│   ├── java/
│   │   └── okhttp-gson/
│   ├── javascript/
│   └── python/
```

## 注意事项

1. 如果系统中安装了OpenAPI Generator CLI，将优先使用CLI进行代码生成
2. 如果CLI不可用，将使用内置的简化模板生成基本的客户端代码
3. 生成的代码会自动清理临时文件
4. 支持自定义模板，模板文件放在指定的模板目录中

## 错误处理

- 如果OpenAPI规范无效，将返回验证错误
- 如果生成过程中出现错误，将返回HTTP 500错误
- 所有错误信息都会记录在日志中

## 依赖要求

- Spring Boot 2.x+
- Java 11+
- Jackson (用于JSON处理)
- Lombok (用于简化代码)
