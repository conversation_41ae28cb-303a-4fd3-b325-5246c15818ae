# OpenAPI Generator 配置示例
# 复制此文件内容到你的 application.properties 或 application.yml 中

# ========================================
# OpenAPI Generator 配置
# ========================================

# OpenAPI Generator JAR包路径
# 推荐使用 classpath 路径，兼容本地调试和打包后运行
openapi.generator.jar.path=classpath:openapi-generator/openapi-generator-cli.jar

# 如果需要使用外部JAR文件，可以使用绝对路径
# openapi.generator.jar.path=/opt/tapdata/openapi-generator/openapi-generator-cli.jar

# 模板目录路径配置
# 推荐使用 classpath 路径，兼容本地调试和打包后运行
openapi.generator.template.path=classpath:openapi-generator

# 如果需要使用外部模板目录，可以使用绝对路径
# openapi.generator.template.path=/opt/tapdata/templates/openapi-generator

# 临时目录配置
# 默认使用系统临时目录，也可以指定自定义路径
openapi.generator.temp.dir=${java.io.tmpdir}
# openapi.generator.temp.dir=/tmp/tapdata-openapi

# ========================================
# 其他相关配置
# ========================================

# 文件上传大小限制 (如果需要上传大的OpenAPI规范文件)
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# HTTP超时配置
spring.mvc.async.request-timeout=300000

# 日志配置
logging.level.com.tapdata.tm.openapi.generator=DEBUG
