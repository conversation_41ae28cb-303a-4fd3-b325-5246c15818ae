# OpenAPI Generator Configuration Example
# Copy this file content to your application.properties or application.yml

# ========================================
# OpenAPI Generator Configuration
# ========================================

# OpenAPI Generator JAR file path
# Recommended to use classpath path, compatible with local debugging and packaged deployment
openapi.generator.jar.path=classpath:openapi-generator/openapi-generator-cli.jar

# If you need to use external JAR file, you can use absolute path
# openapi.generator.jar.path=/opt/tapdata/openapi-generator/openapi-generator-cli.jar

# Template directory path configuration
# Recommended to use classpath path, compatible with local debugging and packaged deployment
openapi.generator.template.path=classpath:openapi-generator

# If you need to use external template directory, you can use absolute path
# openapi.generator.template.path=/opt/tapdata/templates/openapi-generator

# Temporary directory configuration
# Default uses system temporary directory, can also specify custom path
openapi.generator.temp.dir=${java.io.tmpdir}
# openapi.generator.temp.dir=/tmp/tapdata-openapi

# ========================================
# Other Related Configurations
# ========================================

# File upload size limit (if need to upload large OpenAPI specification files)
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# HTTP timeout configuration
spring.mvc.async.request-timeout=300000

# Logging configuration
logging.level.com.tapdata.tm.openapi.generator=DEBUG
