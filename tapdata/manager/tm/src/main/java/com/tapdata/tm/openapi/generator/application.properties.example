# OpenAPI Generator 配置示例
# 复制此文件内容到你的 application.properties 或 application.yml 中

# ========================================
# OpenAPI Generator 配置
# ========================================

# 模板目录路径配置
# 推荐使用 classpath 路径，兼容本地调试和打包后运行
openapi.generator.template.path=classpath:openapi-generator

# 如果需要使用外部模板目录，可以使用绝对路径
# openapi.generator.template.path=/opt/tapdata/templates/openapi-generator

# 临时目录配置
# 默认使用系统临时目录，也可以指定自定义路径
openapi.generator.temp.dir=${java.io.tmpdir}
# openapi.generator.temp.dir=/tmp/tapdata-openapi

# ========================================
# 其他相关配置
# ========================================

# 文件上传大小限制 (如果需要上传大的OpenAPI规范文件)
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# 日志级别配置 (可选)
logging.level.com.tapdata.tm.openapi.generator=INFO

# ========================================
# YAML 格式配置示例 (application.yml)
# ========================================

# openapi:
#   generator:
#     template:
#       path: classpath:openapi-generator
#     temp:
#       dir: ${java.io.tmpdir}
# 
# spring:
#   servlet:
#     multipart:
#       max-file-size: 10MB
#       max-request-size: 10MB
# 
# logging:
#   level:
#     com.tapdata.tm.openapi.generator: INFO
