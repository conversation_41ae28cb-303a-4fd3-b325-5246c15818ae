package io.tapdata.inspect.compare;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.tapdata.constant.*;
import com.tapdata.entity.Connections;
import com.tapdata.entity.TapdataRecoveryEvent;
import com.tapdata.entity.inspect.*;
import com.tapdata.tm.commons.schema.Field;
import com.tapdata.tm.commons.schema.MetadataInstancesDto;
import com.tapdata.tm.commons.task.dto.TaskDto;
import io.tapdata.exception.TapPdkBaseException;
import io.tapdata.inspect.AutoRecovery;
import io.tapdata.inspect.AutoRecoveryClient;
import io.tapdata.inspect.InspectTaskContext;
import io.tapdata.inspect.exception.AutoRecoveryException;
import io.tapdata.inspect.exception.TimeoutAutoRecoveryException;
import io.tapdata.inspect.util.InspectJobUtil;
import io.tapdata.pdk.core.api.ConnectorNode;
import io.tapdata.pdk.core.error.TapPdkRunnerUnknownException;
import io.tapdata.pdk.core.utils.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2020/9/21 7:55 上午
 * @description
 */
public class TableRowContentInspectJob extends InspectTableRowJob {
	private final Logger logger = LogManager.getLogger(TableRowContentInspectJob.class);
	private final Gson gson = new GsonBuilder().serializeNulls().create();

	// 统计变量
	protected long current = 0;
	protected long both = 0;
	protected long sourceOnly = 0;
	protected long targetOnly = 0;
	protected long rowPassed = 0;
	protected long rowField = 0;
	protected long startTime = System.currentTimeMillis() / 1000;
	long max = 0L;
	private final AtomicBoolean firstCompareKeyValue = new AtomicBoolean();
	private final AtomicBoolean firstCompareAllValue = new AtomicBoolean();

	private final String NO_PDK_HASH = "_no_pk_hash";


	private TaskDto task;

	private boolean moveSource = true;
	private boolean moveTarget = true;
	private boolean sourceHasNext = true;
	private boolean targetHasNext = true;

	private long uniqueFieldLimit;

	private long otherFieldLimit;

	List<String> sourceKeys;

	List<String> targetKeys;

	public TableRowContentInspectJob(InspectTaskContext inspectTaskContext) {
		super(inspectTaskContext);
	}

	@Override
	protected void doRun() {
		int retry = 0;
		while (retry < 4) {
			try {
				compare(inspectTask, source, target, stats, (inspectResultStats, inspectDetails) -> progressUpdateCallback.progress(inspectTask, stats, inspectDetails));
				break;
			} catch (Exception e) {
				if (retry >= 3) {
					logger.error(String.format("Failed to compare the count of rows in table %s.%s and table %s.%s, the taskId is %s",
							source.getName(), inspectTask.getSource().getTable(),
							target.getName(), inspectTask.getTarget().getTable(), inspectTask.getTaskId()), e);

					stats.setEnd(new Date());
					stats.setStatus(InspectStatus.ERROR.getCode());
					stats.setResult("failed");
					stats.setErrorMsg(e.getMessage());
					break;
				}
				retry++;
				stats.setErrorMsg(String.format("Check has an exception and is trying again..., The number of retries: %s", retry));
				stats.setStatus(InspectStatus.RUNNING.getCode());
				stats.setEnd(new Date());
				stats.setResult("failed");
				progressUpdateCallback.progress(inspectTask, stats, null);
				logger.error(String.format("Check has an exception and is trying again..., The number of retries: %s", retry), e);
				try {
					TimeUnit.SECONDS.sleep(5);
				} catch (InterruptedException interruptedException) {
					break;
				}
			}
		}
	}

	public void compare(InspectTask inspectTask, Connections source, Connections target, InspectResultStats stats, CompareProgress compareProgress) {

		CompareFunction<Map<String, Object>, String> compareFn = null;

		boolean fullMatch = inspectTask.isFullMatch();
		InspectLimit inspectLimit = inspectTask.getLimit();
		 uniqueFieldLimit = inspectLimit != null ? inspectLimit.getKeep() : 1000;
		 uniqueFieldLimit = uniqueFieldLimit == 0 ? 1000 : uniqueFieldLimit;

		// force equals uniqueFieldLimit
		otherFieldLimit = uniqueFieldLimit;
		boolean ignoreTimePrecision = inspectTaskContext.getIgnoreTimePrecision();
		String roundingMode = inspectTaskContext.getRoundingMode();


		compareFn = initData(fullMatch,ignoreTimePrecision,roundingMode);


		sourceKeys = getSortColumns(inspectTask.getSource().getSortColumn());
		targetKeys = getSortColumns(inspectTask.getTarget().getSortColumn());
        try {
            autoRecoveryStart(compareProgress, fullMatch, sourceKeys, targetKeys);
        } catch (Throwable e) {
            stats.setEnd(new Date());
            stats.setResult("failed");
            stats.setStatus(InspectStatus.ERROR.getCode());
            stats.setErrorMsg(e.getMessage() + "\n" + Log4jUtil.getStackString(e));
            compareProgress.update(stats, null);
            logger.error("Auto-recovery failed: {}", e.getMessage(), e);
            return;
        }

		try (DiffDetailCursor diffDetailCursor = new DiffDetailCursor(inspectTask.getTaskId(), inspectResultParentId, clientMongoOperator, sourceKeys, targetKeys)) {
			List<InspectDetail> inspectDetails = new ArrayList<>();
			long sourceTotal = 0;
			long targetTotal = 0;
			while (diffDetailCursor.next() && !Thread.interrupted()) {
				try (
						BaseResult<Map<String, Object>> sourceCursor = queryForCursor(source, inspectTask.getSource(), sourceNode, fullMatch, sourceKeys, diffDetailCursor.getData());
						BaseResult<Map<String, Object>> targetCursor = queryForCursor(target, inspectTask.getTarget(), targetNode, fullMatch, targetKeys, diffDetailCursor.getData())
				) {
					sourceTotal += sourceCursor.getTotal();
					targetTotal += targetCursor.getTotal();

					logger.info(sourceTotal + " -> " + targetTotal);



					Map<String, Object> sourceRecord = null;
					Map<String, Object> targetRecord = null;
					if (diffDetailCursor.diffCounts() > 0) {
						// 差异数据数量
						max = diffDetailCursor.diffCounts();
					} else {
						// 全量校验，源和目标取最大数
						max = Math.max(sourceTotal, targetTotal);
					}

					while (moveSource || moveTarget) {

						if (moveSource) {
							if (sourceCursor.hasNext()) {
								sourceRecord = sourceCursor.next();
							} else {
								sourceRecord = null;
							}
							if (null == sourceRecord) {
								sourceHasNext = false;
								sourceRecord = Collections.emptyMap();
							} else {
								sourceHasNext = true;
							}
						}
						if (moveTarget) {
							if (targetCursor.hasNext()) {
								targetRecord = targetCursor.next();
							} else {
								targetRecord = null;
							}
							if (null == targetRecord) {
								targetHasNext = false;
								targetRecord = Collections.emptyMap();
							} else {
								targetHasNext = true;
							}
						}

						if (!sourceHasNext && !targetHasNext) {
							break;
						}

						current++;
						handleCurrent(sourceTotal,targetTotal,compareProgress,stats);

						String msg = handleCompareRecord(sourceRecord, targetRecord,
								fullMatch, compareFn, inspectDetails,sourceCursor,targetCursor,ignoreTimePrecision,roundingMode);


						if (msg != null) {
							logger.debug(msg);
						}

						if (inspectDetails.size() > 50) {
							compareProgress.update(stats, inspectDetails);
							inspectDetails = new ArrayList<>();
						}
					} // end while data

					// long sourceTotal, long targetTotal, double progress, int cycles, long both, long source_only, long target_only, long row_passed, long row_failed, int speed
					stats.setSource_total(sourceTotal);
					stats.setTarget_total(targetTotal);
				}
			} // end while diffDetailCursor

            handleDoneInspectResultStats(stats,compareProgress,inspectDetails);
		} catch (Throwable e) {
			stats.setEnd(new Date());
			stats.setResult("failed");
			stats.setStatus(InspectStatus.ERROR.getCode());
			InspectJobUtil.buildStatsErrorMsg(stats, e);
			compareProgress.update(stats, null);
		}
	}

	public CompareFunction<Map<String, Object>, String> initData(boolean fullMatch,boolean ignoreTimePrecision,String roundMode) {
		DefaultCompare compareFn = null;
		if (fullMatch) {
			if (null == inspectTask.getSource().getColumns() || inspectTask.getSource().getColumns().isEmpty()
					|| null == inspectTask.getTarget().getColumns() || inspectTask.getTarget().getColumns().isEmpty()) {
				compareFn = generateFullFieldFn();
			} else {
				compareFn = new DefaultCompare(inspectTask.getSource().getColumns(), inspectTask.getTarget().getColumns());
			}
		}
		if(compareFn != null && ignoreTimePrecision){
			compareFn.setIgnoreTimePrecision(true);
			compareFn.setRoundingMode(roundMode);
		}

		if (inspectTask.getBatchSize() > 0) {
			batchSize = inspectTask.getBatchSize();
		}


		if (logger.isDebugEnabled()) {
			try {
				logger.debug(JSONUtil.obj2JsonPretty(source));
				logger.debug(JSONUtil.obj2JsonPretty(target));
			} catch (JsonProcessingException ignore) {
			}
		}
		return compareFn;
	}
	protected DefaultCompare generateFullFieldFn() {
		if (StringUtils.isNotBlank(inspectTaskContext.getFlowId())) {
			List<String> sourceColumns = new ArrayList<>();
			List<String> targetColumns = new ArrayList<>();
			Map<String, Object> params = new HashMap<>();
			params.put("nodeId", inspectTask.getTarget().getNodeId());
			params.put("tableFilter", inspectTask.getTarget().getTable());
			params.put("pageSize", 1);
			MetadataInstancesDto targetMetadataInstance = clientMongoOperator.findOne(params, ConnectorConstant.METADATA_INSTANCE_COLLECTION + "/node/schemaPage", MetadataInstancesDto.class);
			if (null != targetMetadataInstance && CollectionUtils.isNotEmpty(targetMetadataInstance.getFields())) {
				String targetDatabaseType = targetMetadataInstance.getSource().getDatabase_type();
				for (Field field : targetMetadataInstance.getFields()) {
					if (Boolean.TRUE.equals(field.isDeleted()) || Field.SOURCE_JOB_ANALYZE.equalsIgnoreCase(field.getSource()) || Field.SOURCE_VIRTUAL_HASH.equalsIgnoreCase(field.getSource()) ||
							NO_PDK_HASH.equalsIgnoreCase(field.getFieldName()) || ("Sybase".equalsIgnoreCase(targetDatabaseType) && "timestamp".equalsIgnoreCase(field.getDataType()))
						|| 	("SQL Server".equalsIgnoreCase(targetDatabaseType) && "timestamp".equalsIgnoreCase(field.getDataType()))
					    || (targetDatabaseType.contains("MongoDB") && field.getFieldName().contains(".")))
						continue;
					targetColumns.add(field.getFieldName());
					if (field.getFieldName().equals(field.getOriginalFieldName())) {
						sourceColumns.add(field.getFieldName());
					} else {
						sourceColumns.add(field.getOriginalFieldName());
					}
				}
			}
            // 这里 sourceColumns, targetColumns 有值，需要将配置同步到 stats.source.columns, stats.target.columns 中
            // 会影响校验结果格式，从 'Different fields: title' 变为 'Different index: 1'
            // 结果如果是 'Different index: 1' 前端会从 stats.source.columns 中去找对应字段，找不到则不显示！！
            stats.getSource().setColumns(sourceColumns);
            stats.getTarget().setColumns(targetColumns);
			return new DefaultCompare(sourceColumns, targetColumns);
		} else {
			return new DefaultCompare();
		}
	}

	public void handleCurrent(long sourceTotal,long targetTotal,CompareProgress compareProgress,InspectResultStats stats){
		if (current % 5000 == 0) {
			double progress = getProgress();
			if (current % 20000 == 0) {
				logger.info("Compared " + current + ", total " + max + ", completed " + Math.round(progress * 100) + "%");
			}
			// long sourceTotal, long targetTotal, double progress, int cycles, long both, long source_only, long target_only, long row_passed, long row_failed, int speed
			stats.setSource_total(sourceTotal);
			stats.setTarget_total(targetTotal);
			stats.setProgress(progress);
			stats.setCycles(current);
			stats.setBoth(both);
			stats.setSource_only(sourceOnly);
			stats.setTarget_only(targetOnly);
			stats.setRow_passed(rowPassed);
			stats.setRow_failed(rowField);
			stats.setSpeed(current / (System.currentTimeMillis() / 1000 - startTime + 1));

			compareProgress.update(stats, null);
		}
	}

	public String handleCompareRecord(Map<String, Object> sourceRecord,Map<String, Object> targetRecord,boolean fullMatch,
									CompareFunction<Map<String, Object>, String> compareFn,
									List<InspectDetail> inspectDetails,BaseResult<Map<String, Object>> sourceCursor,
									  BaseResult<Map<String, Object>> targetCursor,boolean ignoreTimePrecision,String roundingMode){

		String sourceVal = sourceCursor.getSortValue(sourceRecord);
		String targetVal = targetCursor.getSortValue(targetRecord);
		Object[] sourceKeyArr = getKeyArray(sourceRecord, sourceKeys);
		Object[] targetKeyArr = getKeyArray(targetRecord, targetKeys);
		int compare = CommonUtil.compareObjects(sourceKeyArr, targetKeyArr,ignoreTimePrecision,roundingMode);
		String msg = null;
		if (firstCompareKeyValue.compareAndSet(false, true)) {
			logger.info("Inspect job[{}] first compare sort value, source: {}, target: {}, result: {}",
					String.join("-", inspectTaskContext.getName(), source.getName(), inspectTask.getSource().getTable(), target.getName(), inspectTask.getTarget().getTable()),
					sourceKeyArr, targetKeyArr, compare);
		}

		if (fullMatch && compare == 0) {
			String res = compareRecord(current, sourceVal, targetVal, sourceRecord, targetRecord, compareFn);
			if (firstCompareAllValue.compareAndSet(false, true)) {
				logger.info("Inspect job[{}] first compare all value, source: {}, target: {}, result: {}",
						String.join("-", inspectTaskContext.getName(), source.getName(), inspectTask.getSource().getTable(), target.getName(), inspectTask.getTarget().getTable()),
						sourceRecord, targetRecord, res);
			}
			if (null == res) {
				rowPassed++;
			} else {
				rowField++;
				if (otherFieldLimit > 0) {
					otherFieldLimit--;
					InspectDetail detail = new InspectDetail();

					detail.setSource(diffRecordTypeConvert(sourceRecord, inspectTask.getSource().getColumns()));
					detail.setTarget(diffRecordTypeConvert(targetRecord, inspectTask.getTarget().getColumns()));
					detail.setType("otherFields");
					detail.setMessage(res);

					inspectDetails.add(detail);
				}
			}
		}
		if (compare == 0) {
			moveSource = true;
			moveTarget = true;
			both++;
		} else if (compare < 0) { // ASC
			moveSource = true;
			moveTarget = false;
			msg = "SOURCE " + sourceVal;
			if (uniqueFieldLimit > 0) {
				uniqueFieldLimit--;
				InspectDetail detail = new InspectDetail();
				if(sourceVal == null){
                                    targetOnly++;
					detail.setTarget(diffRecordTypeConvert(targetRecord, inspectTask.getTarget().getColumns()));
				}else {
                                    sourceOnly++;
					detail.setSource(diffRecordTypeConvert(sourceRecord, inspectTask.getSource().getColumns()));
				}
				detail.setType("uniqueField");

				inspectDetails.add(detail);
							} else {
                                sourceOnly++;
                            }
						} else {
							moveSource = false;
							moveTarget = true;
							msg = "TARGET " + targetVal;
							if (InspectDifferenceMode.isAll(inspectTaskContext.getInspectDifferenceMode())) {
				if (uniqueFieldLimit > 0) {
					uniqueFieldLimit--;
					InspectDetail detail = new InspectDetail();
					if(targetVal == null){
                                        targetOnly++;
						detail.setSource(diffRecordTypeConvert(sourceRecord, inspectTask.getSource().getColumns()));
					}else {
                                        targetOnly++;
						detail.setTarget(diffRecordTypeConvert(targetRecord, inspectTask.getTarget().getColumns()));
					}
					detail.setTarget(diffRecordTypeConvert(targetRecord, inspectTask.getTarget().getColumns()));
					detail.setType("uniqueField");
					inspectDetails.add(detail);
								} else {
                                    targetOnly++;
                                }
							}
						}
		if (!sourceHasNext) {
			moveSource = false;
			if (targetHasNext) {
				msg = "TARGET " + targetVal;
				moveTarget = true;
			}
		}
		if (!targetHasNext) {
			moveTarget = false;
			if (sourceHasNext) {
				msg = "SOURCE " + sourceVal;
				moveSource = true;
			}
		}
		return msg;
	}

	public void handleDoneInspectResultStats(InspectResultStats stats,CompareProgress compareProgress,List<InspectDetail> inspectDetails){
		stats.setCycles(current);
		stats.setBoth(both);
		stats.setSource_only(sourceOnly);
		stats.setTarget_only(targetOnly);
		stats.setRow_passed(rowPassed);
		stats.setRow_failed(rowField);
		stats.setSpeed(current / (System.currentTimeMillis() / 1000 - startTime + 1));

		stats.setEnd(new Date());
		stats.setProgress(1);
		stats.setResult((stats.getSource_only() > 0 || stats.getTarget_only() > 0 || stats.getRow_failed() > 0) ? "failed" : "passed");
		stats.setStatus(InspectStatus.DONE.getCode());

		compareProgress.update(stats, inspectDetails);
		logger.info("compare " + current);
	}

	private double getProgress() {
		return new BigDecimal(current)
				.divide(new BigDecimal(current > max ? current + 1 : max), 4, BigDecimal.ROUND_DOWN).doubleValue();
	}

	private String compareRecord(long index, String sourceId, String targetId, Map<String, Object> sourceRecord, Map<String, Object> targetRecord, CompareFunction<Map<String, Object>, String> compareFn) {
		if (compareFn == null) {
			return "Compare fn is null.";
		}
		try {
			String result = compareFn.apply(sourceRecord, targetRecord, sourceId, targetId);
			if (null != result) {
				if (logger.isDebugEnabled()) {
					logger.debug("Different record: \n [" + sourceId + "]: " + gson.toJson(sourceRecord) + "\n -> \n [" + targetId + "]: " + gson.toJson(targetRecord));
//        } else {
//          logger.info(sourceId + " -> " + targetId + " has different fields.");
				}
			}
			return result;
		} catch (Exception e) {
			throw new RuntimeException("Call compare function failed: " + e.getMessage(), e);
		}
	}

	protected BaseResult<Map<String, Object>> queryForCursor(Connections connections, InspectDataSource inspectDataSource, ConnectorNode connectorNode, boolean fullMatch, List<String> dataKeys, List<List<Object>> diffKeyValues) {
		inspectDataSource.setDirection("DESC"); // force desc
		Set<String> columns = null;
		if (null != inspectDataSource.getColumns()) {
			columns = new LinkedHashSet<>(inspectDataSource.getColumns());
		}
		return new PdkResult(
				getSortColumns(inspectDataSource.getSortColumn()),
				connections,
				inspectDataSource.getTable(),
				columns,
				connectorNode,
				fullMatch,
				dataKeys,
				diffKeyValues,
				null != inspectDataSource.getIsFilter() && inspectDataSource.getIsFilter() ? inspectDataSource.getConditions() : null,
				inspectDataSource.isEnableCustomCommand(),inspectDataSource.getCustomCommand(),inspectDataSource.getCollate(),
				inspectDataSource.getCustomNullSort()
		);
	}

	private BaseResult<Map<String, Object>> queryRecoveryForCursor(Connections connections, InspectDataSource inspectDataSource, ConnectorNode connectorNode, boolean fullMatch, List<String> dataKeys, List<List<Object>> diffKeyValues) {
		inspectDataSource.setDirection("DESC"); // force desc
		Set<String> columns = null;
		if (null != inspectDataSource.getColumns()) {
			columns = new LinkedHashSet<>(inspectDataSource.getColumns());
		}
		return new RecoveryPdkResult(
				getSortColumns(inspectDataSource.getSortColumn()),
				connections,
				inspectDataSource.getTable(),
				columns,
				connectorNode,
				fullMatch,
				dataKeys,
				diffKeyValues,
				null != inspectDataSource.getIsFilter() && inspectDataSource.getIsFilter() ? inspectDataSource.getConditions() : null,
				inspectDataSource.isEnableCustomCommand(),inspectDataSource.getCustomCommand(),inspectDataSource.getCollate()
		);
	}


	@FunctionalInterface
	public interface CompareProgress {

		void update(InspectResultStats stats, List<InspectDetail> inspectDetails);

	}

	private Object[] getKeyArray(Map<String, Object> record, List<String> keys) {
		Object[] objects = new Object[keys.size()];
		for (int i = 0; i < keys.size(); i++) {
			String key = keys.get(i);
			objects[i] = MapUtil.getValueByKey(record, key);
		}
		return objects;
	}

    private void autoRecoveryStart(CompareProgress compareProgress, boolean fullMatch, List<String> sourceKeys, List<String> targetKeys) throws AutoRecoveryException {
		try {
			if (!Boolean.TRUE.equals(inspectTaskContext.getEnableRecovery()) || null == inspectTaskContext.getFlowId() || inspectTaskContext.getFlowId().isEmpty()) {
				return;
			}

			stats.setStatus(InspectStatus.RECOVERING.getCode());
			compareProgress.update(stats, null);

			String flowId = inspectTaskContext.getFlowId();
			String taskId = inspectTask.getTaskId();
			try (DiffDetailCursor diffDetailCursor = new DiffDetailCursor(inspectTask.getTaskId(), inspectResultParentId, clientMongoOperator, sourceKeys, targetKeys)) {
				if (diffDetailCursor.diffCounts() > 0) {
					AtomicBoolean isRecoveryEnd = new AtomicBoolean(false);
					AtomicLong queueTotals = new AtomicLong(0);
					try (AutoRecoveryClient autoRecoveryClient = autoRecoveryClientInit(flowId, taskId, isRecoveryEnd, queueTotals)) {
						autoRecoveryClient.enqueue(TapdataRecoveryEvent.createBegin(taskId));
						while (diffDetailCursor.next() && !Thread.interrupted()) {
							try (BaseResult<Map<String, Object>> sourceCursor = queryRecoveryForCursor(source, inspectTask.getSource(), sourceNode, fullMatch, sourceKeys, diffDetailCursor.getData())) {
								while (sourceCursor.hasNext()) {
									queueTotals.addAndGet(1);
									Map<String, Object> data = sourceCursor.next();
									if (null != data) {
										autoRecoveryClient.enqueue(TapdataRecoveryEvent.createInsert(taskId, sourceCursor.tableName, data));
									}
								}
							}
						}
						autoRecoveryClient.enqueue(TapdataRecoveryEvent.createEnd(taskId));

						autoRecoveryWaitCompleted(taskId, isRecoveryEnd, queueTotals);
						logger.info("Auto-recovery completed, taskId: {}, totals: {}", taskId, diffDetailCursor.diffCounts());
					}
				}
			}

			stats.setStatus(InspectStatus.RUNNING.getCode());
			compareProgress.update(stats, null);
		} catch (InterruptedException e) {
			Thread.currentThread().interrupt();
		} catch (Exception e) {
			throw new AutoRecoveryException(e);
		}
	}

    private AutoRecoveryClient autoRecoveryClientInit(String flowId, String taskId, AtomicBoolean isRecoveryEnd, AtomicLong queueTotals) {
        return AutoRecovery.initClient(flowId, taskId, tapdataEvent -> {
            switch (tapdataEvent.getRecoveryType()) {
                case TapdataRecoveryEvent.RECOVERY_TYPE_BEGIN:
                    logger.info("Auto-recovery begin, taskId: {}", tapdataEvent.getInspectTaskId());
                    break;
                case TapdataRecoveryEvent.RECOVERY_TYPE_DATA:
                    queueTotals.addAndGet(-1);
                    break;
                case TapdataRecoveryEvent.RECOVERY_TYPE_END:
                    isRecoveryEnd.set(true);
                    logger.info("Auto-recovery end, taskId: {}", tapdataEvent.getInspectTaskId());
                    break;
                default:
                    break;
            }
        });
    }

    private void autoRecoveryWaitCompleted(String taskId, AtomicBoolean isRecoveryEnd, AtomicLong queueTotals) throws InterruptedException {
        long beginTs = System.currentTimeMillis();
        logger.info("Auto-recovery wait completed, taskId: {}", taskId);
        while (queueTotals.get() > 0 && !isRecoveryEnd.get() && !Thread.interrupted()) {
            TimeoutAutoRecoveryException.assertFalse(taskId, System.currentTimeMillis() - beginTs > 60000);
            Thread.sleep(500);
        }
    }
}
