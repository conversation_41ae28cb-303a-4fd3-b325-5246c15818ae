package io.tapdata.inspect;

import com.tapdata.entity.Connections;
import com.tapdata.entity.inspect.InspectTask;
import com.tapdata.mongo.ClientMongoOperator;
import io.tapdata.pdk.core.api.ConnectorNode;

/**
 * <AUTHOR>
 * @Description
 * @create 2022-06-06 11:47
 **/
public abstract class InspectContext {
	protected String name;
	protected String flowId;
	protected InspectTask task;
	protected Connections source;
	protected Connections target;
	protected String inspectResultParentId;
	/**
	 * 差异结果模式：All,OnSourceExists
	 */
	private String inspectDifferenceMode;
	private Boolean enableRecovery;
	private Boolean ignoreTimePrecision;
	private String roundingMode;

	protected ProgressUpdate progressUpdateCallback;
	protected ConnectorNode sourceConnectorNode;
	protected ConnectorNode targetConnectorNode;
	protected ClientMongoOperator clientMongoOperator;
	protected  InspectService inspectService;


	public InspectContext(
			String name,
			String flowId,
			InspectTask task,
			Connections source,
			Connections target,
			String inspectResultParentId,
			String inspectDifferenceMode,
            Boolean enableRecovery,
			ProgressUpdate progressUpdateCallback,
			ConnectorNode sourceConnectorNode,
			ConnectorNode targetConnectorNode,
			ClientMongoOperator clientMongoOperator,
			InspectService inspectService

	) {
		this.name = name;
		this.flowId = flowId;
		this.task = task;
		this.source = source;
		this.target = target;
		this.inspectResultParentId = inspectResultParentId;
		this.inspectDifferenceMode = inspectDifferenceMode;
		this.enableRecovery = enableRecovery;
		this.progressUpdateCallback = progressUpdateCallback;
		this.sourceConnectorNode = sourceConnectorNode;
		this.targetConnectorNode = targetConnectorNode;
		this.clientMongoOperator = clientMongoOperator;
		this.inspectService = inspectService;
	}

	public String getName() {
		return name;
	}

    public String getFlowId() {
        return flowId;
    }

    public InspectTask getTask() {
		return task;
	}

	public Connections getSource() {
		return source;
	}

	public Connections getTarget() {
		return target;
	}

	public String getInspectResultParentId() {
		return inspectResultParentId;
	}

	public String getInspectDifferenceMode() {
		return inspectDifferenceMode;
	}

    public Boolean getEnableRecovery() {
        return enableRecovery;
    }

    public ProgressUpdate getProgressUpdateCallback() {
		return progressUpdateCallback;
	}

	public ConnectorNode getSourceConnectorNode() {
		return sourceConnectorNode;
	}

	public ConnectorNode getTargetConnectorNode() {
		return targetConnectorNode;
	}

	public ClientMongoOperator getClientMongoOperator() {
		return clientMongoOperator;
	}

	public InspectService getInspectService() {
		return inspectService;
	}

	public Boolean getIgnoreTimePrecision() {
		return ignoreTimePrecision;
	}

	public void setIgnoreTimePrecision(Boolean ignoreTimePrecision) {
		this.ignoreTimePrecision = ignoreTimePrecision;
	}
	public String getRoundingMode() {
		return roundingMode;
	}

	public void setRoundingMode(String roundingMode) {
		this.roundingMode = roundingMode;
	}
}
