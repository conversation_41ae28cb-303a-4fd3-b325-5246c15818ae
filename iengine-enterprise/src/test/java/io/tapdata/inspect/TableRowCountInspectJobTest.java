package io.tapdata.inspect;

import com.tapdata.constant.ConnectorConstant;
import com.tapdata.entity.Connections;
import com.tapdata.entity.inspect.InspectDataSource;
import com.tapdata.entity.inspect.InspectResult;
import com.tapdata.entity.inspect.InspectResultStats;
import com.tapdata.entity.inspect.InspectTask;
import com.tapdata.mongo.ClientMongoOperator;
import com.tapdata.tm.commons.util.MetaType;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.schema.value.DateTime;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.error.TaskInspectExCode_27;
import io.tapdata.exception.TapCodeException;
import io.tapdata.inspect.compare.*;
import io.tapdata.pdk.apis.context.TapConnectorContext;
import io.tapdata.pdk.apis.entity.QueryOperator;
import io.tapdata.pdk.apis.functions.ConnectorFunctions;
import io.tapdata.pdk.apis.functions.connector.source.BatchCountFunction;
import io.tapdata.pdk.apis.functions.connector.source.CountByPartitionFilterFunction;
import io.tapdata.pdk.apis.functions.connector.source.ExecuteCommandFunction;
import io.tapdata.pdk.apis.spec.TapNodeSpecification;
import io.tapdata.pdk.core.api.ConnectorNode;
import io.tapdata.pdk.core.tapnode.TapNodeInfo;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.message.Message;
import org.junit.jupiter.api.*;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class TableRowCountInspectJobTest {

     static InspectTask inspectTask;
     static ClientMongoOperator clientMongoOperator;

     static ConnectorNode sourceConnectorNode;

     static Connections source;
     static Connections target;

     static TapNodeSpecification tapNodeSpecification;

     static ConnectorFunctions connectorFunction;

     static ConnectorNode targetConnectorNode;

     @BeforeAll
     static void init() {
         inspectTask = new InspectTask();
         inspectTask.setTaskId("test");
         clientMongoOperator = Mockito.mock(ClientMongoOperator.class);
         sourceConnectorNode = new ConnectorNode();
         source = new Connections();
         source.setName("testSource");
         source.setDatabase_type("mysql");
         target = new Connections();
         target.setName("targetSource");
         target.setDatabase_type("mysql");
         Map<String, Object> params = new HashMap<>();
         params.put("connectionId", null);
         params.put("metaType", MetaType.table.name());
         params.put("tableName", null);
         when(clientMongoOperator.findOne(params, ConnectorConstant.METADATA_INSTANCE_COLLECTION + "/metadata/v2"
                 , TapTable.class)).thenReturn(Mockito.mock(TapTable.class));
         tapNodeSpecification = new TapNodeSpecification();
         tapNodeSpecification.setId("test");
         connectorFunction = new ConnectorFunctions();
         targetConnectorNode = new ConnectorNode();

     }

     @Test
     void testSourceCountByPartitionFilterFunctionException() {
         // input param
         InspectDataSource inspectSource = new InspectDataSource();
         List<QueryOperator> list = new ArrayList<>();
         QueryOperator queryOperator = new QueryOperator();
         queryOperator.setKey("test");
         list.add(queryOperator);
         inspectSource.setConditions(list);
         inspectSource.setIsFilter(true);
         InspectDataSource inspectTarget = new InspectDataSource();
         inspectTask.setSource(inspectSource);
         inspectTask.setTarget(inspectTarget);

         ReflectionTestUtils.setField(sourceConnectorNode, "connectorFunctions", connectorFunction);

         TapConnectorContext tapConnectorContext = new TapConnectorContext(tapNodeSpecification, new DataMap(), new DataMap(), Mockito.mock(Log.class));
         ReflectionTestUtils.setField(sourceConnectorNode, "connectorFunctions", connectorFunction);
         ReflectionTestUtils.setField(sourceConnectorNode, "connectorContext", tapConnectorContext);


         InspectTaskContext inspectTaskContext = new InspectTaskContext("test", null, inspectTask,
                 source, target, null, null, null, null, sourceConnectorNode, null, clientMongoOperator, null);
         TableRowCountInspectJob tableRowContentInspectJob = new TableRowCountInspectJob(inspectTaskContext);
         ReflectionTestUtils.setField(tableRowContentInspectJob, "progressUpdateCallback", Mockito.mock(ProgressUpdate.class));

         ReflectionTestUtils.invokeMethod(tableRowContentInspectJob, "doRun");
         InspectResultStats inspectResultStats = (InspectResultStats) ReflectionTestUtils.getField(tableRowContentInspectJob, "stats");

         Assertions.assertTrue(inspectResultStats.getErrorMsg().contains("Source node does not support count with filter function"));


     }

     @Test
     void testTargetCountByPartitionFilterFunctionException() {
         // input param
         InspectDataSource inspectSource = new InspectDataSource();
         InspectDataSource inspectTarget = new InspectDataSource();
         List<QueryOperator> list = new ArrayList<>();
         QueryOperator queryOperator = new QueryOperator();
         queryOperator.setKey("test");
         list.add(queryOperator);
         inspectTarget.setConditions(list);
         inspectTarget.setIsFilter(true);
         inspectSource.setConditions(list);
         inspectSource.setIsFilter(true);
         inspectTask.setSource(inspectSource);
         inspectTask.setTarget(inspectTarget);


         connectorFunction.supportCountByPartitionFilterFunction(Mockito.mock(CountByPartitionFilterFunction.class));
         ReflectionTestUtils.setField(sourceConnectorNode, "connectorFunctions", connectorFunction);

         TapConnectorContext tapConnectorContext = new TapConnectorContext(tapNodeSpecification, new DataMap(), new DataMap(), Mockito.mock(Log.class));
         ReflectionTestUtils.setField(sourceConnectorNode, "connectorFunctions", connectorFunction);
         ReflectionTestUtils.setField(sourceConnectorNode, "connectorContext", tapConnectorContext);

         ReflectionTestUtils.setField(targetConnectorNode, "connectorFunctions", new ConnectorFunctions());
         ReflectionTestUtils.setField(targetConnectorNode, "connectorContext", tapConnectorContext);

         InspectTaskContext inspectTaskContext = new InspectTaskContext("test", null, inspectTask,
                 source, target, null, null, null, null, sourceConnectorNode, targetConnectorNode, clientMongoOperator, null);
         TableRowCountInspectJob tableRowContentInspectJob = new TableRowCountInspectJob(inspectTaskContext);
         ReflectionTestUtils.setField(tableRowContentInspectJob, "progressUpdateCallback", Mockito.mock(ProgressUpdate.class));

         ReflectionTestUtils.invokeMethod(tableRowContentInspectJob, "doRun");
         InspectResultStats inspectResultStats = (InspectResultStats) ReflectionTestUtils.getField(tableRowContentInspectJob, "stats");

         Assertions.assertTrue(inspectResultStats.getErrorMsg().contains("Target node does not support count with filter function"));


     }


     @Test
     void testSourceExecuteCommandFunctionException() {
         // input param
         InspectDataSource inspectSource = new InspectDataSource();
         inspectSource.setEnableCustomCommand(true);
         Map<String, Object> customCommand = new HashMap<>();
         customCommand.put("test", "value");
         inspectSource.setCustomCommand(customCommand);
         InspectDataSource inspectTarget = new InspectDataSource();
         inspectTask.setSource(inspectSource);
         inspectTask.setTarget(inspectTarget);


         ConnectorFunctions connectorFunction = new ConnectorFunctions();
         ReflectionTestUtils.setField(sourceConnectorNode, "connectorFunctions", connectorFunction);

         TapConnectorContext tapConnectorContext = new TapConnectorContext(tapNodeSpecification, new DataMap(), new DataMap(), Mockito.mock(Log.class));
         ReflectionTestUtils.setField(sourceConnectorNode, "connectorFunctions", connectorFunction);
         ReflectionTestUtils.setField(sourceConnectorNode, "connectorContext", tapConnectorContext);

         InspectTaskContext inspectTaskContext = new InspectTaskContext("test", null, inspectTask,
                 source, target, null, null, null, null, sourceConnectorNode, null, clientMongoOperator, null);
         TableRowCountInspectJob tableRowContentInspectJob = new TableRowCountInspectJob(inspectTaskContext);
         ReflectionTestUtils.setField(tableRowContentInspectJob, "progressUpdateCallback", Mockito.mock(ProgressUpdate.class));

         ReflectionTestUtils.invokeMethod(tableRowContentInspectJob, "doRun");
         InspectResultStats inspectResultStats = (InspectResultStats) ReflectionTestUtils.getField(tableRowContentInspectJob, "stats");

         Assertions.assertTrue(inspectResultStats.getErrorMsg().contains("Source node does not support execute command function"));


     }

     @Test
     void testTargetExecuteCommandFunctionException() {
         // input param
         InspectDataSource inspectSource = new InspectDataSource();
         inspectSource.setEnableCustomCommand(true);
         Map<String, Object> customCommand = new HashMap<>();
         customCommand.put("params", new HashMap<>());
         inspectSource.setCustomCommand(customCommand);
         InspectDataSource inspectTarget = new InspectDataSource();
         inspectTarget.setCustomCommand(customCommand);
         inspectTarget.setEnableCustomCommand(true);
         inspectTask.setSource(inspectSource);
         inspectTask.setTarget(inspectTarget);


         connectorFunction.supportExecuteCommandFunction(Mockito.mock(ExecuteCommandFunction.class));
         ReflectionTestUtils.setField(sourceConnectorNode, "connectorFunctions", connectorFunction);
         TapNodeInfo tapNodeInfo = new TapNodeInfo();

         ReflectionTestUtils.setField(tapNodeInfo, "tapNodeSpecification", tapNodeSpecification);
         ReflectionTestUtils.setField(sourceConnectorNode, "tapNodeInfo", tapNodeInfo);

         TapConnectorContext tapConnectorContext = new TapConnectorContext(tapNodeSpecification, new DataMap(), new DataMap(), Mockito.mock(Log.class));
         ReflectionTestUtils.setField(sourceConnectorNode, "connectorFunctions", connectorFunction);
         ReflectionTestUtils.setField(sourceConnectorNode, "connectorContext", tapConnectorContext);

         ConnectorNode targetConnectorNode = new ConnectorNode();
         ReflectionTestUtils.setField(targetConnectorNode, "connectorFunctions", new ConnectorFunctions());
         ReflectionTestUtils.setField(targetConnectorNode, "connectorContext", tapConnectorContext);

         InspectTaskContext inspectTaskContext = new InspectTaskContext("test", null, inspectTask,
                 source, target, null, null, null, null, sourceConnectorNode, targetConnectorNode, clientMongoOperator, null);
         TableRowCountInspectJob tableRowContentInspectJob = new TableRowCountInspectJob(inspectTaskContext);
         ReflectionTestUtils.setField(tableRowContentInspectJob, "progressUpdateCallback", Mockito.mock(ProgressUpdate.class));

         ReflectionTestUtils.invokeMethod(tableRowContentInspectJob, "doRun");
         InspectResultStats inspectResultStats = (InspectResultStats) ReflectionTestUtils.getField(tableRowContentInspectJob, "stats");

         Assertions.assertTrue(inspectResultStats.getErrorMsg().contains("Target node does not support execute command function"));


     }


     @Test
     void testSourceBatchFunctionException() {
         // input param
         InspectDataSource inspectSource = new InspectDataSource();
         InspectDataSource inspectTarget = new InspectDataSource();
         inspectTask.setSource(inspectSource);
         inspectTask.setTarget(inspectTarget);


         ReflectionTestUtils.setField(sourceConnectorNode, "connectorFunctions", connectorFunction);

         TapConnectorContext tapConnectorContext = new TapConnectorContext(tapNodeSpecification, new DataMap(), new DataMap(), Mockito.mock(Log.class));
         ReflectionTestUtils.setField(sourceConnectorNode, "connectorFunctions", connectorFunction);
         ReflectionTestUtils.setField(sourceConnectorNode, "connectorContext", tapConnectorContext);
         ConnectorNode sourceConnectorNodeTmp = sourceConnectorNode;
         sourceConnectorNodeTmp.getConnectorFunctions().supportBatchCount(null);
         InspectTaskContext inspectTaskContext = new InspectTaskContext("test", null, inspectTask,
                 source, target, null, null, null, null, sourceConnectorNodeTmp, null, clientMongoOperator, null);
         TableRowCountInspectJob tableRowContentInspectJob = new TableRowCountInspectJob(inspectTaskContext);
         ReflectionTestUtils.setField(tableRowContentInspectJob, "progressUpdateCallback", Mockito.mock(ProgressUpdate.class));

         ReflectionTestUtils.invokeMethod(tableRowContentInspectJob, "doRun");
         InspectResultStats inspectResultStats = (InspectResultStats) ReflectionTestUtils.getField(tableRowContentInspectJob, "stats");

         Assertions.assertTrue(inspectResultStats.getErrorMsg().contains("Source node does not support batch count function"));


     }

     @Test
     void testTargetBatchFunctionException() {
         // input param
         InspectDataSource inspectSource = new InspectDataSource();
         InspectDataSource inspectTarget = new InspectDataSource();
         inspectTask.setSource(inspectSource);
         inspectTask.setTarget(inspectTarget);


         connectorFunction.supportBatchCount(Mockito.mock(BatchCountFunction.class));

         ReflectionTestUtils.setField(sourceConnectorNode, "connectorFunctions", connectorFunction);

         TapConnectorContext tapConnectorContext = new TapConnectorContext(tapNodeSpecification, new DataMap(), new DataMap(), Mockito.mock(Log.class));
         ReflectionTestUtils.setField(sourceConnectorNode, "connectorFunctions", connectorFunction);
         ReflectionTestUtils.setField(sourceConnectorNode, "connectorContext", tapConnectorContext);

         ReflectionTestUtils.setField(targetConnectorNode, "connectorFunctions", new ConnectorFunctions());
         ReflectionTestUtils.setField(targetConnectorNode, "connectorContext", tapConnectorContext);

         InspectTaskContext inspectTaskContext = new InspectTaskContext("test", null, inspectTask,
                 source, target, null, null, null, null, sourceConnectorNode, targetConnectorNode, clientMongoOperator, null);
         TableRowCountInspectJob tableRowContentInspectJob = new TableRowCountInspectJob(inspectTaskContext);

         ReflectionTestUtils.invokeMethod(tableRowContentInspectJob, "doRun");
         InspectResultStats inspectResultStats = (InspectResultStats) ReflectionTestUtils.getField(tableRowContentInspectJob, "stats");

         assert inspectResultStats != null;
         Assertions.assertTrue(inspectResultStats.getErrorMsg().contains("Target node does not support batch count function"));


     }

     @Test
     void testSetCommandCountParamException() {
         try {
             TableRowCountInspectJob.setCommandCountParam(new HashMap<>(), new ConnectorNode(), new TapTable());
         } catch (TapCodeException tapCodeException) {
             Assertions.assertEquals(TaskInspectExCode_27.COMMAND_COUNT_PARAM_ERROR, tapCodeException.getCode());
         }
     }

    public void initAutomaticCheck() {
        // input param
        InspectDataSource inspectSource = new InspectDataSource();
        InspectDataSource inspectTarget = new InspectDataSource();
        inspectTask.setSource(inspectSource);
        inspectTask.setTarget(inspectTarget);


        connectorFunction.supportBatchCount(Mockito.mock(BatchCountFunction.class));

        ReflectionTestUtils.setField(sourceConnectorNode, "connectorFunctions", connectorFunction);

        TapConnectorContext tapConnectorContext = new TapConnectorContext(tapNodeSpecification, new DataMap(), new DataMap(), Mockito.mock(Log.class));
        ReflectionTestUtils.setField(sourceConnectorNode, "connectorFunctions", connectorFunction);
        ReflectionTestUtils.setField(sourceConnectorNode, "connectorContext", tapConnectorContext);

        ReflectionTestUtils.setField(targetConnectorNode, "connectorFunctions", new ConnectorFunctions());
        ReflectionTestUtils.setField(targetConnectorNode, "connectorContext", tapConnectorContext);
        InspectTaskContext inspectTaskContext = new InspectTaskContext("test", null, inspectTask,
                source, target, null, null, null, null, sourceConnectorNode, targetConnectorNode, clientMongoOperator, null);
        TableRowContentInspectJob tableRowContentInspectJob = new TableRowContentInspectJob(inspectTaskContext);

    }

    @Test
    void testHandleCurrent(){
        initAutomaticCheck();
        ClientMongoOperator clientMongoOperatorTmp = mock(ClientMongoOperator.class);
        InspectServiceImpl inspectService = mock(InspectServiceImpl.class);
        InspectTaskContext inspectTaskContext = new InspectTaskContext("test", null, inspectTask,
                source, target, null, null, null, null, sourceConnectorNode, targetConnectorNode, clientMongoOperatorTmp, inspectService);
        TableRowContentInspectJob tableRowContentInspectJob = spy(new TableRowContentInspectJob(inspectTaskContext));
        String id = "66616f30c7a14d46120ef0b1";
        InspectResult excepted = new InspectResult();
        excepted.setId(id);
        doNothing().when(inspectService).upsertInspectResult(excepted, false);
        ReflectionTestUtils.setField(tableRowContentInspectJob,"current",20000);

        TableRowContentInspectJob.CompareProgress compareProgress = mock(TableRowContentInspectJob.CompareProgress.class);
        tableRowContentInspectJob.handleCurrent(1,1,compareProgress,new InspectResultStats());

        ReflectionTestUtils.setField(tableRowContentInspectJob,"current",80);
        TableRowContentInspectJob.CompareProgress compareProgress1 = mock(TableRowContentInspectJob.CompareProgress.class);

        tableRowContentInspectJob.handleCurrent(1,1,compareProgress1,new InspectResultStats());

        ReflectionTestUtils.setField(tableRowContentInspectJob,"current",10000);
        TableRowContentInspectJob.CompareProgress compareProgress2 = mock(TableRowContentInspectJob.CompareProgress.class);
        tableRowContentInspectJob.handleCurrent(1,1,compareProgress2,new InspectResultStats());

        verify(compareProgress, times(1)).update(any(),any());
    }


    @Test
    void testInitData() {
        initAutomaticCheck();
        ClientMongoOperator clientMongoOperatorTmp = mock(ClientMongoOperator.class);
        InspectServiceImpl inspectService = mock(InspectServiceImpl.class);
        Connections source = mock(Connections.class);
        Connections target = mock(Connections.class);
        InspectTaskContext inspectTaskContext = new InspectTaskContext("test", null, inspectTask,
                source, target, null, null, null, null, sourceConnectorNode, targetConnectorNode, clientMongoOperatorTmp, inspectService);
        TableRowContentInspectJob tableRowContentInspectJob = spy(new TableRowContentInspectJob(inspectTaskContext));
        String id = "66616f30c7a14d46120ef0b1";
        InspectResult excepted = new InspectResult();
        excepted.setId(id);
        doNothing().when(inspectService).upsertInspectResult(excepted, false);
        Logger logger = mock(Logger.class);
        ReflectionTestUtils.setField(tableRowContentInspectJob, "logger", logger);

        when(logger.isDebugEnabled()).thenReturn(true);
        doNothing().when(logger).debug(Mockito.any(Message.class));
        Object actualData = tableRowContentInspectJob.initData(true,false,null);
        Assertions.assertTrue(actualData != null);
    }

    @Test
    void testInitDataWithColumns() {
        initAutomaticCheck();
        ClientMongoOperator clientMongoOperatorTmp = mock(ClientMongoOperator.class);
        InspectServiceImpl inspectService = mock(InspectServiceImpl.class);
        InspectTask inspectTask = new InspectTask();
        InspectDataSource inspectSource = new InspectDataSource();
        InspectDataSource inspectTarget = new InspectDataSource();
        inspectTask.setSource(inspectSource);
        List<String> list = new ArrayList();
        list.add("id");
        inspectSource.setColumns(list);
        inspectTarget.setColumns(list);
        inspectTask.setTarget(inspectTarget);
        inspectTask.setBatchSize(0);
        InspectTaskContext inspectTaskContext = new InspectTaskContext("test", null, inspectTask,
                source, target, null, null, null, null, sourceConnectorNode, targetConnectorNode, clientMongoOperatorTmp, inspectService);
        TableRowContentInspectJob tableRowContentInspectJob = spy(new TableRowContentInspectJob(inspectTaskContext));
        String id = "66616f30c7a14d46120ef0b1";
        InspectResult excepted = new InspectResult();
        excepted.setId(id);
        doNothing().when(inspectService).upsertInspectResult(excepted, false);
        Logger logger = mock(Logger.class);
        ReflectionTestUtils.setField(tableRowContentInspectJob, "logger", logger);

        when(logger.isDebugEnabled()).thenReturn(true);

        Object actualData = tableRowContentInspectJob.initData(true,false,null);
        Assertions.assertTrue(actualData != null);
    }

    @Test
    void testHandleCompareRecord() {
        initAutomaticCheck();
        ClientMongoOperator clientMongoOperatorTmp = mock(ClientMongoOperator.class);
        InspectServiceImpl inspectService = mock(InspectServiceImpl.class);
        InspectTaskContext inspectTaskContext = new InspectTaskContext("test", null, inspectTask,
                source, target, null, "all", null, null, sourceConnectorNode, targetConnectorNode, clientMongoOperatorTmp, inspectService);
        TableRowContentInspectJob tableRowContentInspectJob = spy(new TableRowContentInspectJob(inspectTaskContext));
        String id = "66616f30c7a14d46120ef0b1";

        ReflectionTestUtils.setField(tableRowContentInspectJob, "uniqueFieldLimit", 100L);
        ReflectionTestUtils.setField(tableRowContentInspectJob, "otherFieldLimit", 100L);


        Object[] sourceKeyArr = new Object[2];
        sourceKeyArr[1] = "id";
        ReflectionTestUtils.setField(tableRowContentInspectJob, "sourceHasNext", true);

        ReflectionTestUtils.setField(tableRowContentInspectJob, "targetHasNext", false);
        List<String> sourceKeys = new ArrayList<>();
        sourceKeys.add("id");
        ReflectionTestUtils.setField(tableRowContentInspectJob, "sourceKeys", sourceKeys);
        ReflectionTestUtils.setField(tableRowContentInspectJob, "targetKeys", sourceKeys);
        CompareFunction<Map<String, Object>, String> compareFn = new DefaultCompare();
        Object actualData = tableRowContentInspectJob.handleCompareRecord(new HashMap<>(),new HashMap<>(),
                true,compareFn,new ArrayList<>(),mock(BaseResult.class),mock(BaseResult.class),false,null);
        Assertions.assertTrue(actualData != null);

        Map<String, Object> sourceRecord = new HashMap<>();
        sourceRecord.put("id",1);
        Object actualData1 = tableRowContentInspectJob.handleCompareRecord(sourceRecord,new HashMap<>(),
                true,null,new ArrayList<>(),mock(BaseResult.class),mock(BaseResult.class),false,null);
        Assertions.assertTrue(actualData1 != null);

        ReflectionTestUtils.setField(tableRowContentInspectJob, "sourceHasNext", false);
        ReflectionTestUtils.setField(tableRowContentInspectJob, "targetHasNext", true);
        Map<String, Object> targetRecord = new HashMap<>();
        targetRecord.put("id",1);
        Object actualData2 = tableRowContentInspectJob.handleCompareRecord(new HashMap<>(),targetRecord,
               true,null,new ArrayList<>(),mock(BaseResult.class),mock(BaseResult.class),false,null);
        Assertions.assertTrue(actualData2 != null);

        Object actualData3 = tableRowContentInspectJob.handleCompareRecord(new HashMap<>(),targetRecord,
                true,compareFn,new ArrayList<>(),mock(BaseResult.class),mock(BaseResult.class),false,null);
        Assertions.assertTrue(actualData3 != null);
    }

    @Test
    void testHandleCompareRecordDateTime() {
        initAutomaticCheck();
        ClientMongoOperator clientMongoOperatorTmp = mock(ClientMongoOperator.class);
        InspectServiceImpl inspectService = mock(InspectServiceImpl.class);
        InspectTaskContext inspectTaskContext = new InspectTaskContext("test", null, inspectTask,
                source, target, null, "all", null, null, sourceConnectorNode, targetConnectorNode, clientMongoOperatorTmp, inspectService);
        TableRowContentInspectJob tableRowContentInspectJob = spy(new TableRowContentInspectJob(inspectTaskContext));

        ReflectionTestUtils.setField(tableRowContentInspectJob, "uniqueFieldLimit", 100L);
        ReflectionTestUtils.setField(tableRowContentInspectJob, "otherFieldLimit", 100L);
        ReflectionTestUtils.setField(tableRowContentInspectJob, "sourceHasNext", false);
        ReflectionTestUtils.setField(tableRowContentInspectJob, "targetHasNext", false);
        List<String> sourceKeys = new ArrayList<>();
        sourceKeys.add("dateTime");
        ReflectionTestUtils.setField(tableRowContentInspectJob, "sourceKeys", sourceKeys);
        ReflectionTestUtils.setField(tableRowContentInspectJob, "targetKeys", sourceKeys);
        CompareFunction<Map<String, Object>, String> compareFn = new DefaultCompare();
        Map<String, Object> sourceRecord = new HashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSSSSS");
        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSS");
        DateTime vl1 = new DateTime(LocalDateTime.parse("2023-05-15 14:30:25.123456789", formatter));

        DateTime vl2 =new DateTime(LocalDateTime.parse("2023-05-15 14:30:25.12346", formatter2));
        sourceRecord.put("dateTime",vl1);
        Map<String, Object> targetRecord = new HashMap<>();
        targetRecord.put("dateTime",vl2);
        Object actualData1 = tableRowContentInspectJob.handleCompareRecord(sourceRecord,targetRecord,
                false,null,new ArrayList<>(),mock(BaseResult.class),mock(BaseResult.class),false,null);
        Object actualData2 = tableRowContentInspectJob.handleCompareRecord(sourceRecord,targetRecord,
                false,null,new ArrayList<>(),mock(BaseResult.class),mock(BaseResult.class),true,null);
        Assertions.assertNotNull(actualData1);
        Assertions.assertNull(actualData2);
    }



    @Test
    void testHandleDoneInspectResultStats() {
        initAutomaticCheck();
        ClientMongoOperator clientMongoOperatorTmp = mock(ClientMongoOperator.class);
        InspectServiceImpl inspectService = mock(InspectServiceImpl.class);

        InspectTaskContext inspectTaskContext = new InspectTaskContext("test", null, inspectTask,
                source, target, null, null, null, null, sourceConnectorNode, targetConnectorNode, clientMongoOperatorTmp, inspectService);
        TableRowContentInspectJob tableRowContentInspectJob = spy(new TableRowContentInspectJob(inspectTaskContext));

        TableRowContentInspectJob.CompareProgress compareProgress = mock(TableRowContentInspectJob.CompareProgress.class);
        InspectResultStats stats = new InspectResultStats();
        stats.setResult("passed");

        tableRowContentInspectJob.handleDoneInspectResultStats(stats,compareProgress,new ArrayList<>());
        verify(compareProgress, times(1)).update(any(),any());

    }
    @Nested
    class testInitData{
         @BeforeEach
          void beforeEach() {
             initAutomaticCheck();
         }
         @Test
         void testInitDataIgnoreTimePrecisionFalse() {
             InspectTaskContext inspectTaskContext = new InspectTaskContext("test", null, inspectTask,
                     source, target, null, null, null, null, sourceConnectorNode, null, clientMongoOperator, null);
             TableRowContentInspectJob tableRowContentInspectJob = new TableRowContentInspectJob(inspectTaskContext);
             CompareFunction<Map<String, Object>, String> result = tableRowContentInspectJob.initData(true,false,null);
             Assertions.assertInstanceOf(DefaultCompare.class, result);
             DefaultCompare defaultCompare = (DefaultCompare) result;
             Assertions.assertFalse(defaultCompare.isIgnoreTimePrecision());
         }

        @Test
        void testInitDataIgnoreTimePrecisionTrue() {
            InspectTaskContext inspectTaskContext = new InspectTaskContext("test", null, inspectTask,
                    source, target, null, null, null, null, sourceConnectorNode, null, clientMongoOperator, null);
            TableRowContentInspectJob tableRowContentInspectJob = new TableRowContentInspectJob(inspectTaskContext);
            CompareFunction<Map<String, Object>, String> result = tableRowContentInspectJob.initData(true,true,null);
            Assertions.assertInstanceOf(DefaultCompare.class, result);
            DefaultCompare defaultCompare = (DefaultCompare) result;
            Assertions.assertTrue(defaultCompare.isIgnoreTimePrecision());
        }

    }
}
