package io.tapdata.inspect;

import com.tapdata.constant.ConnectorConstant;
import com.tapdata.entity.inspect.*;
import com.tapdata.exception.NonsupportMethodDifferenceInspectException;
import com.tapdata.exception.NotfoundLastResultInspectException;
import com.tapdata.mongo.ClientMongoOperator;
import com.tapdata.tm.commons.task.dto.ParentTaskDto;
import com.tapdata.tm.commons.task.dto.TaskDto;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class InspectTaskTest {

    ClientMongoOperator clientMongoOperator;
    Inspect inspect;
    InspectService inspectService;

    InspectTask inspectTask;

    @BeforeEach
    void init() {
        clientMongoOperator = mock(ClientMongoOperator.class);
        inspect = new Inspect();
        inspectService = Mockito.mock(InspectService.class);
        inspectTask = new InspectTask(inspectService, inspect, clientMongoOperator) {
            @Override
            public Runnable createTableInspectJob(InspectTaskContext inspectTaskContext) {
                return null;
            }
        };
        com.tapdata.entity.inspect.InspectTask task = new com.tapdata.entity.inspect.InspectTask();
        InspectDataSource source = new InspectDataSource();
        source.setConnectionId("6805f415b01ccb06fb5cfae9");
        task.setSource(source);
        InspectDataSource target = new InspectDataSource();
        source.setConnectionId("6802236507e3ae7f6bc626de");
        task.setTarget(target);
        inspect.setTasks(Arrays.asList(task));
    }

    @Nested
    class handleDiffInspectTest {
        @BeforeEach
        void init() {
            inspect.setInspectMethod("row_count");
        }

        @Test
        void testJudgeAutomaticCheck() {
            TaskDto taskDto = new TaskDto();
            when(clientMongoOperator.findOne(Mockito.any(Query.class), any(), any())).thenReturn(taskDto);
            // test mode不为cron
            ReflectionTestUtils.setField(inspect, "mode", "manual");
            when(clientMongoOperator.findOne(Mockito.any(Query.class), any(), any())).thenReturn(taskDto);
            boolean actualDataFlowId = inspectTask.judgeAutomaticCheck(new InspectResult());
            Assert.assertFalse(actualDataFlowId);

            // flowId = "66618104c016c632a6612325"
            ReflectionTestUtils.setField(inspect, "flowId", "66618104c016c632a6612325");
            when(clientMongoOperator.findOne(Mockito.any(Query.class), any(), any())).thenReturn(taskDto);
            boolean actualData1 = inspectTask.judgeAutomaticCheck(new InspectResult());
            Assert.assertFalse(actualData1);
          }

        @Test
        void testJudgeAutomaticCheckWithTimes() {
            TaskDto taskDto = new TaskDto();
            inspect.setMode("manual");
            inspect.setFlowId("66618104c016c632a6612325");
            inspect.setByFirstCheckId("66618104c016c632a6612326");
            // test mode不为cron
            boolean actualDataFlowId = inspectTask.judgeAutomaticCheck(new InspectResult());
            Assert.assertFalse(actualDataFlowId);

            inspect.setByFirstCheckId(null);
            when(clientMongoOperator.findOne(Mockito.any(Query.class), any(), any())).thenReturn(taskDto);
            boolean actualData1 = inspectTask.judgeAutomaticCheck(new InspectResult());
            Assert.assertFalse(actualData1);
        }

        @Test
        void testJudgeAutomaticCheckWithRun() {
            inspect.setMode("manual");
            inspect.setFlowId("66618104c016c632a6612325");
            inspect.setByFirstCheckId("66618104c016c632a6612326");
            // test mode不为cron
            boolean actualDataFlowId = inspectTask.judgeAutomaticCheck(new InspectResult());
            Assert.assertFalse(actualDataFlowId);

            inspect.setByFirstCheckId(null);
            ReflectionTestUtils.setField(inspectTask, "syncTaskStatus", TaskDto.STATUS_RUNNING);
            boolean actualData1 = inspectTask.judgeAutomaticCheck(new InspectResult());
            Assert.assertFalse(actualData1);
        }

        @Test
        void testJudgeAutomaticCheckWithStatus() {
            inspect.setMode("manual");
            inspect.setFlowId("66618104c016c632a6612325");
            inspect.setByFirstCheckId("66618104c016c632a6612326");
            // test mode不为cron
            boolean actualDataFlowId = inspectTask.judgeAutomaticCheck(new InspectResult());
            Assert.assertFalse(actualDataFlowId);

            inspect.setByFirstCheckId(null);
            ReflectionTestUtils.setField(inspectTask, "syncTaskStatus", TaskDto.STATUS_RUNNING);
            ReflectionTestUtils.setField(inspectTask, "syncTaskType", ParentTaskDto.TYPE_INITIAL_SYNC_CDC);
            InspectService inspectService = mock(InspectServiceImpl.class);
            InspectResult inspectResult = new InspectResult();
            List<InspectResultStats> stats = new ArrayList<>();
            InspectResultStats inspectResultStats = new InspectResultStats();
            inspectResultStats.setStatus(InspectStatus.DONE.getCode());
            inspectResultStats.setResult("failed");
            stats.add(inspectResultStats);
            inspectResult.setStats(stats);

            ReflectionTestUtils.setField(inspectTask, "inspectService", inspectService);
            boolean actualData1 = inspectTask.judgeAutomaticCheck(inspectResult);
            Assert.assertFalse(actualData1);

            ReflectionTestUtils.setField(inspectTask, "syncTaskType", ParentTaskDto.TYPE_CDC);
            boolean actualData2 = inspectTask.judgeAutomaticCheck(inspectResult);

            Assert.assertFalse(actualData2);
        }

        @Test
        void testJudgeAutomaticCheckWithStatusField() {
            inspect.setInspectMethod("field");
            inspect.setMode("manual");
            inspect.setFlowId("66618104c016c632a6612325");
            inspect.setByFirstCheckId("66618104c016c632a6612326");
            // test mode不为cron
            boolean actualDataFlowId = inspectTask.judgeAutomaticCheck(new InspectResult());
            Assert.assertFalse(actualDataFlowId);
        }

        @Test
        void testJudgeAutomaticCheckWithStatusJointField() {
            inspect.setInspectMethod("jointField");
            inspect.setMode("manual");
            inspect.setFlowId("66618104c016c632a6612325");
            inspect.setByFirstCheckId("66618104c016c632a6612326");
            // test mode不为cron
            boolean actualDataFlowId = inspectTask.judgeAutomaticCheck(new InspectResult());
            Assert.assertFalse(actualDataFlowId);
        }

        @Test
        void testJudgeAutomaticCheckWithMongo3() {
            when(inspectService.getConnectionDatabaseType("6805f415b01ccb06fb5cfae9")).thenReturn("MongoDB");
            when(inspectService.getConnectionDatabaseType("6802236507e3ae7f6bc626de")).thenReturn("MongoDB Below 3.4");
            boolean actualDataFlowId = inspectTask.judgeAutomaticCheck(new InspectResult());
            Assert.assertFalse(actualDataFlowId);
        }

    }

    @Nested
    class judgeAutomaticCheckTest {

        InspectResult inspectResult;
        InspectLimit limit;

        @BeforeEach
        void setUp() {
            limit = new InspectLimit();
            limit.setKeep(100);

            ReflectionTestUtils.setField(inspectTask, "syncTaskStatus", TaskDto.STATUS_RUNNING);
            ReflectionTestUtils.setField(inspectTask, "syncTaskType", ParentTaskDto.TYPE_INITIAL_SYNC_CDC);

            inspect.setInspectMethod("field");
            inspect.setMode("manual");
            inspect.setFlowId("66618104c016c632a6612325");
            inspect.setByFirstCheckId("66618104c016c632a6612326");
            inspect.setDiffInspectTimes(2);
            inspect.setLimit(limit);

            inspectResult = new InspectResult();
            inspectResult.setInspect(inspect);
            inspectResult.setStats(Optional.of(new ArrayList<InspectResultStats>()).map(statsList -> {
                statsList.add(Optional.of(new InspectResultStats()).map(stats -> {
                    stats.setResult("failed");
                    stats.setStatus(InspectStatus.DONE.getCode());
                    stats.setRow_failed(1);
                    return stats;
                }).get());
                return statsList;
            }).get());
        }

        @Test
        void testTrue() {
            Assert.assertTrue(inspectTask.judgeAutomaticCheck(inspectResult));
        }

        @Test
        void testOutOffLimit() {
            inspectResult.getStats().add(Optional.of(new InspectResultStats()).map(stats -> {
                stats.setRow_failed(100);
                return stats;
            }).get());

            Assert.assertFalse(inspectTask.judgeAutomaticCheck(inspectResult));
        }
    }

    @Nested
    class JudgeJobStatusTest {
        @Test
        void testJudgeJobStatusError() {

            InspectService inspectService = mock(InspectServiceImpl.class);
            InspectResult inspectResult = new InspectResult();
            List<InspectResultStats> stats = new ArrayList<>();
            InspectResultStats inspectResultStats = new InspectResultStats();
            inspectResultStats.setStatus(InspectStatus.ERROR.getCode());
            stats.add(inspectResultStats);
            inspectResult.setStats(stats);
            ReflectionTestUtils.setField(inspectTask, "inspectService", inspectService);
            boolean actualData = inspectTask.judgeJobStatus(inspectResult);
            Assert.assertFalse(actualData);
        }

        @Test
        void testJudgeJobStatusFail() {
            InspectService inspectService = mock(InspectServiceImpl.class);
            InspectResult inspectResult = new InspectResult();
            List<InspectResultStats> stats = new ArrayList<>();
            InspectResultStats inspectResultStats = new InspectResultStats();
            inspectResultStats.setStatus(InspectStatus.DONE.getCode());
            inspectResultStats.setResult("failed");
            stats.add(inspectResultStats);
            inspectResult.setStats(stats);

            ReflectionTestUtils.setField(inspectTask, "inspectService", inspectService);
            boolean actualData = inspectTask.judgeJobStatus(inspectResult);
            Assert.assertTrue(actualData);
        }

        @Test
        void testJudgeJobStatusPass() {
            InspectService inspectService = mock(InspectServiceImpl.class);
            InspectResult inspectResult = new InspectResult();
            List<InspectResultStats> stats = new ArrayList<>();
            InspectResultStats inspectResultStats = new InspectResultStats();
            inspectResultStats.setStatus(InspectStatus.DONE.getCode());
            inspectResultStats.setResult("passed");
            stats.add(inspectResultStats);
            inspectResult.setStats(stats);

            ReflectionTestUtils.setField(inspectTask, "inspectService", inspectService);
            boolean actualData = inspectTask.judgeJobStatus(inspectResult);
            Assert.assertFalse(actualData);
        }
    }

    @Nested
    class InitTaskDelayAndStatusTest {
        @Test
        void initTaskDelayAndStatusFlowIdIsNull() {
            InspectService inspectService = mock(InspectServiceImpl.class);
            TaskDto task = new TaskDto();
            ReflectionTestUtils.setField(inspectTask, "inspectService", inspectService);

            when(clientMongoOperator.findOne(
                    new Query(Criteria.where("_id").is(inspect.getFlowId())), ConnectorConstant.TASK_COLLECTION, TaskDto.class)).
                    thenReturn(task);
            inspectTask.initTaskDelayAndStatus();
            Assert.assertTrue(ReflectionTestUtils.getField(inspectTask, "syncTaskStatus") == null);
        }

        @Test
        void initTaskDelayAndStatusFlowNotNull() {
            TaskDto task = new TaskDto();
            ReflectionTestUtils.setField(inspect, "flowId", "66618104c016c632a6612325");
            String status = "running";
            task.setStatus(status);
            when(clientMongoOperator.findOne(
                    new Query(Criteria.where("_id").is(inspect.getFlowId())), ConnectorConstant.TASK_COLLECTION, TaskDto.class)).
                    thenReturn(task);
            inspectTask.initTaskDelayAndStatus();
            Assert.assertTrue(ReflectionTestUtils.getField(inspectTask, "syncTaskStatus").equals(status));
        }

        @Test
        void initTaskDelayAndStatusTaskIsNull() {
            when(clientMongoOperator.findOne(
                    new Query(Criteria.where("_id").is(inspect.getFlowId())), ConnectorConstant.TASK_COLLECTION, TaskDto.class)).
                    thenReturn(null);
            inspectTask.initTaskDelayAndStatus();
            Assert.assertTrue(ReflectionTestUtils.getField(inspectTask, "syncTaskStatus") == null);
        }
    }

    @Nested
    class InitDifferenceInspectTest {
        @Test
        void testNonsupportDifferenceInspect() {
            InspectResult inspectResult = mock(InspectResult.class);

            String firstCheckId = "test-first-check-id";
            inspect.setByFirstCheckId(firstCheckId);

            for (InspectMethod method : InspectMethod.values()) {
                inspect.setInspectMethod(method.getCode());
                switch (method) {
                    case FIELD:
                    case JOINTFIELD:
                        Assertions.assertThrows(NonsupportMethodDifferenceInspectException.class, () -> inspectTask.initDifferenceInspect(inspectResult, firstCheckId));
                        break;
                    default:
                        Assertions.assertThrows(NotfoundLastResultInspectException.class, () -> inspectTask.initDifferenceInspect(inspectResult, firstCheckId));
                        break;
                }
            }
        }

        @Test
        void testSuccess() {
            String inspectResultId = "test-inspect-result-id";
            String firstCheckId = "test-first-check-id";
            String lastInspectResultId = "lastInspectResultId";

            inspect.setByFirstCheckId(firstCheckId);
            inspect.setInspectResultId(inspectResultId);
            inspect.setInspectMethod(InspectMethod.FIELD.getCode());
            inspect.setTasks(new ArrayList<>());

            Inspect lastInspect = new Inspect();
            lastInspect.setTasks(new ArrayList<>());
            Optional.of(new com.tapdata.entity.inspect.InspectTask()).ifPresent(task -> {
                task.setId("test-task-id1");
                task.setSource(mock(InspectDataSource.class));
                task.setTarget(mock(InspectDataSource.class));
                lastInspect.getTasks().add(task);
                inspect.getTasks().add(task);
            });

            InspectResultStats lastInspectResultStats = new InspectResultStats();

            InspectResult lastInspectResult = new InspectResult();
            lastInspectResult.setInspect(lastInspect);
            lastInspectResult.setStats(new ArrayList<>());
            lastInspectResult.getStats().add(lastInspectResultStats);
            lastInspectResult.setId(lastInspectResultId);
            lastInspectResult.setFirstTargetTotal(1);
            lastInspectResult.setFirstTargetTotal(1);

            when(inspectService.getLastDifferenceInspectResult(any(), any())).thenReturn(lastInspectResult);


            InspectResult diffInspectResult = new InspectResult();
            diffInspectResult.setStats(new ArrayList<>());
            inspectTask.initDifferenceInspect(diffInspectResult, firstCheckId);

            Assertions.assertNull(diffInspectResult.getId());
            Assertions.assertEquals(firstCheckId, diffInspectResult.getFirstCheckId());
            Assertions.assertEquals(lastInspectResultId, diffInspectResult.getParentId());
        }

        @Test
        void testLastPassed() {
            String inspectResultId = "test-inspect-result-id";
            String firstCheckId = "test-first-check-id";
            String lastInspectResultId = "lastInspectResultId";

            inspect.setByFirstCheckId(firstCheckId);
            inspect.setInspectResultId(inspectResultId);
            inspect.setInspectMethod(InspectMethod.FIELD.getCode());
            inspect.setTasks(new ArrayList<>());

            Inspect lastInspect = new Inspect();
            lastInspect.setTasks(new ArrayList<>());
            com.tapdata.entity.inspect.InspectTask task = new com.tapdata.entity.inspect.InspectTask();
            task.setTaskId("test-task-id1");
            task.setSource(mock(InspectDataSource.class));
            task.setTarget(mock(InspectDataSource.class));
            lastInspect.getTasks().add(task);
            inspect.getTasks().add(task);

            com.tapdata.entity.inspect.InspectTask task1 = new com.tapdata.entity.inspect.InspectTask();
            task1.setTaskId("test-task-id2");
            task1.setSource(mock(InspectDataSource.class));
            task1.setTarget(mock(InspectDataSource.class));
            lastInspect.getTasks().add(task1);
            inspect.getTasks().add(task1);

            InspectResultStats lastInspectResultStats = new InspectResultStats();
            InspectResultStats lastInspectResultStats1 = new InspectResultStats();

            InspectResult lastInspectResult = new InspectResult();
            lastInspectResultStats.setResult("passed");
            lastInspectResultStats.setTaskId("test-task-id1");
            lastInspectResultStats1.setResult("failed");
            lastInspectResultStats1.setTaskId("test-task-id2");
            lastInspectResult.setInspect(lastInspect);
            lastInspectResult.setStats(new ArrayList<>());
            lastInspectResult.getStats().add(lastInspectResultStats);
            lastInspectResult.getStats().add(lastInspectResultStats1);
            lastInspectResult.setId(lastInspectResultId);
            lastInspectResult.setFirstTargetTotal(1);
            lastInspectResult.setFirstTargetTotal(1);

            when(inspectService.getLastDifferenceInspectResult(any(), any())).thenReturn(lastInspectResult);


            InspectResult diffInspectResult = new InspectResult();
            List<InspectResultStats> stats = new ArrayList<>();
            InspectResultStats inspectResultStats = new InspectResultStats();
            inspectResultStats.setTaskId("test-task-id1");
            stats.add(inspectResultStats);
            diffInspectResult.setStats(stats);
            inspectTask.initDifferenceInspect(diffInspectResult, firstCheckId);

            Assertions.assertNull(diffInspectResult.getId());
            Assertions.assertEquals(firstCheckId, diffInspectResult.getFirstCheckId());
            Assertions.assertEquals(lastInspectResultId, diffInspectResult.getParentId());
            Assertions.assertEquals(1, diffInspectResult.getInspect().getTasks().size());
            Assertions.assertTrue(diffInspectResult.getStats().contains(lastInspectResultStats));
        }
    }

    @Nested
    class runFinallyTest {
        @Test
        void runFinallyNoDiffInspect() {
            InspectService inspectService = mock(InspectServiceImpl.class);
            TaskDto task = new TaskDto();
            ReflectionTestUtils.setField(inspectTask, "inspectService", inspectService);

            when(clientMongoOperator.findOne(
                    new Query(Criteria.where("_id").is(inspect.getFlowId())), ConnectorConstant.TASK_COLLECTION, TaskDto.class)).
                    thenReturn(task);
            inspectTask.run();
            Assert.assertTrue(ReflectionTestUtils.getField(inspectTask, "syncTaskStatus") == null);
        }

        @Test
        void runFinallyDiffInspect() {
            ClientMongoOperator clientMongoOperator = mock(ClientMongoOperator.class);
            Inspect inspect = new Inspect();
            InspectService inspectService = Mockito.mock(InspectService.class);
            InspectTask inspectTask = spy(new InspectTask(inspectService, inspect, clientMongoOperator) {
                @Override
                public Runnable createTableInspectJob(InspectTaskContext inspectTaskContext) {
                    return null;
                }
            });
            doReturn(true).when(inspectTask).judgeAutomaticCheck(any());
            inspectTask.run();
            Assert.assertTrue(ReflectionTestUtils.getField(inspectTask, "syncTaskStatus") == null);
        }


    }

}
