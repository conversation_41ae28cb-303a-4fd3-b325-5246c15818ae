2025-06-06 16:33:34.746 [tm] [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-06 16:33:34.810 [tm] [main] INFO  com.tapdata.tm.TMApplication - Starting TMApplication using Java 17.0.14 with PID 15062 (/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes started by samuel in /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17)
2025-06-06 16:33:34.811 [tm] [main] INFO  com.tapdata.tm.TMApplication - The following 2 profiles are active: "idaas", "default"
2025-06-06 16:33:35.916 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:33:35.916 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-06 16:33:36.207 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 286 ms. Found 2 MongoDB repository interfaces.
2025-06-06 16:33:36.208 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:33:36.209 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-06 16:33:36.212 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 MongoDB repository interfaces.
2025-06-06 16:33:36.212 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:33:36.212 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-06 16:33:36.214 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 MongoDB repository interfaces.
2025-06-06 16:33:36.223 [tm] [main] INFO  c.t.t.w.c.WebSocketHandlerImportBeanRegistrar - The number of webSocketHandler beans successfully registered: 17
2025-06-06 16:33:36.616 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:33:36.616 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 16:33:36.673 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.task.repository.TaskDagCheckLogRepository; If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository
2025-06-06 16:33:36.674 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.Settings.repository.SettingsRepository; If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository
2025-06-06 16:33:36.674 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 56 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 16:33:36.677 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:33:36.677 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 16:33:36.726 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.task.repository.TaskDagCheckLogRepository; If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository
2025-06-06 16:33:36.726 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.Settings.repository.SettingsRepository; If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository
2025-06-06 16:33:36.726 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 47 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 16:33:37.551 [tm] [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 3000 (http)
2025-06-06 16:33:37.558 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - The ["http-nio-3000"] connector has been configured to support HTTP upgrade to [h2c]
2025-06-06 16:33:37.558 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-3000"]
2025-06-06 16:33:37.560 [tm] [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 16:33:37.560 [tm] [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.40]
2025-06-06 16:33:37.602 [tm] [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 16:33:37.602 [tm] [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2748 ms
2025-06-06 16:33:37.814 [tm] [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-data", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@282aea3c, com.mongodb.Jep395RecordCodecProvider@54a5799f, com.mongodb.KotlinCodecProvider@162e29a1]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 16:33:37.831 [tm] [cluster-ClusterId{value='6842a7e1dd84083979ffc8d0', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=14219917, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 16:33:33 CST 2025, lastUpdateTimeNanos=194445788052666}
2025-06-06 16:33:39.830 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@282aea3c, com.mongodb.Jep395RecordCodecProvider@54a5799f, com.mongodb.KotlinCodecProvider@162e29a1]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 16:33:39.833 [tm] [cluster-ClusterId{value='6842a7e3dd84083979ffc8d1', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=3706792, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 16:33:33 CST 2025, lastUpdateTimeNanos=194447792114750}
2025-06-06 16:33:40.230 [tm] [main] INFO  c.t.t.c.component.ProductComponent - Current product type settings is DAAS.
2025-06-06 16:33:40.243 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@282aea3c, com.mongodb.Jep395RecordCodecProvider@54a5799f, com.mongodb.KotlinCodecProvider@162e29a1]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 16:33:40.247 [tm] [cluster-ClusterId{value='6842a7e4dd84083979ffc8d2', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=3841250, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 16:33:33 CST 2025, lastUpdateTimeNanos=194448205732625}
2025-06-06 16:33:40.247 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.cluster - Waiting for server to become available for operation with ID 13. Remaining time: 29998 ms. Selector: ReadPreferenceServerSelector{readPreference=primary}, topology description: {type=UNKNOWN, servers=[{address=localhost:27017, type=UNKNOWN, state=CONNECTING}].
2025-06-06 16:33:42.218 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-06-06 16:33:42.227 [tm] [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-06 16:33:42.228 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v2.5.0 created.
2025-06-06 16:33:42.228 [tm] [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-06-06 16:33:42.229 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-06 16:33:42.229 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-06 16:33:42.229 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.5.0
2025-06-06 16:33:42.229 [tm] [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7c5ae5c3
2025-06-06 16:33:42.677 [tm] [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name userServiceImpl
2025-06-06 16:33:43.333 [tm] [main] WARN  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Multiple @RequestMapping annotations found on public com.tapdata.tm.base.dto.ResponseMessage com.tapdata.tm.message.controller.MessageController.read(java.lang.String,java.lang.String) throws java.io.IOException, but only the first will be used: [@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={}, name="", params={}, path={}, produces={}, value={}), @org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name="", params={}, path={}, produces={}, value={})]
2025-06-06 16:33:44.534 [tm] [main] WARN  o.s.d.e.c.m.SimpleElasticsearchPersistentProperty - Unsupported type 'class java.lang.Object' for date property 'date'.
2025-06-06 16:33:44.536 [tm] [main] WARN  o.s.d.e.c.m.SimpleElasticsearchPersistentProperty - Unsupported type 'class java.lang.Object' for date property 'millis'.
2025-06-06 16:33:44.664 [tm] [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoints beneath base path '/actuator'
2025-06-06 16:33:44.821 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-3000"]
2025-06-06 16:33:44.830 [tm] [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 3000 (http) with context path '/'
2025-06-06 16:33:44.831 [tm] [main] INFO  o.s.s.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-06-06 16:33:44.831 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-06 16:33:44.850 [tm] [main] INFO  c.t.t.s.u.StateMachineProcessorManager - Urls path: /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/com/tapdata/tm/statemachine/processor, protocol: file
2025-06-06 16:33:44.867 [tm] [main] INFO  com.tapdata.tm.TMApplication - Started TMApplication in 10.437 seconds (process running for 11.248)
2025-06-06 16:33:44.951 [tm] [main] INFO  com.tapdata.tm.init.PatchesRunner - Not have any DAAS patches, current version is 4.0-1
2025-06-06 16:33:45.013 [tm] [main] INFO  com.tapdata.tm.init.InitLogMap - 
  _______              _       _           _                    _             _                                _      _           _ 
 |__   __|            | |     | |         | |                  | |           | |                              | |    | |         | |
    | | __ _ _ __   __| | __ _| |_ __ _   | |_ _ __ ___     ___| |_ __ _ _ __| |_     ___ ___  _ __ ___  _ __ | | ___| |_ ___  __| |
    | |/ _` | '_ \ / _` |/ _` | __/ _` |  | __| '_ ` _ \   / __| __/ _` | '__| __|   / __/ _ \| '_ ` _ \| '_ \| |/ _ \ __/ _ \/ _` |
    | | (_| | |_) | (_| | (_| | || (_| |  | |_| | | | | |  \__ \ || (_| | |  | |_   | (_| (_) | | | | | | |_) | |  __/ ||  __/ (_| |
    |_|\__,_| .__/ \__,_|\__,_|\__\__,_|   \__|_| |_| |_|  |___/\__\__,_|_|   \__|   \___\___/|_| |_| |_| .__/|_|\___|\__\___|\__,_|
            | |                                                                                         | |                         
            |_|                                                                                         |_|                         
2025-06-06 16:33:45.015 [tm] [Thread-1] INFO  com.tapdata.tm.TMApplication - No need to start TCM
2025-06-06 16:33:45.019 [tm] [main] INFO  com.tapdata.tm.TMApplication - admin access code is 3324cfdf-7d3e-4792-bd32-571638d4562f
2025-06-06 16:33:45.175 [tm] [main] INFO  org.reflections.Reflections - Reflections took 143 ms to scan 19 urls, producing 62 keys and 1137 values
2025-06-06 16:33:45.281 [tm] [main] INFO  org.reflections.Reflections - Reflections took 14 ms to scan 12 urls, producing 15 keys and 149 values
2025-06-06 16:33:45.284 [tm] [main] INFO  org.reflections.Reflections - Reflections took 2 ms to scan 1 urls, producing 10 keys and 24 values
2025-06-06 16:33:45.286 [tm] [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@282aea3c, com.mongodb.Jep395RecordCodecProvider@54a5799f, com.mongodb.KotlinCodecProvider@162e29a1]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 16:33:45.290 [tm] [cluster-ClusterId{value='6842a7e9dd84083979ffc8d3', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=3189833, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 16:33:44 CST 2025, lastUpdateTimeNanos=194453248425416}
2025-06-06 16:33:45.372 [tm] [main] INFO  org.reflections.Reflections - Reflections took 1 ms to scan 1 urls, producing 4 keys and 9 values
2025-06-06 16:33:45.422 [tm] [RMI TCP Connection(5)-127.0.0.1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 16:33:45.422 [tm] [RMI TCP Connection(5)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 16:33:45.425 [tm] [RMI TCP Connection(5)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-06 16:33:45.443 [tm] [main] INFO  PDK - IPHolder [Server ip is [************, **********, 127.0.0.1] by ipPrefix null ethPrefix null]
2025-06-06 16:33:45.470 [tm] [RMI TCP Connection(3)-127.0.0.1] WARN  o.s.b.a.d.e.ElasticsearchReactiveHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.MonoCompletionStage] :
	reactor.core.publisher.Mono.fromFuture(Mono.java:629)
	org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClusterClient.health(ReactiveElasticsearchClusterClient.java:50)
Error has been observed at the following site(s):
	*__Mono.fromFuture ⇢ at org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClusterClient.health(ReactiveElasticsearchClusterClient.java:50)
	|_        Mono.map ⇢ at org.springframework.boot.actuate.data.elasticsearch.ElasticsearchReactiveHealthIndicator.doHealthCheck(ElasticsearchReactiveHealthIndicator.java:49)
Original Stack Trace:
		at java.base/sun.nio.ch.Net.pollConnect(Native Method)
		at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
		at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
		at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
		at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
		at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
		at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221)
		at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64)
		at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-06 16:33:46.002 [tm] [Thread-3] INFO  c.t.t.t.service.impl.LdpServiceImpl - Supplementary ldp task exception
2025-06-06 16:34:05.005 [tm] [taskScheduler-20] INFO  o.s.s.a.AnnotationAsyncExecutionInterceptor - More than one TaskExecutor bean found within the context, and none is named 'taskExecutor'. Mark one of them as primary or name it 'taskExecutor' (possibly as an alias) in order to use it for async processing: [taskScheduler, NotificationExecutor]
2025-06-06 16:34:31.994 [tm] [127.0.0.1-92-5a6f9b35-d1aa-4286-86b5-16d219acd1f5] INFO  c.t.t.o.g.c.OpenApiGeneratorController - Received code generation request: oas=https://petstore3.swagger.io/api/v3/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata
2025-06-06 16:34:31.994 [tm] [127.0.0.1-92-5a6f9b35-d1aa-4286-86b5-16d219acd1f5] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Starting code generation with request parameters: CodeGenerationRequest(oas=https://petstore3.swagger.io/api/v3/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata)
2025-06-06 16:34:31.995 [tm] [127.0.0.1-92-5a6f9b35-d1aa-4286-86b5-16d219acd1f5] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Executing command: java -jar /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator/openapi-generator-cli.jar generate -i https://petstore3.swagger.io/api/v3/openapi.json -g java -o /var/folders/x5/j5713w351xsdlsjxq64311m00000gn/T/openapi-generator/43c54859-271b-48d1-ba5c-ab4f26f71d73 --package-name io.tapdata.sdk --artifact-id tapdata-sdk --group-id io.tapdata -t /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator
2025-06-06 16:34:37.289 [tm] [127.0.0.1-92-5a6f9b35-d1aa-4286-86b5-16d219acd1f5] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Code generation successful
2025-06-06 16:34:37.298 [tm] [127.0.0.1-92-5a6f9b35-d1aa-4286-86b5-16d219acd1f5] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Code generation completed, file size: 134836 bytes
2025-06-06 16:34:45.520 [tm] [EMS-3-thread-1] INFO  PDK - NodeHealthManager [Found dead node registration ********** time Thu Jun 05 09:30:20 CST 2025, deleted]
2025-06-06 16:34:45.526 [tm] [EMS-3-thread-1] INFO  PDK - NodeHealthManager [Found dead node health ********** time Thu Jun 05 09:30:20 CST 2025, deleted]
2025-06-06 16:34:45.539 [tm] [EMS-3-thread-1] INFO  PDK - NodeHealthManager [Found dead proxy subscription ********** time , deleted]
2025-06-06 16:35:00.069 [tm] [TaskAlarmScheduler-taskAgentAlarm] ERROR c.t.tm.schedule.TaskAlarmScheduler - taskAgentAlarm stopEgineMap data:{"sam_iengine":{"cpuLoad":0,"createAt":1749088057735,"createUser":"<EMAIL>","gitCommitId":"-","hostname":"Sam-macbook-m1x.local","id":{"date":**********000,"timestamp":**********},"lastUpdAt":1749113570469,"lastUpdBy":"62bc5008d4958d013d97c7a6","metricValues":{"HeapMemoryUsage":0.06081453338265419,"CpuUsage":0.03936814433695751},"pingTime":1749113570460,"processId":"sam_iengine","singletonLock":"67574fba-c1fb-46c1-8842-82de3f2eec6a","stopping":false,"totalThread":1,"usedMemory":654311424,"userId":"62bc5008d4958d013d97c7a6","version":"-","workerDate":1749113570457,"workerType":"connector"}}
2025-06-06 16:36:14.876 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:36:19.932 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:36:24.969 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:36:27.080 [tm] [127.0.0.1-96-1ecd97b5-b7d8-4761-b679-c7ffcf587d95] INFO  c.t.t.o.g.c.OpenApiGeneratorController - Received code generation request: oas=https://invalid-url.example.com/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata
2025-06-06 16:36:27.080 [tm] [127.0.0.1-96-1ecd97b5-b7d8-4761-b679-c7ffcf587d95] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Starting code generation with request parameters: CodeGenerationRequest(oas=https://invalid-url.example.com/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata)
2025-06-06 16:36:27.081 [tm] [127.0.0.1-96-1ecd97b5-b7d8-4761-b679-c7ffcf587d95] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Executing command: java -jar /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator/openapi-generator-cli.jar generate -i https://invalid-url.example.com/openapi.json -g java -o /var/folders/x5/j5713w351xsdlsjxq64311m00000gn/T/openapi-generator/fb953fc9-18da-4aa9-bfd0-3e0e1e0b703a --package-name io.tapdata.sdk --artifact-id tapdata-sdk --group-id io.tapdata -t /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator
2025-06-06 16:36:30.014 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:36:35.067 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:36:40.094 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:36:45.126 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:36:50.187 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:36:55.239 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:37:00.320 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:37:05.362 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:37:09.172 [tm] [127.0.0.1-96-1ecd97b5-b7d8-4761-b679-c7ffcf587d95] ERROR c.t.t.o.g.s.OpenApiGeneratorService - Code generation failed, exit code: 1, output: [main] ERROR io.swagger.v3.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.v3.parser.util.RemoteUrl.urlToString(RemoteUrl.java:147)
	at io.swagger.v3.parser.OpenAPIV3Parser.readWithInfo(OpenAPIV3Parser.java:123)
	at io.swagger.v3.parser.OpenAPIV3Parser.readLocation(OpenAPIV3Parser.java:45)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 13 common frames omitted
[main] ERROR io.swagger.v3.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.v3.parser.util.RemoteUrl.urlToString(RemoteUrl.java:147)
	at io.swagger.v3.parser.OpenAPIV3Parser.readWithInfo(OpenAPIV3Parser.java:123)
	at io.swagger.v3.parser.OpenAPIV3Parser.readLocation(OpenAPIV3Parser.java:45)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 13 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.Swagger20Parser.readWithInfo(Swagger20Parser.java:44)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:32)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 14 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.Swagger20Parser.readWithInfo(Swagger20Parser.java:44)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:32)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 14 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.Swagger20Parser.readWithInfo(Swagger20Parser.java:44)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 14 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.Swagger20Parser.readWithInfo(Swagger20Parser.java:44)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 14 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.SwaggerCompatConverter.readResourceListing(SwaggerCompatConverter.java:193)
	at io.swagger.parser.SwaggerCompatConverter.read(SwaggerCompatConverter.java:123)
	at io.swagger.parser.SwaggerCompatConverter.readWithInfo(SwaggerCompatConverter.java:94)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 16 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.SwaggerCompatConverter.readResourceListing(SwaggerCompatConverter.java:193)
	at io.swagger.parser.SwaggerCompatConverter.read(SwaggerCompatConverter.java:123)
	at io.swagger.parser.SwaggerCompatConverter.readWithInfo(SwaggerCompatConverter.java:94)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 16 common frames omitted
[main] ERROR i.s.parser.SwaggerCompatConverter - failed to read resource listing
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.SwaggerCompatConverter.readResourceListing(SwaggerCompatConverter.java:193)
	at io.swagger.parser.SwaggerCompatConverter.read(SwaggerCompatConverter.java:123)
	at io.swagger.parser.SwaggerCompatConverter.readWithInfo(SwaggerCompatConverter.java:94)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 16 common frames omitted
[main] ERROR i.s.parser.SwaggerCompatConverter - failed to read resource listing
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.SwaggerCompatConverter.readResourceListing(SwaggerCompatConverter.java:193)
	at io.swagger.parser.SwaggerCompatConverter.read(SwaggerCompatConverter.java:123)
	at io.swagger.parser.SwaggerCompatConverter.readWithInfo(SwaggerCompatConverter.java:94)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 16 common frames omitted
Exception in thread "main" java.lang.NullPointerException
	at java.util.HashSet.<init>(HashSet.java:119)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:588)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)

2025-06-06 16:37:09.174 [tm] [127.0.0.1-96-1ecd97b5-b7d8-4761-b679-c7ffcf587d95] ERROR c.t.t.o.g.c.OpenApiGeneratorController - Code generation failed
com.tapdata.tm.openapi.generator.exception.CodeGenerationException: Code generation failed: [main] ERROR io.swagger.v3.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.v3.parser.util.RemoteUrl.urlToString(RemoteUrl.java:147)
	at io.swagger.v3.parser.OpenAPIV3Parser.readWithInfo(OpenAPIV3Parser.java:123)
	at io.swagger.v3.parser.OpenAPIV3Parser.readLocation(OpenAPIV3Parser.java:45)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 13 common frames omitted
[main] ERROR io.swagger.v3.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.v3.parser.util.RemoteUrl.urlToString(RemoteUrl.java:147)
	at io.swagger.v3.parser.OpenAPIV3Parser.readWithInfo(OpenAPIV3Parser.java:123)
	at io.swagger.v3.parser.OpenAPIV3Parser.readLocation(OpenAPIV3Parser.java:45)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 13 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.Swagger20Parser.readWithInfo(Swagger20Parser.java:44)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:32)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 14 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.Swagger20Parser.readWithInfo(Swagger20Parser.java:44)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:32)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 14 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.Swagger20Parser.readWithInfo(Swagger20Parser.java:44)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 14 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.Swagger20Parser.readWithInfo(Swagger20Parser.java:44)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 14 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.SwaggerCompatConverter.readResourceListing(SwaggerCompatConverter.java:193)
	at io.swagger.parser.SwaggerCompatConverter.read(SwaggerCompatConverter.java:123)
	at io.swagger.parser.SwaggerCompatConverter.readWithInfo(SwaggerCompatConverter.java:94)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 16 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.SwaggerCompatConverter.readResourceListing(SwaggerCompatConverter.java:193)
	at io.swagger.parser.SwaggerCompatConverter.read(SwaggerCompatConverter.java:123)
	at io.swagger.parser.SwaggerCompatConverter.readWithInfo(SwaggerCompatConverter.java:94)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 16 common frames omitted
[main] ERROR i.s.parser.SwaggerCompatConverter - failed to read resource listing
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.SwaggerCompatConverter.readResourceListing(SwaggerCompatConverter.java:193)
	at io.swagger.parser.SwaggerCompatConverter.read(SwaggerCompatConverter.java:123)
	at io.swagger.parser.SwaggerCompatConverter.readWithInfo(SwaggerCompatConverter.java:94)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 16 common frames omitted
[main] ERROR i.s.parser.SwaggerCompatConverter - failed to read resource listing
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.SwaggerCompatConverter.readResourceListing(SwaggerCompatConverter.java:193)
	at io.swagger.parser.SwaggerCompatConverter.read(SwaggerCompatConverter.java:123)
	at io.swagger.parser.SwaggerCompatConverter.readWithInfo(SwaggerCompatConverter.java:94)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 16 common frames omitted
Exception in thread "main" java.lang.NullPointerException
	at java.util.HashSet.<init>(HashSet.java:119)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:588)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)

	at com.tapdata.tm.openapi.generator.service.OpenApiGeneratorService.executeGenerator(OpenApiGeneratorService.java:154)
	at com.tapdata.tm.openapi.generator.service.OpenApiGeneratorService.generateCode(OpenApiGeneratorService.java:58)
	at com.tapdata.tm.openapi.generator.controller.OpenApiGeneratorController.generateCode(OpenApiGeneratorController.java:84)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.tapdata.tm.openapi.generator.controller.OpenApiGeneratorController$$SpringCGLIB$$0.generateCode(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.tapdata.tm.base.filter.RequestFilter.doFilter(RequestFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter.doFilterInternal(DefaultLogoutPageGeneratingFilter.java:58)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:220)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:206)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultResourcesFilter.doFilter(DefaultResourcesFilter.java:72)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-06 16:37:09.178 [tm] [127.0.0.1-96-1ecd97b5-b7d8-4761-b679-c7ffcf587d95] ERROR c.t.tm.base.handler.ExceptionHandler - System error
com.tapdata.tm.openapi.generator.exception.CodeGenerationException: Code generation failed: [main] ERROR io.swagger.v3.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.v3.parser.util.RemoteUrl.urlToString(RemoteUrl.java:147)
	at io.swagger.v3.parser.OpenAPIV3Parser.readWithInfo(OpenAPIV3Parser.java:123)
	at io.swagger.v3.parser.OpenAPIV3Parser.readLocation(OpenAPIV3Parser.java:45)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 13 common frames omitted
[main] ERROR io.swagger.v3.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.v3.parser.util.RemoteUrl.urlToString(RemoteUrl.java:147)
	at io.swagger.v3.parser.OpenAPIV3Parser.readWithInfo(OpenAPIV3Parser.java:123)
	at io.swagger.v3.parser.OpenAPIV3Parser.readLocation(OpenAPIV3Parser.java:45)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 13 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.Swagger20Parser.readWithInfo(Swagger20Parser.java:44)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:32)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 14 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.Swagger20Parser.readWithInfo(Swagger20Parser.java:44)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:32)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 14 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.Swagger20Parser.readWithInfo(Swagger20Parser.java:44)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 14 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.Swagger20Parser.readWithInfo(Swagger20Parser.java:44)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 14 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.SwaggerCompatConverter.readResourceListing(SwaggerCompatConverter.java:193)
	at io.swagger.parser.SwaggerCompatConverter.read(SwaggerCompatConverter.java:123)
	at io.swagger.parser.SwaggerCompatConverter.readWithInfo(SwaggerCompatConverter.java:94)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 16 common frames omitted
[main] ERROR io.swagger.parser.util.RemoteUrl - unable to read
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.SwaggerCompatConverter.readResourceListing(SwaggerCompatConverter.java:193)
	at io.swagger.parser.SwaggerCompatConverter.read(SwaggerCompatConverter.java:123)
	at io.swagger.parser.SwaggerCompatConverter.readWithInfo(SwaggerCompatConverter.java:94)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 16 common frames omitted
[main] ERROR i.s.parser.SwaggerCompatConverter - failed to read resource listing
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.SwaggerCompatConverter.readResourceListing(SwaggerCompatConverter.java:193)
	at io.swagger.parser.SwaggerCompatConverter.read(SwaggerCompatConverter.java:123)
	at io.swagger.parser.SwaggerCompatConverter.readWithInfo(SwaggerCompatConverter.java:94)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 16 common frames omitted
[main] ERROR i.s.parser.SwaggerCompatConverter - failed to read resource listing
javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
	at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
	at sun.net.www.protocol.https.HttpsClient.afterConnect(HttpsClient.java:559)
	at sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:185)
	at sun.net.www.protocol.https.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:162)
	at io.swagger.parser.util.RemoteUrl.urlToString(RemoteUrl.java:134)
	at io.swagger.parser.SwaggerCompatConverter.readResourceListing(SwaggerCompatConverter.java:193)
	at io.swagger.parser.SwaggerCompatConverter.read(SwaggerCompatConverter.java:123)
	at io.swagger.parser.SwaggerCompatConverter.readWithInfo(SwaggerCompatConverter.java:94)
	at io.swagger.parser.SwaggerParser.readWithInfo(SwaggerParser.java:42)
	at io.swagger.v3.parser.converter.SwaggerConverter.readLocation(SwaggerConverter.java:92)
	at io.swagger.parser.OpenAPIParser.readLocation(OpenAPIParser.java:16)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:586)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.InputRecord.read(InputRecord.java:505)
	at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
	... 16 common frames omitted
Exception in thread "main" java.lang.NullPointerException
	at java.util.HashSet.<init>(HashSet.java:119)
	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:588)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)

	at com.tapdata.tm.openapi.generator.service.OpenApiGeneratorService.executeGenerator(OpenApiGeneratorService.java:154)
	at com.tapdata.tm.openapi.generator.service.OpenApiGeneratorService.generateCode(OpenApiGeneratorService.java:58)
	at com.tapdata.tm.openapi.generator.controller.OpenApiGeneratorController.generateCode(OpenApiGeneratorController.java:84)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.tapdata.tm.openapi.generator.controller.OpenApiGeneratorController$$SpringCGLIB$$0.generateCode(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.tapdata.tm.base.filter.RequestFilter.doFilter(RequestFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter.doFilterInternal(DefaultLogoutPageGeneratingFilter.java:58)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:220)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:206)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultResourcesFilter.doFilter(DefaultResourcesFilter.java:72)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-06 16:37:10.408 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:37:15.451 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:37:20.499 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:37:22.861 [tm] [127.0.0.1-97-effe17f8-645a-4329-b440-7b99666d7539] INFO  c.t.t.o.g.c.OpenApiGeneratorController - Received code generation request: oas=https://petstore3.swagger.io/api/v3/openapi.json, lan=unsupported-language, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata
2025-06-06 16:37:22.861 [tm] [127.0.0.1-97-effe17f8-645a-4329-b440-7b99666d7539] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Starting code generation with request parameters: CodeGenerationRequest(oas=https://petstore3.swagger.io/api/v3/openapi.json, lan=unsupported-language, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata)
2025-06-06 16:37:22.861 [tm] [127.0.0.1-97-effe17f8-645a-4329-b440-7b99666d7539] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Executing command: java -jar /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator/openapi-generator-cli.jar generate -i https://petstore3.swagger.io/api/v3/openapi.json -g unsupported-language -o /var/folders/x5/j5713w351xsdlsjxq64311m00000gn/T/openapi-generator/f12a1dd1-373d-445d-971b-4adb8196947f --package-name io.tapdata.sdk --artifact-id tapdata-sdk --group-id io.tapdata -t /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator
2025-06-06 16:37:23.781 [tm] [127.0.0.1-97-effe17f8-645a-4329-b440-7b99666d7539] ERROR c.t.t.o.g.s.OpenApiGeneratorService - Code generation failed, exit code: 1, output: Can't load config class with name 'unsupported-language'
Available:
ada
ada-server
android
apache2
apex
aspnetcore
bash
c
clojure
cwiki
cpp-qt5-client
cpp-qt5-qhttpengine-server
cpp-pistache-server
cpp-restbed-server
cpp-restsdk
cpp-tizen
csharp
csharp-netcore
csharp-dotnet2
csharp-nancyfx
dart
dart-jaguar
eiffel
elixir
elm
erlang-client
erlang-proper
erlang-server
flash
go
go-server
go-gin-server
graphql-schema
graphql-nodejs-express-server
groovy
kotlin
kotlin-server
kotlin-spring
haskell-http-client
haskell
java
jaxrs-cxf-client
java-inflector
java-msf4j
java-pkmst
java-play-framework
java-undertow-server
java-vertx
jaxrs-cxf
jaxrs-cxf-extended
jaxrs-cxf-cdi
jaxrs-jersey
jaxrs-resteasy
jaxrs-resteasy-eap
jaxrs-spec
javascript
javascript-flowtyped
javascript-closure-angular
jmeter
lua
mysql-schema
nodejs-server-deprecated
objc
openapi
openapi-yaml
perl
php
php-laravel
php-lumen
php-slim
php-silex
php-symfony
php-ze-ph
powershell
python
python-flask
python-aiohttp
python-blueplanet
r
ruby
ruby-on-rails
ruby-sinatra
rust
rust-server
scalatra
scala-akka
scala-finch
scala-httpclient-deprecated
scala-gatling
scala-lagom-server
scala-play-server
scalaz
spring
dynamic-html
html
html2
swift2-deprecated
swift3-deprecated
swift4
typescript-angular
typescript-angularjs
typescript-aurelia
typescript-axios
typescript-fetch
typescript-inversify
typescript-jquery
typescript-node
typescript-rxjs
fsharp-giraffe-server

[error] Check the spelling of the generator's name and try again.

2025-06-06 16:37:23.782 [tm] [127.0.0.1-97-effe17f8-645a-4329-b440-7b99666d7539] ERROR c.t.t.o.g.c.OpenApiGeneratorController - Code generation failed
com.tapdata.tm.openapi.generator.exception.CodeGenerationException: Code generation failed: Can't load config class with name 'unsupported-language'
Available:
ada
ada-server
android
apache2
apex
aspnetcore
bash
c
clojure
cwiki
cpp-qt5-client
cpp-qt5-qhttpengine-server
cpp-pistache-server
cpp-restbed-server
cpp-restsdk
cpp-tizen
csharp
csharp-netcore
csharp-dotnet2
csharp-nancyfx
dart
dart-jaguar
eiffel
elixir
elm
erlang-client
erlang-proper
erlang-server
flash
go
go-server
go-gin-server
graphql-schema
graphql-nodejs-express-server
groovy
kotlin
kotlin-server
kotlin-spring
haskell-http-client
haskell
java
jaxrs-cxf-client
java-inflector
java-msf4j
java-pkmst
java-play-framework
java-undertow-server
java-vertx
jaxrs-cxf
jaxrs-cxf-extended
jaxrs-cxf-cdi
jaxrs-jersey
jaxrs-resteasy
jaxrs-resteasy-eap
jaxrs-spec
javascript
javascript-flowtyped
javascript-closure-angular
jmeter
lua
mysql-schema
nodejs-server-deprecated
objc
openapi
openapi-yaml
perl
php
php-laravel
php-lumen
php-slim
php-silex
php-symfony
php-ze-ph
powershell
python
python-flask
python-aiohttp
python-blueplanet
r
ruby
ruby-on-rails
ruby-sinatra
rust
rust-server
scalatra
scala-akka
scala-finch
scala-httpclient-deprecated
scala-gatling
scala-lagom-server
scala-play-server
scalaz
spring
dynamic-html
html
html2
swift2-deprecated
swift3-deprecated
swift4
typescript-angular
typescript-angularjs
typescript-aurelia
typescript-axios
typescript-fetch
typescript-inversify
typescript-jquery
typescript-node
typescript-rxjs
fsharp-giraffe-server

[error] Check the spelling of the generator's name and try again.

	at com.tapdata.tm.openapi.generator.service.OpenApiGeneratorService.executeGenerator(OpenApiGeneratorService.java:154)
	at com.tapdata.tm.openapi.generator.service.OpenApiGeneratorService.generateCode(OpenApiGeneratorService.java:58)
	at com.tapdata.tm.openapi.generator.controller.OpenApiGeneratorController.generateCode(OpenApiGeneratorController.java:84)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.tapdata.tm.openapi.generator.controller.OpenApiGeneratorController$$SpringCGLIB$$0.generateCode(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.tapdata.tm.base.filter.RequestFilter.doFilter(RequestFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter.doFilterInternal(DefaultLogoutPageGeneratingFilter.java:58)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:220)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:206)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultResourcesFilter.doFilter(DefaultResourcesFilter.java:72)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-06 16:37:23.783 [tm] [127.0.0.1-97-effe17f8-645a-4329-b440-7b99666d7539] ERROR c.t.tm.base.handler.ExceptionHandler - System error
com.tapdata.tm.openapi.generator.exception.CodeGenerationException: Code generation failed: Can't load config class with name 'unsupported-language'
Available:
ada
ada-server
android
apache2
apex
aspnetcore
bash
c
clojure
cwiki
cpp-qt5-client
cpp-qt5-qhttpengine-server
cpp-pistache-server
cpp-restbed-server
cpp-restsdk
cpp-tizen
csharp
csharp-netcore
csharp-dotnet2
csharp-nancyfx
dart
dart-jaguar
eiffel
elixir
elm
erlang-client
erlang-proper
erlang-server
flash
go
go-server
go-gin-server
graphql-schema
graphql-nodejs-express-server
groovy
kotlin
kotlin-server
kotlin-spring
haskell-http-client
haskell
java
jaxrs-cxf-client
java-inflector
java-msf4j
java-pkmst
java-play-framework
java-undertow-server
java-vertx
jaxrs-cxf
jaxrs-cxf-extended
jaxrs-cxf-cdi
jaxrs-jersey
jaxrs-resteasy
jaxrs-resteasy-eap
jaxrs-spec
javascript
javascript-flowtyped
javascript-closure-angular
jmeter
lua
mysql-schema
nodejs-server-deprecated
objc
openapi
openapi-yaml
perl
php
php-laravel
php-lumen
php-slim
php-silex
php-symfony
php-ze-ph
powershell
python
python-flask
python-aiohttp
python-blueplanet
r
ruby
ruby-on-rails
ruby-sinatra
rust
rust-server
scalatra
scala-akka
scala-finch
scala-httpclient-deprecated
scala-gatling
scala-lagom-server
scala-play-server
scalaz
spring
dynamic-html
html
html2
swift2-deprecated
swift3-deprecated
swift4
typescript-angular
typescript-angularjs
typescript-aurelia
typescript-axios
typescript-fetch
typescript-inversify
typescript-jquery
typescript-node
typescript-rxjs
fsharp-giraffe-server

[error] Check the spelling of the generator's name and try again.

	at com.tapdata.tm.openapi.generator.service.OpenApiGeneratorService.executeGenerator(OpenApiGeneratorService.java:154)
	at com.tapdata.tm.openapi.generator.service.OpenApiGeneratorService.generateCode(OpenApiGeneratorService.java:58)
	at com.tapdata.tm.openapi.generator.controller.OpenApiGeneratorController.generateCode(OpenApiGeneratorController.java:84)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.tapdata.tm.openapi.generator.controller.OpenApiGeneratorController$$SpringCGLIB$$0.generateCode(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.tapdata.tm.base.filter.RequestFilter.doFilter(RequestFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter.doFilterInternal(DefaultLogoutPageGeneratingFilter.java:58)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:220)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:206)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultResourcesFilter.doFilter(DefaultResourcesFilter.java:72)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-06 16:37:25.558 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:37:30.624 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:37:35.661 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:37:40.699 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:37:44.299 [tm] [127.0.0.1-99-79fd1767-14d4-4bbc-b867-c134a97fb324] INFO  c.t.t.o.g.c.OpenApiGeneratorController - Received GET code generation request: oas=https://petstore3.swagger.io/api/v3/openapi.json, lan=java
2025-06-06 16:37:44.300 [tm] [127.0.0.1-99-79fd1767-14d4-4bbc-b867-c134a97fb324] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Starting code generation with request parameters: CodeGenerationRequest(oas=https://petstore3.swagger.io/api/v3/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata)
2025-06-06 16:37:44.300 [tm] [127.0.0.1-99-79fd1767-14d4-4bbc-b867-c134a97fb324] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Executing command: java -jar /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator/openapi-generator-cli.jar generate -i https://petstore3.swagger.io/api/v3/openapi.json -g java -o /var/folders/x5/j5713w351xsdlsjxq64311m00000gn/T/openapi-generator/060f9af6-3def-4e8a-aada-79d55203f9ef --package-name io.tapdata.sdk --artifact-id tapdata-sdk --group-id io.tapdata -t /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator
2025-06-06 16:37:45.731 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:37:48.630 [tm] [127.0.0.1-99-79fd1767-14d4-4bbc-b867-c134a97fb324] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Code generation successful
2025-06-06 16:37:48.638 [tm] [127.0.0.1-99-79fd1767-14d4-4bbc-b867-c134a97fb324] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Code generation completed, file size: 134847 bytes
2025-06-06 16:37:50.769 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:37:55.865 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:38:00.914 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:38:05.960 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:38:11.016 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:38:16.089 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:38:21.151 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:38:26.179 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:38:31.208 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:38:36.235 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:38:41.316 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:38:46.349 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:38:51.381 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:38:56.429 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:38:58.569 [tm] [127.0.0.1-94-06ae2cb8-867c-4be3-b5c7-80d949cd127a] INFO  c.t.t.o.g.c.OpenApiGeneratorController - Received code generation request: oas=https://petstore3.swagger.io/api/v3/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata
2025-06-06 16:38:58.569 [tm] [127.0.0.1-94-06ae2cb8-867c-4be3-b5c7-80d949cd127a] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Starting code generation with request parameters: CodeGenerationRequest(oas=https://petstore3.swagger.io/api/v3/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata)
2025-06-06 16:38:58.570 [tm] [127.0.0.1-94-06ae2cb8-867c-4be3-b5c7-80d949cd127a] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Executing command: java -jar /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator/openapi-generator-cli.jar generate -i https://petstore3.swagger.io/api/v3/openapi.json -g java -o /var/folders/x5/j5713w351xsdlsjxq64311m00000gn/T/openapi-generator/15f7a5d0-2b34-4cef-ad47-01583fad9324 --package-name io.tapdata.sdk --artifact-id tapdata-sdk --group-id io.tapdata -t /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator
2025-06-06 16:39:01.468 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:39:02.763 [tm] [127.0.0.1-94-06ae2cb8-867c-4be3-b5c7-80d949cd127a] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Code generation successful
2025-06-06 16:39:02.771 [tm] [127.0.0.1-94-06ae2cb8-867c-4be3-b5c7-80d949cd127a] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Code generation completed, file size: 134839 bytes
2025-06-06 16:39:06.519 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:39:11.560 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:39:15.872 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-06 16:39:15.873 [tm] [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-06 16:39:16.378 [tm] [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-06 16:39:16.397 [tm] [SpringApplicationShutdownHook] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2025-06-06 16:39:16.397 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-06 16:39:16.397 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-06 16:39:16.398 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-06 16:39:20.151 [tm] [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-06 16:39:20.190 [tm] [main] INFO  com.tapdata.tm.TMApplication - Starting TMApplication using Java 17.0.14 with PID 15266 (/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes started by samuel in /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17)
2025-06-06 16:39:20.191 [tm] [main] INFO  com.tapdata.tm.TMApplication - The following 2 profiles are active: "idaas", "default"
2025-06-06 16:39:21.223 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:39:21.224 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-06 16:39:21.469 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 240 ms. Found 2 MongoDB repository interfaces.
2025-06-06 16:39:21.470 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:39:21.470 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-06 16:39:21.472 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 MongoDB repository interfaces.
2025-06-06 16:39:21.472 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:39:21.472 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-06 16:39:21.474 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 MongoDB repository interfaces.
2025-06-06 16:39:21.481 [tm] [main] INFO  c.t.t.w.c.WebSocketHandlerImportBeanRegistrar - The number of webSocketHandler beans successfully registered: 17
2025-06-06 16:39:21.788 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:39:21.788 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 16:39:21.840 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.task.repository.TaskDagCheckLogRepository; If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository
2025-06-06 16:39:21.840 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.Settings.repository.SettingsRepository; If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository
2025-06-06 16:39:21.840 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 51 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 16:39:21.843 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:39:21.844 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 16:39:21.887 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.task.repository.TaskDagCheckLogRepository; If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository
2025-06-06 16:39:21.888 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.Settings.repository.SettingsRepository; If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository
2025-06-06 16:39:21.888 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 43 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 16:39:22.536 [tm] [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 3000 (http)
2025-06-06 16:39:22.543 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - The ["http-nio-3000"] connector has been configured to support HTTP upgrade to [h2c]
2025-06-06 16:39:22.543 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-3000"]
2025-06-06 16:39:22.544 [tm] [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 16:39:22.544 [tm] [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.40]
2025-06-06 16:39:22.584 [tm] [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 16:39:22.584 [tm] [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2361 ms
2025-06-06 16:39:22.755 [tm] [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-data", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@37196d53, com.mongodb.Jep395RecordCodecProvider@40105b39, com.mongodb.KotlinCodecProvider@72a61e61]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 16:39:22.772 [tm] [cluster-ClusterId{value='6842a93a7cc64e1807e98dc9', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=15538250, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 16:39:16 CST 2025, lastUpdateTimeNanos=194790727170625}
2025-06-06 16:39:24.528 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@37196d53, com.mongodb.Jep395RecordCodecProvider@40105b39, com.mongodb.KotlinCodecProvider@72a61e61]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 16:39:24.536 [tm] [cluster-ClusterId{value='6842a93c7cc64e1807e98dca', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=8076750, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 16:39:16 CST 2025, lastUpdateTimeNanos=194792492717541}
2025-06-06 16:39:24.917 [tm] [main] INFO  c.t.t.c.component.ProductComponent - Current product type settings is DAAS.
2025-06-06 16:39:24.930 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@37196d53, com.mongodb.Jep395RecordCodecProvider@40105b39, com.mongodb.KotlinCodecProvider@72a61e61]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 16:39:24.934 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.cluster - Waiting for server to become available for operation with ID 13. Remaining time: 29998 ms. Selector: ReadPreferenceServerSelector{readPreference=primary}, topology description: {type=UNKNOWN, servers=[{address=localhost:27017, type=UNKNOWN, state=CONNECTING}].
2025-06-06 16:39:24.934 [tm] [cluster-ClusterId{value='6842a93c7cc64e1807e98dcb', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=4244042, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 16:39:16 CST 2025, lastUpdateTimeNanos=194792890908708}
2025-06-06 16:39:26.888 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-06-06 16:39:26.897 [tm] [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-06 16:39:26.897 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v2.5.0 created.
2025-06-06 16:39:26.898 [tm] [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-06-06 16:39:26.898 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-06 16:39:26.898 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-06 16:39:26.898 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.5.0
2025-06-06 16:39:26.898 [tm] [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7cb4fc46
2025-06-06 16:39:27.398 [tm] [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name userServiceImpl
2025-06-06 16:39:27.981 [tm] [main] WARN  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Multiple @RequestMapping annotations found on public com.tapdata.tm.base.dto.ResponseMessage com.tapdata.tm.message.controller.MessageController.read(java.lang.String,java.lang.String) throws java.io.IOException, but only the first will be used: [@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={}, name="", params={}, path={}, produces={}, value={}), @org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name="", params={}, path={}, produces={}, value={})]
2025-06-06 16:39:28.943 [tm] [main] WARN  o.s.d.e.c.m.SimpleElasticsearchPersistentProperty - Unsupported type 'class java.lang.Object' for date property 'date'.
2025-06-06 16:39:28.944 [tm] [main] WARN  o.s.d.e.c.m.SimpleElasticsearchPersistentProperty - Unsupported type 'class java.lang.Object' for date property 'millis'.
2025-06-06 16:39:29.061 [tm] [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoints beneath base path '/actuator'
2025-06-06 16:39:29.195 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-3000"]
2025-06-06 16:39:29.202 [tm] [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 3000 (http) with context path '/'
2025-06-06 16:39:29.204 [tm] [main] INFO  o.s.s.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-06-06 16:39:29.204 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-06 16:39:29.221 [tm] [main] INFO  c.t.t.s.u.StateMachineProcessorManager - Urls path: /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/com/tapdata/tm/statemachine/processor, protocol: file
2025-06-06 16:39:29.236 [tm] [main] INFO  com.tapdata.tm.TMApplication - Started TMApplication in 9.346 seconds (process running for 10.035)
2025-06-06 16:39:29.313 [tm] [main] INFO  com.tapdata.tm.init.PatchesRunner - Not have any DAAS patches, current version is 4.0-1
2025-06-06 16:39:29.379 [tm] [main] INFO  com.tapdata.tm.init.InitLogMap - 
  _______              _       _           _                    _             _                                _      _           _ 
 |__   __|            | |     | |         | |                  | |           | |                              | |    | |         | |
    | | __ _ _ __   __| | __ _| |_ __ _   | |_ _ __ ___     ___| |_ __ _ _ __| |_     ___ ___  _ __ ___  _ __ | | ___| |_ ___  __| |
    | |/ _` | '_ \ / _` |/ _` | __/ _` |  | __| '_ ` _ \   / __| __/ _` | '__| __|   / __/ _ \| '_ ` _ \| '_ \| |/ _ \ __/ _ \/ _` |
    | | (_| | |_) | (_| | (_| | || (_| |  | |_| | | | | |  \__ \ || (_| | |  | |_   | (_| (_) | | | | | | |_) | |  __/ ||  __/ (_| |
    |_|\__,_| .__/ \__,_|\__,_|\__\__,_|   \__|_| |_| |_|  |___/\__\__,_|_|   \__|   \___\___/|_| |_| |_| .__/|_|\___|\__\___|\__,_|
            | |                                                                                         | |                         
            |_|                                                                                         |_|                         
2025-06-06 16:39:29.381 [tm] [Thread-1] INFO  com.tapdata.tm.TMApplication - No need to start TCM
2025-06-06 16:39:29.384 [tm] [main] INFO  com.tapdata.tm.TMApplication - admin access code is 3324cfdf-7d3e-4792-bd32-571638d4562f
2025-06-06 16:39:29.516 [tm] [main] INFO  org.reflections.Reflections - Reflections took 121 ms to scan 19 urls, producing 62 keys and 1137 values
2025-06-06 16:39:29.627 [tm] [main] INFO  org.reflections.Reflections - Reflections took 14 ms to scan 12 urls, producing 15 keys and 149 values
2025-06-06 16:39:29.630 [tm] [main] INFO  org.reflections.Reflections - Reflections took 2 ms to scan 1 urls, producing 10 keys and 24 values
2025-06-06 16:39:29.632 [tm] [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@37196d53, com.mongodb.Jep395RecordCodecProvider@40105b39, com.mongodb.KotlinCodecProvider@72a61e61]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 16:39:29.638 [tm] [cluster-ClusterId{value='6842a9417cc64e1807e98dcc', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=5248750, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 16:39:29 CST 2025, lastUpdateTimeNanos=194797594359708}
2025-06-06 16:39:29.696 [tm] [RMI TCP Connection(4)-127.0.0.1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 16:39:29.697 [tm] [RMI TCP Connection(4)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 16:39:29.700 [tm] [RMI TCP Connection(4)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-06 16:39:29.735 [tm] [main] INFO  org.reflections.Reflections - Reflections took 1 ms to scan 1 urls, producing 4 keys and 9 values
2025-06-06 16:39:29.740 [tm] [RMI TCP Connection(2)-127.0.0.1] WARN  o.s.b.a.d.e.ElasticsearchReactiveHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.MonoCompletionStage] :
	reactor.core.publisher.Mono.fromFuture(Mono.java:629)
	org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClusterClient.health(ReactiveElasticsearchClusterClient.java:50)
Error has been observed at the following site(s):
	*__Mono.fromFuture ⇢ at org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClusterClient.health(ReactiveElasticsearchClusterClient.java:50)
	|_        Mono.map ⇢ at org.springframework.boot.actuate.data.elasticsearch.ElasticsearchReactiveHealthIndicator.doHealthCheck(ElasticsearchReactiveHealthIndicator.java:49)
Original Stack Trace:
		at java.base/sun.nio.ch.Net.pollConnect(Native Method)
		at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
		at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
		at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
		at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
		at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
		at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221)
		at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64)
		at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-06 16:39:29.792 [tm] [main] INFO  PDK - IPHolder [Server ip is [************, **********, 127.0.0.1] by ipPrefix null ethPrefix null]
2025-06-06 16:39:30.373 [tm] [Thread-3] INFO  c.t.t.t.service.impl.LdpServiceImpl - Supplementary ldp task exception
2025-06-06 16:39:44.739 [tm] [127.0.0.1-87-b14264f3-3dd0-4910-a34b-453f3ffc158e] INFO  c.t.t.o.g.c.OpenApiGeneratorController - Received code generation request: oas=https://petstore3.swagger.io/api/v3/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata
2025-06-06 16:39:44.739 [tm] [127.0.0.1-87-b14264f3-3dd0-4910-a34b-453f3ffc158e] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Starting code generation with request parameters: CodeGenerationRequest(oas=https://petstore3.swagger.io/api/v3/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata)
2025-06-06 16:39:44.739 [tm] [127.0.0.1-87-b14264f3-3dd0-4910-a34b-453f3ffc158e] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Generator parameters: CodeGenerationRequest(oas=https://petstore3.swagger.io/api/v3/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata), output dir: /var/folders/x5/j5713w351xsdlsjxq64311m00000gn/T/openapi-generator/66866879-e584-40c4-af4a-26db8a9a814d
2025-06-06 16:39:44.740 [tm] [127.0.0.1-87-b14264f3-3dd0-4910-a34b-453f3ffc158e] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Executing command: java -jar /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator/openapi-generator-cli.jar generate -i https://petstore3.swagger.io/api/v3/openapi.json -g java -o /var/folders/x5/j5713w351xsdlsjxq64311m00000gn/T/openapi-generator/66866879-e584-40c4-af4a-26db8a9a814d --package-name io.tapdata.sdk --artifact-id tapdata-sdk --group-id io.tapdata -t /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator
2025-06-06 16:39:49.105 [tm] [127.0.0.1-87-b14264f3-3dd0-4910-a34b-453f3ffc158e] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Code generation successful
2025-06-06 16:39:49.117 [tm] [127.0.0.1-87-b14264f3-3dd0-4910-a34b-453f3ffc158e] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Code generation completed, file size: 134848 bytes
2025-06-06 16:40:00.068 [tm] [TaskAlarmScheduler-taskAgentAlarm] ERROR c.t.tm.schedule.TaskAlarmScheduler - taskAgentAlarm stopEgineMap data:{"sam_iengine":{"cpuLoad":0,"createAt":1749088057735,"createUser":"<EMAIL>","gitCommitId":"-","hostname":"Sam-macbook-m1x.local","id":{"date":**********000,"timestamp":**********},"lastUpdAt":1749113570469,"lastUpdBy":"62bc5008d4958d013d97c7a6","metricValues":{"HeapMemoryUsage":0.06081453338265419,"CpuUsage":0.03936814433695751},"pingTime":1749113570460,"processId":"sam_iengine","singletonLock":"67574fba-c1fb-46c1-8842-82de3f2eec6a","stopping":false,"totalThread":1,"usedMemory":654311424,"userId":"62bc5008d4958d013d97c7a6","version":"-","workerDate":1749113570457,"workerType":"connector"}}
2025-06-06 16:40:05.018 [tm] [taskScheduler-12] INFO  o.s.s.a.AnnotationAsyncExecutionInterceptor - More than one TaskExecutor bean found within the context, and none is named 'taskExecutor'. Mark one of them as primary or name it 'taskExecutor' (possibly as an alias) in order to use it for async processing: [taskScheduler, NotificationExecutor]
2025-06-06 16:41:59.254 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:42:04.285 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:42:09.330 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:42:14.379 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:42:19.412 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:42:24.441 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:42:29.460 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:42:34.530 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:42:39.581 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:42:42.721 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-06 16:42:42.722 [tm] [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-06 16:42:43.228 [tm] [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-06 16:42:43.251 [tm] [SpringApplicationShutdownHook] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2025-06-06 16:42:43.251 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-06 16:42:43.252 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-06 16:42:43.252 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-06 16:42:46.997 [tm] [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-06 16:42:47.039 [tm] [main] INFO  com.tapdata.tm.TMApplication - Starting TMApplication using Java 17.0.14 with PID 15700 (/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes started by samuel in /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17)
2025-06-06 16:42:47.040 [tm] [main] INFO  com.tapdata.tm.TMApplication - The following 2 profiles are active: "idaas", "default"
2025-06-06 16:42:48.071 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:42:48.071 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-06 16:42:48.367 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 290 ms. Found 2 MongoDB repository interfaces.
2025-06-06 16:42:48.368 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:42:48.368 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-06 16:42:48.371 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 MongoDB repository interfaces.
2025-06-06 16:42:48.371 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:42:48.371 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-06 16:42:48.373 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 MongoDB repository interfaces.
2025-06-06 16:42:48.380 [tm] [main] INFO  c.t.t.w.c.WebSocketHandlerImportBeanRegistrar - The number of webSocketHandler beans successfully registered: 17
2025-06-06 16:42:48.713 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:42:48.713 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 16:42:48.767 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.task.repository.TaskDagCheckLogRepository; If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository
2025-06-06 16:42:48.767 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.Settings.repository.SettingsRepository; If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository
2025-06-06 16:42:48.767 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 52 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 16:42:48.770 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:42:48.770 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 16:42:48.816 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.task.repository.TaskDagCheckLogRepository; If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository
2025-06-06 16:42:48.817 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.Settings.repository.SettingsRepository; If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository
2025-06-06 16:42:48.817 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 45 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 16:42:49.536 [tm] [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 3000 (http)
2025-06-06 16:42:49.546 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - The ["http-nio-3000"] connector has been configured to support HTTP upgrade to [h2c]
2025-06-06 16:42:49.547 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-3000"]
2025-06-06 16:42:49.548 [tm] [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 16:42:49.548 [tm] [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.40]
2025-06-06 16:42:49.591 [tm] [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 16:42:49.591 [tm] [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2513 ms
2025-06-06 16:42:49.792 [tm] [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-data", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@17e8caf2, com.mongodb.Jep395RecordCodecProvider@104cf647, com.mongodb.KotlinCodecProvider@7488c183]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 16:42:49.809 [tm] [cluster-ClusterId{value='6842aa09a6d07a177167b4db', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=16961375, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 16:42:40 CST 2025, lastUpdateTimeNanos=194997762476625}
2025-06-06 16:42:51.597 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@17e8caf2, com.mongodb.Jep395RecordCodecProvider@104cf647, com.mongodb.KotlinCodecProvider@7488c183]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 16:42:51.612 [tm] [cluster-ClusterId{value='6842aa0ba6d07a177167b4dc', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=2977000, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 16:42:40 CST 2025, lastUpdateTimeNanos=194999555223500}
2025-06-06 16:42:51.992 [tm] [main] INFO  c.t.t.c.component.ProductComponent - Current product type settings is DAAS.
2025-06-06 16:42:52.004 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@17e8caf2, com.mongodb.Jep395RecordCodecProvider@104cf647, com.mongodb.KotlinCodecProvider@7488c183]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 16:42:52.009 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.cluster - Waiting for server to become available for operation with ID 13. Remaining time: 29998 ms. Selector: ReadPreferenceServerSelector{readPreference=primary}, topology description: {type=UNKNOWN, servers=[{address=localhost:27017, type=UNKNOWN, state=CONNECTING}].
2025-06-06 16:42:52.026 [tm] [cluster-ClusterId{value='6842aa0ca6d07a177167b4dd', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=21578292, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 16:42:40 CST 2025, lastUpdateTimeNanos=194999981423875}
2025-06-06 16:42:53.984 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-06-06 16:42:53.993 [tm] [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-06 16:42:53.993 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v2.5.0 created.
2025-06-06 16:42:53.994 [tm] [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-06-06 16:42:53.994 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-06 16:42:53.994 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-06 16:42:53.994 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.5.0
2025-06-06 16:42:53.994 [tm] [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@29e40db1
2025-06-06 16:42:54.443 [tm] [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name userServiceImpl
2025-06-06 16:42:55.049 [tm] [main] WARN  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Multiple @RequestMapping annotations found on public com.tapdata.tm.base.dto.ResponseMessage com.tapdata.tm.message.controller.MessageController.read(java.lang.String,java.lang.String) throws java.io.IOException, but only the first will be used: [@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={}, name="", params={}, path={}, produces={}, value={}), @org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name="", params={}, path={}, produces={}, value={})]
2025-06-06 16:42:56.025 [tm] [main] WARN  o.s.d.e.c.m.SimpleElasticsearchPersistentProperty - Unsupported type 'class java.lang.Object' for date property 'date'.
2025-06-06 16:42:56.026 [tm] [main] WARN  o.s.d.e.c.m.SimpleElasticsearchPersistentProperty - Unsupported type 'class java.lang.Object' for date property 'millis'.
2025-06-06 16:42:56.140 [tm] [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoints beneath base path '/actuator'
2025-06-06 16:42:56.280 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-3000"]
2025-06-06 16:42:56.287 [tm] [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 3000 (http) with context path '/'
2025-06-06 16:42:56.288 [tm] [main] INFO  o.s.s.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-06-06 16:42:56.288 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-06 16:42:56.307 [tm] [main] INFO  c.t.t.s.u.StateMachineProcessorManager - Urls path: /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/com/tapdata/tm/statemachine/processor, protocol: file
2025-06-06 16:42:56.324 [tm] [main] INFO  com.tapdata.tm.TMApplication - Started TMApplication in 9.589 seconds (process running for 10.313)
2025-06-06 16:42:56.399 [tm] [main] INFO  com.tapdata.tm.init.PatchesRunner - Not have any DAAS patches, current version is 4.0-1
2025-06-06 16:42:56.460 [tm] [main] INFO  com.tapdata.tm.init.InitLogMap - 
  _______              _       _           _                    _             _                                _      _           _ 
 |__   __|            | |     | |         | |                  | |           | |                              | |    | |         | |
    | | __ _ _ __   __| | __ _| |_ __ _   | |_ _ __ ___     ___| |_ __ _ _ __| |_     ___ ___  _ __ ___  _ __ | | ___| |_ ___  __| |
    | |/ _` | '_ \ / _` |/ _` | __/ _` |  | __| '_ ` _ \   / __| __/ _` | '__| __|   / __/ _ \| '_ ` _ \| '_ \| |/ _ \ __/ _ \/ _` |
    | | (_| | |_) | (_| | (_| | || (_| |  | |_| | | | | |  \__ \ || (_| | |  | |_   | (_| (_) | | | | | | |_) | |  __/ ||  __/ (_| |
    |_|\__,_| .__/ \__,_|\__,_|\__\__,_|   \__|_| |_| |_|  |___/\__\__,_|_|   \__|   \___\___/|_| |_| |_| .__/|_|\___|\__\___|\__,_|
            | |                                                                                         | |                         
            |_|                                                                                         |_|                         
2025-06-06 16:42:56.461 [tm] [Thread-1] INFO  com.tapdata.tm.TMApplication - No need to start TCM
2025-06-06 16:42:56.467 [tm] [main] INFO  com.tapdata.tm.TMApplication - admin access code is 3324cfdf-7d3e-4792-bd32-571638d4562f
2025-06-06 16:42:56.618 [tm] [main] INFO  org.reflections.Reflections - Reflections took 140 ms to scan 19 urls, producing 62 keys and 1137 values
2025-06-06 16:42:56.716 [tm] [main] INFO  org.reflections.Reflections - Reflections took 14 ms to scan 12 urls, producing 15 keys and 149 values
2025-06-06 16:42:56.719 [tm] [main] INFO  org.reflections.Reflections - Reflections took 2 ms to scan 1 urls, producing 10 keys and 24 values
2025-06-06 16:42:56.721 [tm] [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@17e8caf2, com.mongodb.Jep395RecordCodecProvider@104cf647, com.mongodb.KotlinCodecProvider@7488c183]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 16:42:56.726 [tm] [cluster-ClusterId{value='6842aa10a6d07a177167b4de', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=4479584, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 16:42:56 CST 2025, lastUpdateTimeNanos=195004680966041}
2025-06-06 16:42:56.789 [tm] [main] INFO  org.reflections.Reflections - Reflections took 1 ms to scan 1 urls, producing 4 keys and 9 values
2025-06-06 16:42:56.836 [tm] [main] INFO  PDK - IPHolder [Server ip is [************, **********, 127.0.0.1] by ipPrefix null ethPrefix null]
2025-06-06 16:42:57.013 [tm] [RMI TCP Connection(4)-127.0.0.1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 16:42:57.013 [tm] [RMI TCP Connection(4)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 16:42:57.015 [tm] [RMI TCP Connection(4)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-06 16:42:57.059 [tm] [RMI TCP Connection(2)-127.0.0.1] WARN  o.s.b.a.d.e.ElasticsearchReactiveHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.MonoCompletionStage] :
	reactor.core.publisher.Mono.fromFuture(Mono.java:629)
	org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClusterClient.health(ReactiveElasticsearchClusterClient.java:50)
Error has been observed at the following site(s):
	*__Mono.fromFuture ⇢ at org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClusterClient.health(ReactiveElasticsearchClusterClient.java:50)
	|_        Mono.map ⇢ at org.springframework.boot.actuate.data.elasticsearch.ElasticsearchReactiveHealthIndicator.doHealthCheck(ElasticsearchReactiveHealthIndicator.java:49)
Original Stack Trace:
		at java.base/sun.nio.ch.Net.pollConnect(Native Method)
		at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
		at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
		at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
		at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
		at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
		at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221)
		at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64)
		at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-06 16:42:57.394 [tm] [Thread-3] INFO  c.t.t.t.service.impl.LdpServiceImpl - Supplementary ldp task exception
2025-06-06 16:43:01.323 [tm] [127.0.0.1-86-cd3207ed-2bfc-4a91-9f7c-280588af94ca] INFO  c.t.t.o.g.c.OpenApiGeneratorController - Received code generation request: oas=https://petstore3.swagger.io/api/v3/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata
2025-06-06 16:43:01.324 [tm] [127.0.0.1-86-cd3207ed-2bfc-4a91-9f7c-280588af94ca] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Starting code generation with request parameters: CodeGenerationRequest(oas=https://petstore3.swagger.io/api/v3/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata)
2025-06-06 16:43:01.324 [tm] [127.0.0.1-86-cd3207ed-2bfc-4a91-9f7c-280588af94ca] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Generator parameters: CodeGenerationRequest(oas=https://petstore3.swagger.io/api/v3/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata), output dir: /var/folders/x5/j5713w351xsdlsjxq64311m00000gn/T/openapi-generator/53cac162-07a2-4a14-bc22-2755925b39d2
2025-06-06 16:43:01.324 [tm] [127.0.0.1-86-cd3207ed-2bfc-4a91-9f7c-280588af94ca] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Executing command: java -jar /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator/openapi-generator-cli.jar generate -i https://petstore3.swagger.io/api/v3/openapi.json -g java -o /var/folders/x5/j5713w351xsdlsjxq64311m00000gn/T/openapi-generator/53cac162-07a2-4a14-bc22-2755925b39d2 --invoker-package io.tapdata.sdk --api-package io.tapdata.sdk.api --model-package io.tapdata.sdk.model --artifact-id tapdata-sdk --group-id io.tapdata -t /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator
2025-06-06 16:43:05.006 [tm] [taskScheduler-37] INFO  o.s.s.a.AnnotationAsyncExecutionInterceptor - More than one TaskExecutor bean found within the context, and none is named 'taskExecutor'. Mark one of them as primary or name it 'taskExecutor' (possibly as an alias) in order to use it for async processing: [taskScheduler, NotificationExecutor]
2025-06-06 16:43:05.759 [tm] [127.0.0.1-86-cd3207ed-2bfc-4a91-9f7c-280588af94ca] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Code generation successful
2025-06-06 16:43:05.769 [tm] [127.0.0.1-86-cd3207ed-2bfc-4a91-9f7c-280588af94ca] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Code generation completed, file size: 134124 bytes
2025-06-06 16:45:00.070 [tm] [TaskAlarmScheduler-taskAgentAlarm] ERROR c.t.tm.schedule.TaskAlarmScheduler - taskAgentAlarm stopEgineMap data:{"sam_iengine":{"cpuLoad":0,"createAt":1749088057735,"createUser":"<EMAIL>","gitCommitId":"-","hostname":"Sam-macbook-m1x.local","id":{"date":**********000,"timestamp":**********},"lastUpdAt":1749113570469,"lastUpdBy":"62bc5008d4958d013d97c7a6","metricValues":{"HeapMemoryUsage":0.06081453338265419,"CpuUsage":0.03936814433695751},"pingTime":1749113570460,"processId":"sam_iengine","singletonLock":"67574fba-c1fb-46c1-8842-82de3f2eec6a","stopping":false,"totalThread":1,"usedMemory":654311424,"userId":"62bc5008d4958d013d97c7a6","version":"-","workerDate":1749113570457,"workerType":"connector"}}
2025-06-06 16:45:26.376 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:45:31.415 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:45:36.456 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:45:41.532 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:45:46.579 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:45:51.669 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:45:56.696 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:46:01.756 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:46:06.823 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:46:11.859 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:46:16.917 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:46:21.957 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:46:27.002 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:46:32.046 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:46:37.171 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:46:42.254 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:46:47.319 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:46:52.361 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:46:57.412 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:47:02.482 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:47:07.578 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:47:12.678 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:47:17.741 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:47:22.787 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:47:27.826 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:47:32.864 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:47:37.967 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:47:43.029 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:47:48.063 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:47:53.117 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:47:58.194 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:48:03.243 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:48:08.286 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:48:13.340 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:48:18.416 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:48:23.509 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:48:28.573 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:48:33.617 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:48:38.666 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:48:43.724 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:48:48.813 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:48:53.850 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:48:58.949 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:49:03.977 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:49:09.067 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:49:14.127 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:49:19.166 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:49:24.211 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:49:29.282 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:49:34.398 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:49:39.543 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:49:44.595 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:49:49.646 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:49:54.713 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:49:59.753 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:50:00.045 [tm] [TaskAlarmScheduler-taskAgentAlarm] ERROR c.t.tm.schedule.TaskAlarmScheduler - taskAgentAlarm stopEgineMap data:{"sam_iengine":{"cpuLoad":0,"createAt":1749088057735,"createUser":"<EMAIL>","gitCommitId":"-","hostname":"Sam-macbook-m1x.local","id":{"date":**********000,"timestamp":**********},"lastUpdAt":1749113570469,"lastUpdBy":"62bc5008d4958d013d97c7a6","metricValues":{"HeapMemoryUsage":0.06081453338265419,"CpuUsage":0.03936814433695751},"pingTime":1749113570460,"processId":"sam_iengine","singletonLock":"67574fba-c1fb-46c1-8842-82de3f2eec6a","stopping":false,"totalThread":1,"usedMemory":654311424,"userId":"62bc5008d4958d013d97c7a6","version":"-","workerDate":1749113570457,"workerType":"connector"}}
2025-06-06 16:50:04.833 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:50:09.896 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:50:14.945 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:50:19.994 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:50:25.030 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:50:30.095 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:50:35.145 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:50:40.200 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:50:45.249 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:50:50.307 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:50:55.364 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:51:00.413 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:51:05.476 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:51:10.524 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:51:15.576 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:51:20.628 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:51:25.685 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:51:30.747 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:51:35.782 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:51:40.845 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:51:45.902 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:51:50.953 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:51:56.018 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:52:01.066 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:52:06.115 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:52:11.159 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:52:16.210 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:52:21.269 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:52:26.333 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:52:31.388 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:52:36.433 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:52:41.496 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:52:46.542 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:52:51.583 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:52:56.642 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:53:01.708 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:53:06.765 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:53:11.835 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:53:16.884 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:53:21.954 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:53:27.042 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:53:32.084 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:53:37.123 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:53:42.169 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:53:47.222 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:53:52.285 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:53:57.336 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:54:02.371 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:54:07.428 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:54:12.513 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:54:17.585 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:54:22.683 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:54:27.737 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:54:32.795 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:54:37.851 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:54:42.910 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:54:47.974 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:54:53.046 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:54:58.087 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:55:00.139 [tm] [TaskAlarmScheduler-taskAgentAlarm] ERROR c.t.tm.schedule.TaskAlarmScheduler - taskAgentAlarm stopEgineMap data:{"sam_iengine":{"cpuLoad":0,"createAt":1749088057735,"createUser":"<EMAIL>","gitCommitId":"-","hostname":"Sam-macbook-m1x.local","id":{"date":**********000,"timestamp":**********},"lastUpdAt":1749113570469,"lastUpdBy":"62bc5008d4958d013d97c7a6","metricValues":{"HeapMemoryUsage":0.06081453338265419,"CpuUsage":0.03936814433695751},"pingTime":1749113570460,"processId":"sam_iengine","singletonLock":"67574fba-c1fb-46c1-8842-82de3f2eec6a","stopping":false,"totalThread":1,"usedMemory":654311424,"userId":"62bc5008d4958d013d97c7a6","version":"-","workerDate":1749113570457,"workerType":"connector"}}
2025-06-06 16:55:03.151 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:55:08.202 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:55:13.265 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:55:18.319 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:55:23.374 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:55:28.413 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:55:33.471 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:55:38.506 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:55:43.560 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:55:48.635 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:55:53.689 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:55:58.820 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:56:03.855 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:56:08.917 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:56:13.975 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:56:19.034 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:56:24.091 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:56:29.133 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:56:34.178 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:56:39.212 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:56:44.254 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:56:49.291 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:56:54.334 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:56:59.370 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:57:04.426 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:57:09.480 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:57:14.537 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:57:19.571 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:57:24.605 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:57:29.637 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:57:34.682 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:57:39.719 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:57:44.788 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:57:49.852 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:57:54.890 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:57:59.936 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:58:04.994 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:58:10.041 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:58:15.093 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:58:20.145 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:58:25.197 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:58:30.243 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:58:35.290 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:58:40.344 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:58:45.393 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:58:50.462 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:58:55.518 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:59:00.567 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:59:05.614 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:59:10.656 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:59:15.687 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:59:20.720 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:59:25.770 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:59:30.813 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:59:35.865 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:59:40.914 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:59:45.966 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:59:51.002 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 16:59:56.063 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:00:00.028 [tm] [TaskAlarmScheduler-taskAgentAlarm] ERROR c.t.tm.schedule.TaskAlarmScheduler - taskAgentAlarm stopEgineMap data:{"sam_iengine":{"cpuLoad":0,"createAt":1749088057735,"createUser":"<EMAIL>","gitCommitId":"-","hostname":"Sam-macbook-m1x.local","id":{"date":**********000,"timestamp":**********},"lastUpdAt":1749113570469,"lastUpdBy":"62bc5008d4958d013d97c7a6","metricValues":{"HeapMemoryUsage":0.06081453338265419,"CpuUsage":0.03936814433695751},"pingTime":1749113570460,"processId":"sam_iengine","singletonLock":"67574fba-c1fb-46c1-8842-82de3f2eec6a","stopping":false,"totalThread":1,"usedMemory":654311424,"userId":"62bc5008d4958d013d97c7a6","version":"-","workerDate":1749113570457,"workerType":"connector"}}
2025-06-06 17:00:01.098 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:00:06.138 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:00:11.193 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:00:16.230 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:00:21.276 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:00:26.333 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:00:31.416 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:00:36.465 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:00:41.488 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:00:46.540 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:00:51.593 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:00:56.630 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:01:01.690 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:01:06.728 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:01:11.781 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:01:16.844 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:01:21.909 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:01:26.954 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:01:32.011 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:01:37.072 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:01:42.100 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:01:47.149 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:01:52.199 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:01:57.258 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:02:02.305 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:02:07.355 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:02:12.396 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:02:17.444 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:02:22.498 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:02:27.552 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:02:32.602 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:02:37.655 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:02:42.711 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:02:47.777 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:02:52.831 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:02:57.890 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:03:02.949 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:03:07.987 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:03:13.112 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:03:18.183 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:03:23.260 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:03:28.332 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:03:33.413 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:03:38.484 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:03:43.537 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:03:48.592 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:03:53.631 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:03:58.683 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:04:03.737 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:04:08.790 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:04:13.865 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:04:18.909 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:04:23.964 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:04:29.029 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:04:34.112 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:04:39.177 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:04:44.243 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:04:49.281 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:04:54.326 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:04:59.367 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:05:00.054 [tm] [TaskAlarmScheduler-taskAgentAlarm] ERROR c.t.tm.schedule.TaskAlarmScheduler - taskAgentAlarm stopEgineMap data:{"sam_iengine":{"cpuLoad":0,"createAt":1749088057735,"createUser":"<EMAIL>","gitCommitId":"-","hostname":"Sam-macbook-m1x.local","id":{"date":**********000,"timestamp":**********},"lastUpdAt":1749113570469,"lastUpdBy":"62bc5008d4958d013d97c7a6","metricValues":{"HeapMemoryUsage":0.06081453338265419,"CpuUsage":0.03936814433695751},"pingTime":1749113570460,"processId":"sam_iengine","singletonLock":"67574fba-c1fb-46c1-8842-82de3f2eec6a","stopping":false,"totalThread":1,"usedMemory":654311424,"userId":"62bc5008d4958d013d97c7a6","version":"-","workerDate":1749113570457,"workerType":"connector"}}
2025-06-06 17:05:04.402 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:05:09.453 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:05:14.500 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:05:19.549 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:05:24.617 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:05:29.639 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:05:34.687 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:05:39.746 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:05:44.788 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:05:49.842 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:05:54.890 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:05:59.944 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:06:04.999 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:06:10.047 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:06:15.095 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:06:20.140 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:06:25.196 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:06:30.250 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:06:35.301 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:06:40.370 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:06:45.437 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:06:50.491 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:06:55.544 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:07:00.592 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:07:05.637 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:07:10.698 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:07:15.758 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:07:20.817 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:07:25.860 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:07:30.913 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:07:35.954 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:07:40.999 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:07:46.053 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:07:51.106 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:07:56.162 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:08:01.212 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:08:06.259 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:08:11.294 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:08:16.337 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:08:21.395 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:08:26.445 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:08:31.491 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:08:36.548 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:08:41.592 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:08:46.652 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:08:51.695 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:08:56.740 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:09:01.786 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:09:06.832 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:09:11.878 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:09:16.933 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:09:21.982 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:09:27.053 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:09:32.089 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:09:37.147 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:09:42.192 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:09:47.247 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:09:52.306 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:09:57.361 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:10:00.059 [tm] [TaskAlarmScheduler-taskAgentAlarm] ERROR c.t.tm.schedule.TaskAlarmScheduler - taskAgentAlarm stopEgineMap data:{"sam_iengine":{"cpuLoad":0,"createAt":1749088057735,"createUser":"<EMAIL>","gitCommitId":"-","hostname":"Sam-macbook-m1x.local","id":{"date":**********000,"timestamp":**********},"lastUpdAt":1749113570469,"lastUpdBy":"62bc5008d4958d013d97c7a6","metricValues":{"HeapMemoryUsage":0.06081453338265419,"CpuUsage":0.03936814433695751},"pingTime":1749113570460,"processId":"sam_iengine","singletonLock":"67574fba-c1fb-46c1-8842-82de3f2eec6a","stopping":false,"totalThread":1,"usedMemory":654311424,"userId":"62bc5008d4958d013d97c7a6","version":"-","workerDate":1749113570457,"workerType":"connector"}}
2025-06-06 17:10:02.419 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:10:07.481 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:10:12.532 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:10:17.570 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:10:22.627 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:10:27.687 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:10:32.797 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:10:37.883 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:10:42.903 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:10:47.938 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:10:52.977 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:10:58.019 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:11:03.048 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:11:08.104 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:11:13.140 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:11:18.237 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:11:23.277 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:11:28.335 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:11:33.365 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:11:38.402 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:11:43.450 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:11:48.502 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:11:53.540 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:11:58.576 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:12:03.605 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:12:08.644 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:12:13.685 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:12:18.749 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:12:23.771 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:12:28.809 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:12:33.850 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:12:38.884 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:12:43.923 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:12:48.955 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:12:53.987 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:12:59.022 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:13:04.086 [tm] [127.0.0.1-88-6ac8877d-5645-42cd-8d13-8d6c93d40067] INFO  c.t.t.o.g.c.OpenApiGeneratorController - Received code generation request: oas=https://petstore3.swagger.io/api/v3/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata
2025-06-06 17:13:04.086 [tm] [127.0.0.1-88-6ac8877d-5645-42cd-8d13-8d6c93d40067] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Starting code generation with request parameters: CodeGenerationRequest(oas=https://petstore3.swagger.io/api/v3/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata)
2025-06-06 17:13:04.087 [tm] [127.0.0.1-88-6ac8877d-5645-42cd-8d13-8d6c93d40067] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Generator parameters: CodeGenerationRequest(oas=https://petstore3.swagger.io/api/v3/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata), output dir: /var/folders/x5/j5713w351xsdlsjxq64311m00000gn/T/openapi-generator/9aae6261-fc8b-4a08-ac93-62bf78deaea2
2025-06-06 17:13:04.087 [tm] [127.0.0.1-88-6ac8877d-5645-42cd-8d13-8d6c93d40067] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Executing command: java -jar /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator/openapi-generator-cli.jar generate -i https://petstore3.swagger.io/api/v3/openapi.json -g java -o /var/folders/x5/j5713w351xsdlsjxq64311m00000gn/T/openapi-generator/9aae6261-fc8b-4a08-ac93-62bf78deaea2 --invoker-package io.tapdata.sdk --api-package io.tapdata.sdk.api --model-package io.tapdata.sdk.model --artifact-id tapdata-sdk --group-id io.tapdata -t /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator
2025-06-06 17:13:04.089 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:13:08.993 [tm] [127.0.0.1-88-6ac8877d-5645-42cd-8d13-8d6c93d40067] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Code generation successful
2025-06-06 17:13:09.002 [tm] [127.0.0.1-88-6ac8877d-5645-42cd-8d13-8d6c93d40067] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Code generation completed, file size: 134134 bytes
2025-06-06 17:13:09.235 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:13:14.326 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:13:19.370 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:13:24.412 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:13:29.458 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:13:34.484 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:13:39.534 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:13:44.307 [tm] [127.0.0.1-89-7a10ffa8-6265-4595-9f65-2422ab2f9d80] INFO  c.t.t.o.g.c.OpenApiGeneratorController - Received code generation request: oas=http://*************:3080/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata
2025-06-06 17:13:44.307 [tm] [127.0.0.1-89-7a10ffa8-6265-4595-9f65-2422ab2f9d80] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Starting code generation with request parameters: CodeGenerationRequest(oas=http://*************:3080/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata)
2025-06-06 17:13:44.307 [tm] [127.0.0.1-89-7a10ffa8-6265-4595-9f65-2422ab2f9d80] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Generator parameters: CodeGenerationRequest(oas=http://*************:3080/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata), output dir: /var/folders/x5/j5713w351xsdlsjxq64311m00000gn/T/openapi-generator/6a2cec20-2246-4a21-87cc-f88c093d15b7
2025-06-06 17:13:44.308 [tm] [127.0.0.1-89-7a10ffa8-6265-4595-9f65-2422ab2f9d80] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Executing command: java -jar /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator/openapi-generator-cli.jar generate -i http://*************:3080/openapi.json -g java -o /var/folders/x5/j5713w351xsdlsjxq64311m00000gn/T/openapi-generator/6a2cec20-2246-4a21-87cc-f88c093d15b7 --invoker-package io.tapdata.sdk --api-package io.tapdata.sdk.api --model-package io.tapdata.sdk.model --artifact-id tapdata-sdk --group-id io.tapdata -t /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator
2025-06-06 17:13:44.564 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:13:45.605 [tm] [127.0.0.1-89-7a10ffa8-6265-4595-9f65-2422ab2f9d80] ERROR c.t.t.o.g.s.OpenApiGeneratorService - Code generation failed, exit code: 1, output: Exception in thread "main" org.openapitools.codegen.SpecValidationException: There were issues with the specification. The option can be disabled via validateSpec (Maven/Gradle) or --skip-validate-spec (CLI).
 | Error count: 18, Warning count: 0
Errors: 
	-attribute paths.'/api/k1p7otg93hq/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/api/f3ylxxv7d7k/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/status'(get).responses.200.content is not of type `object`
	-attribute components.schemas.Xhebq4repiy.additionalProperties is not of type `object`
	-attribute paths.'/api/xhebq4repiy'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute components.schemas.Earmark.additionalProperties is not of type `object`
	-attribute components.schemas.F3ylxxv7d7k.additionalProperties is not of type `object`
	-attribute paths.'/api/v2/earmark/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/api/V1/CUSTOMER/p27gcugvrzq/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/api/v2/earmark'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/api/f3ylxxv7d7k'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute components.schemas.K1p7otg93hq.additionalProperties is not of type `object`
	-attribute paths.'/openapi-readOnly.json'(get).responses.200.content is not of type `object`
	-attribute paths.'/api/k1p7otg93hq'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute components.schemas.CUSTOMERp27gcugvrzq.additionalProperties is not of type `object`
	-attribute paths.'/api/xhebq4repiy/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/api/V1/CUSTOMER/p27gcugvrzq'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/openapi-generator'(get).responses.200.content is not of type `object`

	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:606)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)

2025-06-06 17:13:45.606 [tm] [127.0.0.1-89-7a10ffa8-6265-4595-9f65-2422ab2f9d80] ERROR c.t.t.o.g.c.OpenApiGeneratorController - Code generation failed
com.tapdata.tm.openapi.generator.exception.CodeGenerationException: Code generation failed: Exception in thread "main" org.openapitools.codegen.SpecValidationException: There were issues with the specification. The option can be disabled via validateSpec (Maven/Gradle) or --skip-validate-spec (CLI).
 | Error count: 18, Warning count: 0
Errors: 
	-attribute paths.'/api/k1p7otg93hq/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/api/f3ylxxv7d7k/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/status'(get).responses.200.content is not of type `object`
	-attribute components.schemas.Xhebq4repiy.additionalProperties is not of type `object`
	-attribute paths.'/api/xhebq4repiy'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute components.schemas.Earmark.additionalProperties is not of type `object`
	-attribute components.schemas.F3ylxxv7d7k.additionalProperties is not of type `object`
	-attribute paths.'/api/v2/earmark/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/api/V1/CUSTOMER/p27gcugvrzq/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/api/v2/earmark'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/api/f3ylxxv7d7k'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute components.schemas.K1p7otg93hq.additionalProperties is not of type `object`
	-attribute paths.'/openapi-readOnly.json'(get).responses.200.content is not of type `object`
	-attribute paths.'/api/k1p7otg93hq'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute components.schemas.CUSTOMERp27gcugvrzq.additionalProperties is not of type `object`
	-attribute paths.'/api/xhebq4repiy/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/api/V1/CUSTOMER/p27gcugvrzq'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/openapi-generator'(get).responses.200.content is not of type `object`

	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:606)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)

	at com.tapdata.tm.openapi.generator.service.OpenApiGeneratorService.executeGenerator(OpenApiGeneratorService.java:159)
	at com.tapdata.tm.openapi.generator.service.OpenApiGeneratorService.generateCode(OpenApiGeneratorService.java:59)
	at com.tapdata.tm.openapi.generator.controller.OpenApiGeneratorController.generateCode(OpenApiGeneratorController.java:84)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.tapdata.tm.openapi.generator.controller.OpenApiGeneratorController$$SpringCGLIB$$0.generateCode(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.tapdata.tm.base.filter.RequestFilter.doFilter(RequestFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter.doFilterInternal(DefaultLogoutPageGeneratingFilter.java:58)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:220)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:206)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultResourcesFilter.doFilter(DefaultResourcesFilter.java:72)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-06 17:13:45.609 [tm] [127.0.0.1-89-7a10ffa8-6265-4595-9f65-2422ab2f9d80] ERROR c.t.tm.base.handler.ExceptionHandler - System error
com.tapdata.tm.openapi.generator.exception.CodeGenerationException: Code generation failed: Exception in thread "main" org.openapitools.codegen.SpecValidationException: There were issues with the specification. The option can be disabled via validateSpec (Maven/Gradle) or --skip-validate-spec (CLI).
 | Error count: 18, Warning count: 0
Errors: 
	-attribute paths.'/api/k1p7otg93hq/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/api/f3ylxxv7d7k/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/status'(get).responses.200.content is not of type `object`
	-attribute components.schemas.Xhebq4repiy.additionalProperties is not of type `object`
	-attribute paths.'/api/xhebq4repiy'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute components.schemas.Earmark.additionalProperties is not of type `object`
	-attribute components.schemas.F3ylxxv7d7k.additionalProperties is not of type `object`
	-attribute paths.'/api/v2/earmark/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/api/V1/CUSTOMER/p27gcugvrzq/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/api/v2/earmark'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/api/f3ylxxv7d7k'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute components.schemas.K1p7otg93hq.additionalProperties is not of type `object`
	-attribute paths.'/openapi-readOnly.json'(get).responses.200.content is not of type `object`
	-attribute paths.'/api/k1p7otg93hq'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute components.schemas.CUSTOMERp27gcugvrzq.additionalProperties is not of type `object`
	-attribute paths.'/api/xhebq4repiy/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/api/V1/CUSTOMER/p27gcugvrzq'(get).parameters.[filter].schemas.additionalProperties is not of type `object`
	-attribute paths.'/openapi-generator'(get).responses.200.content is not of type `object`

	at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:606)
	at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)
	at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)

	at com.tapdata.tm.openapi.generator.service.OpenApiGeneratorService.executeGenerator(OpenApiGeneratorService.java:159)
	at com.tapdata.tm.openapi.generator.service.OpenApiGeneratorService.generateCode(OpenApiGeneratorService.java:59)
	at com.tapdata.tm.openapi.generator.controller.OpenApiGeneratorController.generateCode(OpenApiGeneratorController.java:84)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.tapdata.tm.openapi.generator.controller.OpenApiGeneratorController$$SpringCGLIB$$0.generateCode(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.tapdata.tm.base.filter.RequestFilter.doFilter(RequestFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter.doFilterInternal(DefaultLogoutPageGeneratingFilter.java:58)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:220)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:206)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultResourcesFilter.doFilter(DefaultResourcesFilter.java:72)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-06 17:13:45.615 [tm] [127.0.0.1-89-7a10ffa8-6265-4595-9f65-2422ab2f9d80] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Failure in @ExceptionHandler com.tapdata.tm.base.handler.ExceptionHandler#handlerException(Throwable, HttpServletRequest, HttpServletResponse)
org.springframework.web.HttpMediaTypeNotAcceptableException: No acceptable representation
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:291)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:208)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:136)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:471)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:73)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:182)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1359)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1161)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1106)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.tapdata.tm.base.filter.RequestFilter.doFilter(RequestFilter.java:73)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter.doFilterInternal(DefaultLogoutPageGeneratingFilter.java:58)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:220)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:206)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.ui.DefaultResourcesFilter.doFilter(DefaultResourcesFilter.java:72)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:221)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-06 17:13:45.624 [tm] [127.0.0.1-89-7a10ffa8-6265-4595-9f65-2422ab2f9d80] ERROR c.t.tm.base.filter.RequestFilter - Process request error jakarta.servlet.ServletException: Request processing failed: com.tapdata.tm.openapi.generator.exception.CodeGenerationException: Code generation failed: Exception in thread "main" org.openapitools.codegen.SpecValidationException: There were issues with the specification. The option can be disabled via validateSpec (Maven/Gradle) or --skip-validate-spec (CLI).  | Error count: 18, Warning count: 0 Errors:   -attribute paths.'/api/k1p7otg93hq/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`  -attribute paths.'/api/f3ylxxv7d7k/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`  -attribute paths.'/status'(get).responses.200.content is not of type `object`  -attribute components.schemas.Xhebq4repiy.additionalProperties is not of type `object`  -attribute paths.'/api/xhebq4repiy'(get).parameters.[filter].schemas.additionalProperties is not of type `object`  -attribute components.schemas.Earmark.additionalProperties is not of type `object`  -attribute components.schemas.F3ylxxv7d7k.additionalProperties is not of type `object`  -attribute paths.'/api/v2/earmark/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`  -attribute paths.'/api/V1/CUSTOMER/p27gcugvrzq/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`  -attribute paths.'/api/v2/earmark'(get).parameters.[filter].schemas.additionalProperties is not of type `object`  -attribute paths.'/api/f3ylxxv7d7k'(get).parameters.[filter].schemas.additionalProperties is not of type `object`  -attribute components.schemas.K1p7otg93hq.additionalProperties is not of type `object`  -attribute paths.'/openapi-readOnly.json'(get).responses.200.content is not of type `object`  -attribute paths.'/api/k1p7otg93hq'(get).parameters.[filter].schemas.additionalProperties is not of type `object`  -attribute components.schemas.CUSTOMERp27gcugvrzq.additionalProperties is not of type `object`  -attribute paths.'/api/xhebq4repiy/count'(get).parameters.[filter].schemas.additionalProperties is not of type `object`  -attribute paths.'/api/V1/CUSTOMER/p27gcugvrzq'(get).parameters.[filter].schemas.additionalProperties is not of type `object`  -attribute paths.'/openapi-generator'(get).responses.200.content is not of type `object`   at org.openapitools.codegen.config.CodegenConfigurator.toClientOptInput(CodegenConfigurator.java:606)  at org.openapitools.codegen.cmd.Generate.run(Generate.java:395)  at org.openapitools.codegen.OpenAPIGenerator.main(OpenAPIGenerator.java:60)   at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)  at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)  at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)  at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)  at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)  at org.apache.catalina.core.Ap
2025-06-06 17:13:49.603 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:13:54.626 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:13:59.663 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:14:04.701 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:14:09.749 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:14:14.789 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:14:19.827 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:14:24.880 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:14:29.912 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:14:34.946 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:14:39.974 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:14:45.029 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:14:50.049 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:14:55.073 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:15:00.044 [tm] [TaskAlarmScheduler-taskAgentAlarm] ERROR c.t.tm.schedule.TaskAlarmScheduler - taskAgentAlarm stopEgineMap data:{"sam_iengine":{"cpuLoad":0,"createAt":1749088057735,"createUser":"<EMAIL>","gitCommitId":"-","hostname":"Sam-macbook-m1x.local","id":{"date":**********000,"timestamp":**********},"lastUpdAt":1749113570469,"lastUpdBy":"62bc5008d4958d013d97c7a6","metricValues":{"HeapMemoryUsage":0.06081453338265419,"CpuUsage":0.03936814433695751},"pingTime":1749113570460,"processId":"sam_iengine","singletonLock":"67574fba-c1fb-46c1-8842-82de3f2eec6a","stopping":false,"totalThread":1,"usedMemory":654311424,"userId":"62bc5008d4958d013d97c7a6","version":"-","workerDate":1749113570457,"workerType":"connector"}}
2025-06-06 17:15:00.144 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:15:05.182 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:15:10.226 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:15:15.265 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:15:20.313 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:15:25.362 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:15:30.409 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:15:35.459 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:15:40.503 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:15:45.529 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:15:50.569 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:15:55.630 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:16:00.665 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:16:05.719 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:16:10.796 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:16:15.833 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:16:20.881 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:16:25.936 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:16:30.958 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:16:35.987 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:16:41.033 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:16:46.074 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:16:51.102 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:16:56.163 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:17:01.229 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:17:06.300 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:17:11.364 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:17:14.613 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-06 17:17:14.614 [tm] [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-06 17:17:15.121 [tm] [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-06 17:17:15.138 [tm] [SpringApplicationShutdownHook] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2025-06-06 17:17:15.138 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-06 17:17:15.138 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-06 17:17:15.138 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-06 17:17:19.441 [tm] [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-06 17:17:19.481 [tm] [main] INFO  com.tapdata.tm.TMApplication - Starting TMApplication using Java 17.0.14 with PID 17563 (/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes started by samuel in /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17)
2025-06-06 17:17:19.482 [tm] [main] INFO  com.tapdata.tm.TMApplication - The following 2 profiles are active: "idaas", "default"
2025-06-06 17:17:20.510 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 17:17:20.510 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-06 17:17:20.754 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 238 ms. Found 2 MongoDB repository interfaces.
2025-06-06 17:17:20.755 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 17:17:20.756 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-06 17:17:20.759 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 MongoDB repository interfaces.
2025-06-06 17:17:20.759 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 17:17:20.759 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-06 17:17:20.761 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 MongoDB repository interfaces.
2025-06-06 17:17:20.769 [tm] [main] INFO  c.t.t.w.c.WebSocketHandlerImportBeanRegistrar - The number of webSocketHandler beans successfully registered: 17
2025-06-06 17:17:21.098 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 17:17:21.098 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 17:17:21.151 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.task.repository.TaskDagCheckLogRepository; If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository
2025-06-06 17:17:21.151 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.Settings.repository.SettingsRepository; If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository
2025-06-06 17:17:21.151 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 52 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 17:17:21.154 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 17:17:21.154 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 17:17:21.199 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.task.repository.TaskDagCheckLogRepository; If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository
2025-06-06 17:17:21.199 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.Settings.repository.SettingsRepository; If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository
2025-06-06 17:17:21.199 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 43 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 17:17:21.918 [tm] [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 3000 (http)
2025-06-06 17:17:21.925 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - The ["http-nio-3000"] connector has been configured to support HTTP upgrade to [h2c]
2025-06-06 17:17:21.925 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-3000"]
2025-06-06 17:17:21.926 [tm] [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 17:17:21.926 [tm] [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.40]
2025-06-06 17:17:21.970 [tm] [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 17:17:21.970 [tm] [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2457 ms
2025-06-06 17:17:22.159 [tm] [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-data", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@282aea3c, com.mongodb.Jep395RecordCodecProvider@54a5799f, com.mongodb.KotlinCodecProvider@162e29a1]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 17:17:22.177 [tm] [cluster-ClusterId{value='6842b222c517f43351ca5ce7', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=15988625, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 17:17:14 CST 2025, lastUpdateTimeNanos=197070130050375}
2025-06-06 17:17:23.936 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@282aea3c, com.mongodb.Jep395RecordCodecProvider@54a5799f, com.mongodb.KotlinCodecProvider@162e29a1]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 17:17:23.940 [tm] [cluster-ClusterId{value='6842b223c517f43351ca5ce8', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=4231041, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 17:17:14 CST 2025, lastUpdateTimeNanos=197071894704208}
2025-06-06 17:17:24.326 [tm] [main] INFO  c.t.t.c.component.ProductComponent - Current product type settings is DAAS.
2025-06-06 17:17:24.339 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@282aea3c, com.mongodb.Jep395RecordCodecProvider@54a5799f, com.mongodb.KotlinCodecProvider@162e29a1]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 17:17:24.341 [tm] [cluster-ClusterId{value='6842b224c517f43351ca5ce9', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=2482459, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 17:17:14 CST 2025, lastUpdateTimeNanos=197072295927708}
2025-06-06 17:17:24.343 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.cluster - Waiting for server to become available for operation with ID 13. Remaining time: 29998 ms. Selector: ReadPreferenceServerSelector{readPreference=primary}, topology description: {type=UNKNOWN, servers=[{address=localhost:27017, type=UNKNOWN, state=CONNECTING}].
2025-06-06 17:17:26.367 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-06-06 17:17:26.376 [tm] [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-06 17:17:26.376 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v2.5.0 created.
2025-06-06 17:17:26.377 [tm] [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-06-06 17:17:26.377 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-06 17:17:26.377 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-06 17:17:26.377 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.5.0
2025-06-06 17:17:26.377 [tm] [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@29e40db1
2025-06-06 17:17:26.849 [tm] [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name userServiceImpl
2025-06-06 17:17:27.457 [tm] [main] WARN  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Multiple @RequestMapping annotations found on public com.tapdata.tm.base.dto.ResponseMessage com.tapdata.tm.message.controller.MessageController.read(java.lang.String,java.lang.String) throws java.io.IOException, but only the first will be used: [@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={}, name="", params={}, path={}, produces={}, value={}), @org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name="", params={}, path={}, produces={}, value={})]
2025-06-06 17:17:28.476 [tm] [main] WARN  o.s.d.e.c.m.SimpleElasticsearchPersistentProperty - Unsupported type 'class java.lang.Object' for date property 'date'.
2025-06-06 17:17:28.477 [tm] [main] WARN  o.s.d.e.c.m.SimpleElasticsearchPersistentProperty - Unsupported type 'class java.lang.Object' for date property 'millis'.
2025-06-06 17:17:28.600 [tm] [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoints beneath base path '/actuator'
2025-06-06 17:17:28.749 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-3000"]
2025-06-06 17:17:28.758 [tm] [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 3000 (http) with context path '/'
2025-06-06 17:17:28.759 [tm] [main] INFO  o.s.s.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-06-06 17:17:28.759 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-06 17:17:28.778 [tm] [main] INFO  c.t.t.s.u.StateMachineProcessorManager - Urls path: /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/com/tapdata/tm/statemachine/processor, protocol: file
2025-06-06 17:17:28.796 [tm] [main] INFO  com.tapdata.tm.TMApplication - Started TMApplication in 9.61 seconds (process running for 10.422)
2025-06-06 17:17:28.880 [tm] [main] INFO  com.tapdata.tm.init.PatchesRunner - Not have any DAAS patches, current version is 4.0-1
2025-06-06 17:17:28.941 [tm] [main] INFO  com.tapdata.tm.init.InitLogMap - 
  _______              _       _           _                    _             _                                _      _           _ 
 |__   __|            | |     | |         | |                  | |           | |                              | |    | |         | |
    | | __ _ _ __   __| | __ _| |_ __ _   | |_ _ __ ___     ___| |_ __ _ _ __| |_     ___ ___  _ __ ___  _ __ | | ___| |_ ___  __| |
    | |/ _` | '_ \ / _` |/ _` | __/ _` |  | __| '_ ` _ \   / __| __/ _` | '__| __|   / __/ _ \| '_ ` _ \| '_ \| |/ _ \ __/ _ \/ _` |
    | | (_| | |_) | (_| | (_| | || (_| |  | |_| | | | | |  \__ \ || (_| | |  | |_   | (_| (_) | | | | | | |_) | |  __/ ||  __/ (_| |
    |_|\__,_| .__/ \__,_|\__,_|\__\__,_|   \__|_| |_| |_|  |___/\__\__,_|_|   \__|   \___\___/|_| |_| |_| .__/|_|\___|\__\___|\__,_|
            | |                                                                                         | |                         
            |_|                                                                                         |_|                         
2025-06-06 17:17:28.942 [tm] [Thread-1] INFO  com.tapdata.tm.TMApplication - No need to start TCM
2025-06-06 17:17:28.947 [tm] [main] INFO  com.tapdata.tm.TMApplication - admin access code is 3324cfdf-7d3e-4792-bd32-571638d4562f
2025-06-06 17:17:29.119 [tm] [main] INFO  org.reflections.Reflections - Reflections took 155 ms to scan 19 urls, producing 62 keys and 1137 values
2025-06-06 17:17:29.223 [tm] [main] INFO  org.reflections.Reflections - Reflections took 16 ms to scan 12 urls, producing 15 keys and 149 values
2025-06-06 17:17:29.226 [tm] [main] INFO  org.reflections.Reflections - Reflections took 2 ms to scan 1 urls, producing 10 keys and 24 values
2025-06-06 17:17:29.228 [tm] [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@282aea3c, com.mongodb.Jep395RecordCodecProvider@54a5799f, com.mongodb.KotlinCodecProvider@162e29a1]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 17:17:29.231 [tm] [cluster-ClusterId{value='6842b229c517f43351ca5cea', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=2736042, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 17:17:28 CST 2025, lastUpdateTimeNanos=197077185776583}
2025-06-06 17:17:29.296 [tm] [main] INFO  org.reflections.Reflections - Reflections took 2 ms to scan 1 urls, producing 4 keys and 9 values
2025-06-06 17:17:29.352 [tm] [main] INFO  PDK - IPHolder [Server ip is [************, **********, 127.0.0.1] by ipPrefix null ethPrefix null]
2025-06-06 17:17:29.393 [tm] [RMI TCP Connection(4)-127.0.0.1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 17:17:29.393 [tm] [RMI TCP Connection(4)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 17:17:29.395 [tm] [RMI TCP Connection(4)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-06 17:17:29.444 [tm] [RMI TCP Connection(2)-127.0.0.1] WARN  o.s.b.a.d.e.ElasticsearchReactiveHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.MonoCompletionStage] :
	reactor.core.publisher.Mono.fromFuture(Mono.java:629)
	org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClusterClient.health(ReactiveElasticsearchClusterClient.java:50)
Error has been observed at the following site(s):
	*__Mono.fromFuture ⇢ at org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClusterClient.health(ReactiveElasticsearchClusterClient.java:50)
	|_        Mono.map ⇢ at org.springframework.boot.actuate.data.elasticsearch.ElasticsearchReactiveHealthIndicator.doHealthCheck(ElasticsearchReactiveHealthIndicator.java:49)
Original Stack Trace:
		at java.base/sun.nio.ch.Net.pollConnect(Native Method)
		at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
		at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
		at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
		at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
		at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
		at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221)
		at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64)
		at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-06 17:17:29.908 [tm] [Thread-3] INFO  c.t.t.t.service.impl.LdpServiceImpl - Supplementary ldp task exception
2025-06-06 17:17:33.897 [tm] [127.0.0.1-86-1f05340e-3da9-4085-a611-f55dc6fd1391] INFO  c.t.t.o.g.c.OpenApiGeneratorController - Received code generation request: oas=http://*************:3080/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata
2025-06-06 17:17:33.897 [tm] [127.0.0.1-86-1f05340e-3da9-4085-a611-f55dc6fd1391] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Starting code generation with request parameters: CodeGenerationRequest(oas=http://*************:3080/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata)
2025-06-06 17:17:33.898 [tm] [127.0.0.1-86-1f05340e-3da9-4085-a611-f55dc6fd1391] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Generator parameters: CodeGenerationRequest(oas=http://*************:3080/openapi.json, lan=java, packageName=io.tapdata.sdk, artifactId=tapdata-sdk, groupId=io.tapdata), output dir: /var/folders/x5/j5713w351xsdlsjxq64311m00000gn/T/openapi-generator/4f02a858-adfb-4ada-82fc-8b1e9b5c64e9
2025-06-06 17:17:33.898 [tm] [127.0.0.1-86-1f05340e-3da9-4085-a611-f55dc6fd1391] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Executing command: java -jar /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator/openapi-generator-cli.jar generate -i http://*************:3080/openapi.json -g java -o /var/folders/x5/j5713w351xsdlsjxq64311m00000gn/T/openapi-generator/4f02a858-adfb-4ada-82fc-8b1e9b5c64e9 --invoker-package io.tapdata.sdk --api-package io.tapdata.sdk.api --model-package io.tapdata.sdk.model --artifact-id tapdata-sdk --group-id io.tapdata --skip-validate-spec -t /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/openapi-generator
2025-06-06 17:17:36.635 [tm] [127.0.0.1-86-1f05340e-3da9-4085-a611-f55dc6fd1391] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Code generation successful
2025-06-06 17:17:36.652 [tm] [127.0.0.1-86-1f05340e-3da9-4085-a611-f55dc6fd1391] INFO  c.t.t.o.g.s.OpenApiGeneratorService - Code generation completed, file size: 160005 bytes
2025-06-06 17:18:05.008 [tm] [taskScheduler-23] INFO  o.s.s.a.AnnotationAsyncExecutionInterceptor - More than one TaskExecutor bean found within the context, and none is named 'taskExecutor'. Mark one of them as primary or name it 'taskExecutor' (possibly as an alias) in order to use it for async processing: [taskScheduler, NotificationExecutor]
2025-06-06 17:19:58.817 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:20:00.079 [tm] [TaskAlarmScheduler-taskAgentAlarm] ERROR c.t.tm.schedule.TaskAlarmScheduler - taskAgentAlarm stopEgineMap data:{"sam_iengine":{"cpuLoad":0,"createAt":1749088057735,"createUser":"<EMAIL>","gitCommitId":"-","hostname":"Sam-macbook-m1x.local","id":{"date":**********000,"timestamp":**********},"lastUpdAt":1749113570469,"lastUpdBy":"62bc5008d4958d013d97c7a6","metricValues":{"HeapMemoryUsage":0.06081453338265419,"CpuUsage":0.03936814433695751},"pingTime":1749113570460,"processId":"sam_iengine","singletonLock":"67574fba-c1fb-46c1-8842-82de3f2eec6a","stopping":false,"totalThread":1,"usedMemory":654311424,"userId":"62bc5008d4958d013d97c7a6","version":"-","workerDate":1749113570457,"workerType":"connector"}}
2025-06-06 17:20:03.862 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:20:08.890 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:20:13.922 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:20:18.964 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:20:24.001 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:20:29.054 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:20:34.083 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:20:39.111 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:20:44.149 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:20:49.176 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:20:54.230 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:20:59.353 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:21:04.400 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:21:09.452 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:21:14.502 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:21:19.541 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:21:24.589 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:21:29.686 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:21:34.803 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:21:39.831 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:21:44.863 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:21:49.895 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:21:54.924 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:21:59.947 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:22:04.989 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:22:10.050 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:22:15.088 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:22:20.123 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:22:25.162 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:22:30.205 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:22:35.234 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:22:40.269 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:22:45.305 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:22:50.361 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:22:55.404 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:23:00.457 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:23:05.486 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:23:10.508 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:23:15.558 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:23:20.592 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:23:25.621 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:23:30.645 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:23:35.677 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:23:40.710 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:23:45.745 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:23:50.769 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:23:55.799 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:24:00.838 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:24:05.865 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:24:10.903 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:24:15.946 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:24:20.966 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:24:26.016 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:24:31.092 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:24:36.133 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:24:41.155 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:24:46.182 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:24:51.210 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:24:56.244 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:25:00.026 [tm] [TaskAlarmScheduler-taskAgentAlarm] ERROR c.t.tm.schedule.TaskAlarmScheduler - taskAgentAlarm stopEgineMap data:{"sam_iengine":{"cpuLoad":0,"createAt":1749088057735,"createUser":"<EMAIL>","gitCommitId":"-","hostname":"Sam-macbook-m1x.local","id":{"date":**********000,"timestamp":**********},"lastUpdAt":1749113570469,"lastUpdBy":"62bc5008d4958d013d97c7a6","metricValues":{"HeapMemoryUsage":0.06081453338265419,"CpuUsage":0.03936814433695751},"pingTime":1749113570460,"processId":"sam_iengine","singletonLock":"67574fba-c1fb-46c1-8842-82de3f2eec6a","stopping":false,"totalThread":1,"usedMemory":654311424,"userId":"62bc5008d4958d013d97c7a6","version":"-","workerDate":1749113570457,"workerType":"connector"}}
2025-06-06 17:25:01.270 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:25:06.298 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:25:11.330 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:25:16.363 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:25:21.401 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:25:26.432 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:25:31.457 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:25:36.495 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:25:41.552 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:25:46.574 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:25:51.606 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:25:56.644 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:26:01.672 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:26:06.699 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:26:11.732 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:26:16.770 [tm] [taskSchedule-engineRestartNeedStartTask] INFO  c.t.tm.schedule.TaskRestartSchedule - engineRestartNeedStartTask task size 2
2025-06-06 17:26:20.394 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-06 17:26:20.396 [tm] [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-06 17:26:20.904 [tm] [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-06 17:26:20.922 [tm] [SpringApplicationShutdownHook] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2025-06-06 17:26:20.922 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-06 17:26:20.922 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-06 17:26:20.923 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-06 17:26:24.466 [tm] [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-06 17:26:24.511 [tm] [main] INFO  com.tapdata.tm.TMApplication - Starting TMApplication using Java 17.0.14 with PID 18047 (/Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes started by samuel in /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17)
2025-06-06 17:26:24.511 [tm] [main] INFO  com.tapdata.tm.TMApplication - The following 2 profiles are active: "idaas", "default"
2025-06-06 17:26:25.679 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 17:26:25.679 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-06 17:26:25.960 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 275 ms. Found 2 MongoDB repository interfaces.
2025-06-06 17:26:25.961 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 17:26:25.961 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-06 17:26:25.963 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 MongoDB repository interfaces.
2025-06-06 17:26:25.964 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 17:26:25.964 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-06 17:26:25.966 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 MongoDB repository interfaces.
2025-06-06 17:26:25.973 [tm] [main] INFO  c.t.t.w.c.WebSocketHandlerImportBeanRegistrar - The number of webSocketHandler beans successfully registered: 17
2025-06-06 17:26:26.342 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 17:26:26.343 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 17:26:26.395 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.task.repository.TaskDagCheckLogRepository; If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository
2025-06-06 17:26:26.396 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.Settings.repository.SettingsRepository; If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository
2025-06-06 17:26:26.396 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 52 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 17:26:26.398 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 17:26:26.399 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 17:26:26.445 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.task.repository.TaskDagCheckLogRepository; If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository
2025-06-06 17:26:26.446 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.Settings.repository.SettingsRepository; If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository
2025-06-06 17:26:26.446 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 46 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 17:26:27.196 [tm] [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 3000 (http)
2025-06-06 17:26:27.204 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - The ["http-nio-3000"] connector has been configured to support HTTP upgrade to [h2c]
2025-06-06 17:26:27.204 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-3000"]
2025-06-06 17:26:27.205 [tm] [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 17:26:27.205 [tm] [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.40]
2025-06-06 17:26:27.248 [tm] [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 17:26:27.248 [tm] [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2686 ms
2025-06-06 17:26:27.452 [tm] [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-data", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@3a2a56f6, com.mongodb.Jep395RecordCodecProvider@6a8da5c5, com.mongodb.KotlinCodecProvider@160e45c8]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 17:26:27.476 [tm] [cluster-ClusterId{value='6842b44399166222718b3fa3', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=22456875, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 17:26:20 CST 2025, lastUpdateTimeNanos=197615427393000}
2025-06-06 17:26:29.451 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@3a2a56f6, com.mongodb.Jep395RecordCodecProvider@6a8da5c5, com.mongodb.KotlinCodecProvider@160e45c8]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 17:26:29.458 [tm] [cluster-ClusterId{value='6842b44599166222718b3fa4', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=3175333, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 17:26:20 CST 2025, lastUpdateTimeNanos=197617406305666}
2025-06-06 17:26:29.863 [tm] [main] INFO  c.t.t.c.component.ProductComponent - Current product type settings is DAAS.
2025-06-06 17:26:29.876 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@3a2a56f6, com.mongodb.Jep395RecordCodecProvider@6a8da5c5, com.mongodb.KotlinCodecProvider@160e45c8]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 17:26:29.879 [tm] [cluster-ClusterId{value='6842b44599166222718b3fa5', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=3048542, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 17:26:20 CST 2025, lastUpdateTimeNanos=197617831246708}
2025-06-06 17:26:29.880 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.cluster - Waiting for server to become available for operation with ID 13. Remaining time: 29998 ms. Selector: ReadPreferenceServerSelector{readPreference=primary}, topology description: {type=UNKNOWN, servers=[{address=localhost:27017, type=UNKNOWN, state=CONNECTING}].
2025-06-06 17:26:31.943 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-06-06 17:26:31.953 [tm] [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-06 17:26:31.953 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v2.5.0 created.
2025-06-06 17:26:31.954 [tm] [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-06-06 17:26:31.954 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-06 17:26:31.954 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-06 17:26:31.954 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.5.0
2025-06-06 17:26:31.954 [tm] [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@19abe8f7
2025-06-06 17:26:32.479 [tm] [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name userServiceImpl
2025-06-06 17:26:33.139 [tm] [main] WARN  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Multiple @RequestMapping annotations found on public com.tapdata.tm.base.dto.ResponseMessage com.tapdata.tm.message.controller.MessageController.read(java.lang.String,java.lang.String) throws java.io.IOException, but only the first will be used: [@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={}, name="", params={}, path={}, produces={}, value={}), @org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name="", params={}, path={}, produces={}, value={})]
2025-06-06 17:26:34.204 [tm] [main] WARN  o.s.d.e.c.m.SimpleElasticsearchPersistentProperty - Unsupported type 'class java.lang.Object' for date property 'date'.
2025-06-06 17:26:34.205 [tm] [main] WARN  o.s.d.e.c.m.SimpleElasticsearchPersistentProperty - Unsupported type 'class java.lang.Object' for date property 'millis'.
2025-06-06 17:26:34.325 [tm] [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoints beneath base path '/actuator'
2025-06-06 17:26:34.464 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-3000"]
2025-06-06 17:26:34.471 [tm] [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 3000 (http) with context path '/'
2025-06-06 17:26:34.472 [tm] [main] INFO  o.s.s.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-06-06 17:26:34.473 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-06 17:26:34.498 [tm] [main] INFO  c.t.t.s.u.StateMachineProcessorManager - Urls path: /Users/<USER>/IdeaProjects/tapdata/tapdata-github/tapdata-v35-jdk17/tapdata/manager/tm/target/classes/com/tapdata/tm/statemachine/processor, protocol: file
2025-06-06 17:26:34.515 [tm] [main] INFO  com.tapdata.tm.TMApplication - Started TMApplication in 10.377 seconds (process running for 11.224)
2025-06-06 17:26:34.597 [tm] [main] INFO  com.tapdata.tm.init.PatchesRunner - Not have any DAAS patches, current version is 4.0-1
2025-06-06 17:26:34.653 [tm] [main] INFO  com.tapdata.tm.init.InitLogMap - 
  _______              _       _           _                    _             _                                _      _           _ 
 |__   __|            | |     | |         | |                  | |           | |                              | |    | |         | |
    | | __ _ _ __   __| | __ _| |_ __ _   | |_ _ __ ___     ___| |_ __ _ _ __| |_     ___ ___  _ __ ___  _ __ | | ___| |_ ___  __| |
    | |/ _` | '_ \ / _` |/ _` | __/ _` |  | __| '_ ` _ \   / __| __/ _` | '__| __|   / __/ _ \| '_ ` _ \| '_ \| |/ _ \ __/ _ \/ _` |
    | | (_| | |_) | (_| | (_| | || (_| |  | |_| | | | | |  \__ \ || (_| | |  | |_   | (_| (_) | | | | | | |_) | |  __/ ||  __/ (_| |
    |_|\__,_| .__/ \__,_|\__,_|\__\__,_|   \__|_| |_| |_|  |___/\__\__,_|_|   \__|   \___\___/|_| |_| |_| .__/|_|\___|\__\___|\__,_|
            | |                                                                                         | |                         
            |_|                                                                                         |_|                         
2025-06-06 17:26:34.654 [tm] [Thread-1] INFO  com.tapdata.tm.TMApplication - No need to start TCM
2025-06-06 17:26:34.660 [tm] [main] INFO  com.tapdata.tm.TMApplication - admin access code is 3324cfdf-7d3e-4792-bd32-571638d4562f
2025-06-06 17:26:34.783 [tm] [main] INFO  org.reflections.Reflections - Reflections took 111 ms to scan 19 urls, producing 62 keys and 1137 values
2025-06-06 17:26:34.867 [tm] [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 17:26:34.867 [tm] [RMI TCP Connection(3)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 17:26:34.869 [tm] [RMI TCP Connection(3)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-06 17:26:34.926 [tm] [main] INFO  org.reflections.Reflections - Reflections took 19 ms to scan 12 urls, producing 15 keys and 149 values
2025-06-06 17:26:34.930 [tm] [main] INFO  org.reflections.Reflections - Reflections took 2 ms to scan 1 urls, producing 10 keys and 24 values
2025-06-06 17:26:34.933 [tm] [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@3a2a56f6, com.mongodb.Jep395RecordCodecProvider@6a8da5c5, com.mongodb.KotlinCodecProvider@160e45c8]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-06-06 17:26:34.931 [tm] [elasticsearch-rest-client-0-thread-1] WARN  o.s.b.a.d.e.ElasticsearchReactiveHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.MonoCompletionStage] :
	reactor.core.publisher.Mono.fromFuture(Mono.java:629)
	org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClusterClient.health(ReactiveElasticsearchClusterClient.java:50)
Error has been observed at the following site(s):
	*__Mono.fromFuture ⇢ at org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClusterClient.health(ReactiveElasticsearchClusterClient.java:50)
	|_        Mono.map ⇢ at org.springframework.boot.actuate.data.elasticsearch.ElasticsearchReactiveHealthIndicator.doHealthCheck(ElasticsearchReactiveHealthIndicator.java:49)
Original Stack Trace:
		at java.base/sun.nio.ch.Net.pollConnect(Native Method)
		at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
		at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
		at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
		at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
		at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
		at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221)
		at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64)
		at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-06 17:26:34.937 [tm] [cluster-ClusterId{value='6842b44a99166222718b3fa6', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=4090583, minRoundTripTimeNanos=0, setName='rs1', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000019, setVersion=2, topologyVersion=TopologyVersion{processId=683923dafb36645c5d44053a, counter=6}, lastWriteDate=Fri Jun 06 17:26:34 CST 2025, lastUpdateTimeNanos=197622889951958}
2025-06-06 17:26:34.938 [tm] [main] INFO  org.mongodb.driver.cluster - Waiting for server to become available for operation with ID 85. Remaining time: 29999 ms. Selector: ReadPreferenceServerSelector{readPreference=primary}, topology description: {type=UNKNOWN, servers=[{address=localhost:27017, type=UNKNOWN, state=CONNECTING}].
2025-06-06 17:26:35.005 [tm] [main] INFO  org.reflections.Reflections - Reflections took 1 ms to scan 1 urls, producing 4 keys and 9 values
2025-06-06 17:26:35.065 [tm] [main] INFO  PDK - IPHolder [Server ip is [************, **********, 127.0.0.1] by ipPrefix null ethPrefix null]
2025-06-06 17:26:35.710 [tm] [Thread-3] INFO  c.t.t.t.service.impl.LdpServiceImpl - Supplementary ldp task exception
2025-06-06 17:27:04.984 [tm] [taskScheduler-10] INFO  o.s.s.a.AnnotationAsyncExecutionInterceptor - More than one TaskExecutor bean found within the context, and none is named 'taskExecutor'. Mark one of them as primary or name it 'taskExecutor' (possibly as an alias) in order to use it for async processing: [taskScheduler, NotificationExecutor]
2025-06-06 17:28:26.959 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-06 17:28:26.961 [tm] [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-06 17:28:27.468 [tm] [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-06 17:28:27.487 [tm] [SpringApplicationShutdownHook] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2025-06-06 17:28:27.487 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-06 17:28:27.487 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-06 17:28:27.488 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
